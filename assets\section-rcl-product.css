/* Global Section Styling */
.section-rcl-product {
  padding: 2rem 0;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', sans-serif;
}

.product__info-wrapper {
  padding: 1.5rem;
}

/* Product Title & Description */
.product__title h1 {
  font-size: clamp(1.75rem, 5vw, 2.5rem);
  font-weight: 700;
  margin-bottom: 1rem;
  line-height: 1.2;
  color: var(--product-primary);
  letter-spacing: -0.02em;
}

.product__description {
  color: var(--product-primary);
  font-size: 1rem;
  line-height: 1.6;
  margin-bottom: 2rem;
}

/* Price Styling */
.price {
  font-size: 1.5rem;
  font-weight: 600;
  margin: 1rem 0 1.5rem;
}

.price--on-sale .price__sale {
  color: #e53935;
}

.price__compare {
  text-decoration: line-through;
  font-size: 1rem;
  opacity: 0.6;
  margin-right: 0.75rem;
}

/* Metafields Styling - Modern Cards */
.product-metafields {
  margin: 2.5rem 0;
  width: 100%;
}

.product-metafields__columns {
  display: grid;
  grid-template-columns: 1fr;
  gap: 1.5rem;
  margin-bottom: 2rem;
}

@media screen and (min-width: 750px) {
  .product-metafields__columns {
    grid-template-columns: repeat(3, 1fr);
  }
}

.metafield-column {
  background-color: white;
  border-radius: var(--product-border-radius);
  padding: 2rem 1.5rem;
  transition: var(--product-transition);
  text-align: center;
  display: flex;
  flex-direction: column;
  @media (max-width: 768px) {
    flex-direction: row;
    align-items: center;
    gap: 1rem;
  }
  align-items: center;
  box-shadow: var(--product-shadow);
  border: 1px solid rgba(0, 0, 0, 0.05);
  position: relative;
  overflow: hidden;
}

.metafield-column:hover {
  transform: translateY(-8px);
  box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
}

.metafield-column:before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 4px;
  background: linear-gradient(90deg, var(--product-accent), var(--product-accent-light));
  transform: scaleX(0);
  transform-origin: left;
  transition: transform 0.4s ease;
}

.metafield-column:hover:before {
  transform: scaleX(1);
}

.metafield-column__icon {
  margin-bottom: 1.25rem;
  height: 60px;
  width: 60px;
  @media (max-width: 768px) {
    height: 40px;
    width: 40px;
  }
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: var(--product-bg-light);
  border-radius: 50%;
  transition: var(--product-transition);
}

.metafield-column:hover .metafield-column__icon {
  background-color: rgba(74, 144, 226, 0.1);
}

.metafield-column__icon img,
.metafield-column__icon svg {
  height: 28px;
  width: 28px;
  @media (max-width: 768px) {
    height: 20px;
    width: 20px;
  }
  transition: transform 0.3s ease;
}

@media (max-width: 768px) {
  .icon.icon-category {
    width: 40px;
  }
}

.metafield-column:hover .metafield-column__icon img,
.metafield-column:hover .metafield-column__icon svg {
  transform: scale(1.15);
}

.metafield-column:hover .metafield-column__icon svg path {
  stroke: var(--product-accent);
}

.metafield-column__title {
  font-weight: 600;
  font-size: 1.2rem;
  color: var(--product-primary);
  transition: var(--product-transition);
  letter-spacing: normal;
  @media (max-width: 768px) {
    max-width: 130px;
    width: 100%;
    text-align: left;
  }
}

.metafield-column:hover .metafield-column__title {
  color: var(--product-accent);
}

.metafield-column__content {
  font-size: 1.2rem;
  color: #666;
  line-height: 1.5;
}

/* Additional Specifications Accordion */
.product-additional-specs {
  margin-top: 1.5rem;
  border-radius: var(--product-border-radius);
  overflow: hidden;
  box-shadow: var(--product-shadow);
  margin-bottom: 2rem;
  background-color: white;
  border: 1px solid rgba(0, 0, 0, 0.05);
}

.additional-specs__header {
  background-color: white;
  padding: 1.5rem;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: space-between;
  transition: var(--product-transition);
}

.additional-specs__header:hover {
  background-color: var(--product-bg-light);
}

.additional-specs__title {
  font-weight: 600;
  font-size: 1.6rem;
  color: var(--product-primary);
  display: flex;
  align-items: center;
  letter-spacing: normal;
}

.additional-specs__icon {
  width: 20px;
  height: 20px;
  position: relative;
  margin-left: 10px;
  transition: var(--product-transition);
}

.additional-specs__icon:before,
.additional-specs__icon:after {
  content: "";
  position: absolute;
  background-color: var(--product-accent);
  transition: transform 0.3s ease;
}

.additional-specs__icon:before {
  top: 9px;
  left: 0;
  width: 20px;
  height: 2px;
}

.additional-specs__icon:after {
  top: 0;
  left: 9px;
  width: 2px;
  height: 20px;
}

.additional-specs--active .additional-specs__icon:after {
  transform: rotate(90deg);
}

.additional-specs__content {
  padding: 0;
  max-height: 0;
  overflow: hidden;
  transition: max-height 0.5s ease, padding 0.5s ease;
}

.additional-specs__content {
  color: var(--product-primary);
}

.additional-specs--active .additional-specs__content {
  padding: 0 1.5rem 1.5rem;
  max-height: 100%;
}

/* Features List Styling */
.product-features {
  margin-top: 1.5rem;
  border-radius: var(--product-border-radius);
  overflow: hidden;
  box-shadow: var(--product-shadow);
  background-color: white;
  border: 1px solid rgba(0, 0, 0, 0.05);
}

.features__header {
  background-color: white;
  padding: 1.5rem;
  display: flex;
  align-items: center;
  border-bottom: 1px solid rgba(0, 0, 0, 0.05);
}

.features__title {
  font-weight: 600;
  font-size: 1.6rem;
  color: var(--product-primary);
  position: relative;
  padding-left: 0.75rem;
  letter-spacing: normal;
}

.features__title:before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  height: 100%;
  width: 3px;
  background: linear-gradient(to bottom, var(--product-accent), var(--product-accent-light));
  border-radius: 3px;
}

.features__content {
  padding: 1rem 1.5rem 1.5rem;
}

.features-list {
  list-style: none;
  padding: 0;
  margin: 0;
  display: grid;
  grid-template-columns: 1fr;
  gap: 1rem;
}

@media screen and (min-width: 750px) {
  .features-list {
    grid-template-columns: repeat(2, 1fr);
  }
}

.feature-item {
  display: flex;
  align-items: flex-start;
  padding: 0.75rem;
  border-radius: 8px;
  transition: var(--product-transition);
  position: relative;
}

.feature-item:hover {
  background-color: var(--product-bg-light);
}

.feature-item__icon {
  flex-shrink: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 24px;
  height: 24px;
  margin-right: 12px;
  background-color: rgba(209, 176, 115, 0.1);
  border-radius: 50%;
}

.feature-item__icon svg {
  width: 14px;
  height: 14px;
}

.feature-item__text {
  font-size: 1.5rem;
  color: var(--product-primary);
  line-height: 1.5;
}

/* Enhanced Variant Picker Styles */
.product-form__input.product-form__input--dropdown {
  margin-bottom: 1.5rem;
}

.product-form__input.product-form__input--dropdown fieldset {
  border: none;
  padding: 0;
  margin: 0;
}

.select {
  position: relative;
}

.select__select {
  appearance: none;
  padding: 1rem 3rem 1rem 1.25rem;
  font-size: 1rem;
  border-radius: var(--product-border-radius);
  border: 1px solid rgba(0, 0, 0, 0.1);
  background-color: white;
  cursor: pointer;
  transition: var(--product-transition);
  width: 100%;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
}

.select__select:hover {
  border-color: var(--product-accent);
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.05);
}

.select__select:focus {
  border-color: var(--product-accent);
  box-shadow: 0 0 0 2px rgba(74, 144, 226, 0.2);
  outline: none;
}

.select .icon-caret {
  position: absolute;
  right: 1.25rem;
  top: 50%;
  transform: translateY(-50%);
  pointer-events: none;
  transition: transform 0.3s ease;
  color: var(--product-accent);
}

.select__select:focus + .icon-caret {
  transform: translateY(-50%) rotate(180deg);
}

/* Enhanced Variant Pills */
.product-form__input--radio {
  margin-bottom: 1.5rem;
}

.product-form__input--radio legend {
  margin-bottom: 1rem;
  font-weight: 600;
  color: var(--product-primary);
  font-size: 1rem;
}

.product-form__input--radio fieldset {
  border: none;
  padding: 0;
  margin: 0;
}

.form__label {
  font-size: 1rem;
  display: inline-block;
  margin-bottom: 0.75rem;
  color: var(--product-primary);
}

.form__radio-wrapper {
  display: inline-block;
  margin-right: 0.75rem;
  margin-bottom: 0.75rem;
  position: relative;
}

.form__radio {
  position: absolute;
  opacity: 0;
  width: 0;
  height: 0;
}

.form__radio + label {
  display: inline-block;
  padding: 0.6rem 1.25rem;
  border: 1px solid rgba(0, 0, 0, 0.1);
  border-radius: 30px;
  font-size: 1.2rem;
  cursor: pointer;
  transition: var(--product-transition);
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.03);
}

.form__radio:checked + label {
  border-color: var(--product-accent);
  background-color: var(--product-accent);
  color: white;
  box-shadow: 0 4px 10px rgba(74, 144, 226, 0.3);
}

.form__radio:not(:checked) + label:hover {
  border-color: var(--product-accent);
  background-color: rgba(74, 144, 226, 0.05);
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.05);
  transform: translateY(-2px);
}

.form__radio:focus-visible + label {
  box-shadow: 0 0 0 2px rgba(74, 144, 226, 0.3);
  outline: none;
}

/* Quantity Selector Enhancement */
.quantity-input {
  display: flex;
  align-items: center;
  border: 1px solid rgba(0, 0, 0, 0.1);
  border-radius: var(--product-border-radius);
  overflow: hidden;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
}

.quantity__button {
  background-color: white;
  border: none;
  padding: 0.75rem 1rem;
  cursor: pointer;
  transition: var(--product-transition);
  color: var(--product-accent);
}

.quantity__button:hover {
  background-color: rgba(74, 144, 226, 0.05);
}

.quantity__button svg {
  width: 16px;
  height: 16px;
  stroke: currentColor;
}

.quantity__input {
  text-align: center;
  border: none;
  width: 3.5rem;
  padding: 0.75rem 0;
  font-size: 1rem;
  -moz-appearance: textfield;
  background-color: white;
}

.quantity__input::-webkit-outer-spin-button,
.quantity__input::-webkit-inner-spin-button {
  -webkit-appearance: none;
  margin: 0;
}

/* Buy Button Enhancement */
.product-form__submit {
  width: 100%;
  background: linear-gradient(45deg, var(--product-primary), var(--product-primary-light));
  color: white;
  border: none;
  border-radius: var(--product-border-radius);
  padding: 1.1rem;
  font-size: 1.1rem;
  font-weight: 600;
  cursor: pointer;
  transition: var(--product-transition);
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
  position: relative;
  overflow: hidden;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.product-form__submit:hover {
  background: linear-gradient(45deg, var(--product-primary-light), var(--product-primary));
  transform: translateY(-3px);
  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.15);
}

.product-form__submit:active {
  transform: translateY(-1px);
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
}

.product-form__submit:after {
  content: "";
  position: absolute;
  width: 100%;
  height: 100%;
  top: 0;
  left: 0;
  pointer-events: none;
  background-image: radial-gradient(circle, #fff 10%, transparent 10.01%);
  background-repeat: no-repeat;
  background-position: 50%;
  transform: scale(10, 10);
  opacity: 0;
  transition: transform 0.5s, opacity 1s;
}

.product-form__submit:active:after {
  transform: scale(0, 0);
  opacity: 0.3;
  transition: 0s;
}

.product-form__buttons {
  margin-top: 1.5rem;
}

/* Dynamic Checkout Buttons */
.shopify-payment-button {
  margin-top: 1rem;
}

.shopify-payment-button__button {
  border-radius: var(--product-border-radius) !important;
  overflow: hidden;
  transition: var(--product-transition) !important;
}

/* Animation for product info elements */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.product__title, 
.price,
.product-form__input,
.product__description,
.product-form__buttons,
.product-metafields,
.product-additional-specs,
.product-features {
  animation: fadeInUp 0.6s cubic-bezier(0.25, 0.8, 0.25, 1) forwards;
  opacity: 0;
}

.product__title { animation-delay: 0.1s; }
.price { animation-delay: 0.2s; }
.product-form__input:nth-of-type(1) { animation-delay: 0.3s; }
.product-form__input:nth-of-type(2) { animation-delay: 0.4s; }
.product__description { animation-delay: 0.5s; }
.product-form__buttons { animation-delay: 0.6s; }
.product-metafields { animation-delay: 0.7s; }
.product-additional-specs { animation-delay: 0.8s; }
.product-features { animation-delay: 0.9s; }

/* JavaScript animation support classes */
.radio-ripple {
  position: absolute;
  top: 50%;
  left: 50%;
  width: 0;
  height: 0;
  border-radius: 50%;
  background: rgba(74, 144, 226, 0.3);
  transform: translate(-50%, -50%);
  animation: radio-ripple 0.6s cubic-bezier(0.25, 0.8, 0.25, 1);
  z-index: -1;
}

@keyframes radio-ripple {
  0% {
    width: 0;
    height: 0;
    opacity: 0.8;
  }
  100% {
    width: 250%;
    height: 250%;
    opacity: 0;
  }
}

.select--changed .icon-caret {
  animation: bounce 0.5s cubic-bezier(0.36, 0.07, 0.19, 0.97);
}

@keyframes bounce {
  0%, 100% { transform: translateY(-50%); }
  50% { transform: translateY(-70%); }
}

.button-pulse {
  animation: button-pulse 1.2s cubic-bezier(0.36, 0.07, 0.19, 0.97);
}

@keyframes button-pulse {
  0%, 100% { transform: scale(1); }
  50% { transform: scale(1.05); }
}

.icon-pulse {
  animation: icon-pulse 0.6s cubic-bezier(0.36, 0.07, 0.19, 0.97);
}

@keyframes icon-pulse {
  0%, 100% { transform: scale(1); }
  50% { transform: scale(1.2); }
}