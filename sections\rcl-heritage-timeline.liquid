{% schema %}
    {
      "name": "Heritage Timeline",
      "tag": "section",
      "class": "section",
      "settings": [
        {
          "type": "text",
          "id": "title",
          "label": "Section Title",
          "default": "Our Heritage"
        },
        {
          "type": "color",
          "id": "title_color",
          "label": "Title Color",
          "default": "#222228"
        },
        {
          "type": "richtext",
          "id": "intro_text",
          "label": "Introduction Text",
          "default": "<p>Discover the journey of Elf Decor™ since its creation in 2006, building on years of experience in manufacturing premium decorative coatings.</p>"
        },
        {
          "type": "color",
          "id": "text_color",
          "label": "Text Color",
          "default": "#222228"
        },
        {
          "type": "color",
          "id": "accent_color",
          "label": "Accent Color",
          "default": "#d1b073"
        },
        {
          "type": "color",
          "id": "background_color",
          "label": "Background Color",
          "default": "#ffffff"
        },
        {
          "type": "select",
          "id": "layout",
          "label": "Layout",
          "options": [
            {
              "value": "vertical",
              "label": "Vertical"
            },
            {
              "value": "horizontal",
              "label": "Horizontal"
            }
          ],
          "default": "vertical"
        }
      ],
      "blocks": [
        {
          "type": "timeline_item",
          "name": "Timeline Item",
          "settings": [
            {
              "type": "text",
              "id": "year",
              "label": "Year",
              "default": "2006"
            },
            {
              "type": "text",
              "id": "title",
              "label": "Title",
              "default": "Company Creation"
            },
            {
              "type": "richtext",
              "id": "description",
              "label": "Description",
              "default": "<p>Elf Decor™ was created after five years of experience in the production of building materials of Elf™.</p>"
            },
            {
              "type": "image_picker",
              "id": "image",
              "label": "Image"
            }
          ]
        }
      ],
      "presets": [
        {
          "name": "Heritage Timeline",
          "blocks": [
            {
              "type": "timeline_item"
            },
            {
              "type": "timeline_item"
            },
            {
              "type": "timeline_item"
            }
          ]
        }
      ]
    }
    {% endschema %}
    
    <div class="heritage-timeline" 
         style="background-color: {{ section.settings.background_color }}; 
                color: {{ section.settings.text_color }};"
         data-layout="{{ section.settings.layout }}">
      <div class="page-width">
        <div class="section-header text-center">
          <h2 class="section-title" style="color: {{ section.settings.title_color }};">
            {{ section.settings.title }}
          </h2>
          <div class="section-intro rte">
            {{ section.settings.intro_text }}
          </div>
        </div>
    
        <div class="timeline-container">
          {% for block in section.blocks %}
            {% if block.type == 'timeline_item' %}
              <div class="timeline-item" {{ block.shopify_attributes }}>
                <div class="timeline-marker" style="background-color: {{ section.settings.accent_color }};"></div>
                
                <div class="timeline-content">
                  <div class="timeline-year" style="color: {{ section.settings.accent_color }};">
                    {{ block.settings.year }}
                  </div>
                  
                  <h3 class="timeline-title">
                    {{ block.settings.title }}
                  </h3>
                  
                  <div class="timeline-description rte">
                    {{ block.settings.description }}
                  </div>
                  
                  {% if block.settings.image != blank %}
                    <div class="timeline-image">
                      {{ block.settings.image | image_url: width: 400 | image_tag:
                        loading: 'lazy',
                        widths: '275, 550, 710, 1420',
                        sizes: '(min-width: 750px) 350px, 50vw'
                      }}
                    </div>
                  {% endif %}
                </div>
              </div>
            {% endif %}
          {% endfor %}
        </div>
      </div>
    </div>
    
    <style>
      .heritage-timeline {
        padding: 60px 0;
      }
      
      .section-intro {
        max-width: 800px;
        margin: 0 auto;
        font-size: 1.1rem;
        line-height: 1.6;
      }
      
      /* Vertical Layout */
      .timeline-container {
        position: relative;
        max-width: 1200px;
        margin: 40px auto 0;
      }
      
      [data-layout="vertical"] .timeline-container::before {
        content: '';
        position: absolute;
        left: 50%;
        top: 0;
        bottom: 0;
        width: 2px;
        margin-left: -1px;
        background-color: rgba(0, 0, 0, 0.1);
      }
      
      [data-layout="vertical"] .timeline-item {
        position: relative;
        margin-bottom: 60px;
        display: flex;
        flex-direction: row;
        align-items: flex-start;
      }
      
      [data-layout="vertical"] .timeline-item:nth-child(even) {
        flex-direction: row-reverse;
      }
      
      [data-layout="vertical"] .timeline-marker {
        position: absolute;
        left: 50%;
        top: 15px;
        width: 20px;
        height: 20px;
        border-radius: 50%;
        transform: translateX(-50%);
        z-index: 1;
      }
      
      [data-layout="vertical"] .timeline-content {
        width: 45%;
        padding: 0 30px;
        box-sizing: border-box;
      }
      
      [data-layout="vertical"] .timeline-item:nth-child(odd) .timeline-content {
        text-align: right;
        margin-right: 5%;
      }
      
      [data-layout="vertical"] .timeline-item:nth-child(even) .timeline-content {
        text-align: left;
        margin-left: 5%;
      }
      
      [data-layout="vertical"] .timeline-year {
        display: inline-block;
        margin-bottom: 10px;
        font-size: 1.4rem;
        font-weight: 700;
      }
      
      [data-layout="vertical"] .timeline-title {
        margin-bottom: 15px;
        font-size: 1.6rem;
        line-height: 1.3;
      }
      
      [data-layout="vertical"] .timeline-description {
        margin-bottom: 20px;
        font-size: 1rem;
        line-height: 1.5;
      }
      
      [data-layout="vertical"] .timeline-image {
        border-radius: 4px;
        overflow: hidden;
      }
      
      [data-layout="vertical"] .timeline-image img {
        display: block;
        width: 100%;
        height: auto;
      }
      
      /* Horizontal Layout */
      [data-layout="horizontal"] .timeline-container {
        display: flex;
        flex-direction: row;
        justify-content: space-between;
        position: relative;
        padding-top: 40px;
      }
      
      [data-layout="horizontal"] .timeline-container::before {
        content: '';
        position: absolute;
        left: 0;
        right: 0;
        top: 20px;
        height: 2px;
        background-color: rgba(0, 0, 0, 0.1);
      }
      
      [data-layout="horizontal"] .timeline-item {
        position: relative;
        flex: 1;
        padding: 0 15px;
        text-align: center;
      }
      
      [data-layout="horizontal"] .timeline-marker {
        position: absolute;
        top: -40px;
        left: 50%;
        width: 20px;
        height: 20px;
        border-radius: 50%;
        transform: translateX(-50%);
        z-index: 1;
      }
      
      [data-layout="horizontal"] .timeline-content {
        margin-top: 30px;
      }
      
      [data-layout="horizontal"] .timeline-year {
        display: block;
        margin-bottom: 10px;
        font-size: 1.4rem;
        font-weight: 700;
      }
      
      [data-layout="horizontal"] .timeline-title {
        margin-bottom: 15px;
        font-size: 1.2rem;
        line-height: 1.3;
      }
      
      [data-layout="horizontal"] .timeline-description {
        margin-bottom: 20px;
        font-size: 0.9rem;
        line-height: 1.5;
      }
      
      [data-layout="horizontal"] .timeline-image {
        border-radius: 4px;
        overflow: hidden;
      }
      
      [data-layout="horizontal"] .timeline-image img {
        display: block;
        width: 100%;
        height: auto;
      }
      
      /* Responsive */
      @media screen and (max-width: 749px) {
        .section-title {
          font-size: calc(var(--font-heading-scale) * 1.8rem);
        }
        
        .section-intro {
          font-size: 1rem;
        }
        
        /* Vertical Layout - Mobile */
        [data-layout="vertical"] .timeline-container::before {
          left: 30px;
          margin-left: 0;
        }
        
        [data-layout="vertical"] .timeline-item,
        [data-layout="vertical"] .timeline-item:nth-child(even) {
          flex-direction: row;
        }
        
        [data-layout="vertical"] .timeline-marker {
          left: 30px;
          transform: translateX(-50%);
        }
        
        [data-layout="vertical"] .timeline-content,
        [data-layout="vertical"] .timeline-item:nth-child(odd) .timeline-content,
        [data-layout="vertical"] .timeline-item:nth-child(even) .timeline-content {
          width: 85%;
          margin-left: 60px;
          margin-right: 0;
          padding: 0;
          text-align: left;
        }
        
        /* Horizontal Layout - Mobile */
        [data-layout="horizontal"] .timeline-container {
          flex-direction: column;
          padding-top: 0;
        }
        
        [data-layout="horizontal"] .timeline-container::before {
          content: none;
        }
        
        [data-layout="horizontal"] .timeline-item {
          padding: 0;
          margin-bottom: 40px;
          text-align: left;
        }
        
        [data-layout="horizontal"] .timeline-marker {
          top: 15px;
          left: 10px;
          transform: none;
        }
        
        [data-layout="horizontal"] .timeline-content {
          margin-top: 0;
          margin-left: 40px;
          padding-left: 10px;
        }
      }
    </style>