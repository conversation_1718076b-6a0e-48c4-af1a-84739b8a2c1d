.text-center {
  text-align: center;
}

.section-title {
  font-size: calc(var(--font-heading-scale) * 2.5rem);
  margin-bottom: 20px;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.05em;
  position: relative;
  display: inline-block;
}

.section-title::after {
  content: '';
  position: absolute;
  bottom: -5px;
  left: 50%;
  width: 0;
  height: 2px;
  background-color: currentColor;
  transform: translateX(-50%);
  transition: width 0.6s cubic-bezier(0.22, 1, 0.36, 1);
}

.section-title:hover::after {
  width: 60%;
}

.section-description {
  max-width: 800px;
  margin: 0 auto;
  line-height: 1.6;
  margin-bottom: 60px;
}

.section-description,
.product-category-description {
  font-size: 1rem;
}

.rcl-button,
.product-category-button {
  font-size: 1.3rem;
  font-weight: 600;
  padding: 0.9rem 2rem;
  text-transform: uppercase;
}

.rcl-button::before,
.product-category-button::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(255, 255, 255, 0.1);
  transform: translateY(-100%);
  transition: transform 0.5s cubic-bezier(0.22, 1, 0.36, 1);
  z-index: -1;
}

.rcl-button:hover,
.product-category-button:hover {
  box-shadow: 0 10px 20px rgba(0, 0, 0, 0.15);
}

.rcl-button:hover::before,
.product-category-button:hover::before {
  transform: translateY(0);
}

.rcl-button:focus,
.product-category-button:focus {
  outline: none;
  box-shadow: 0 0 0 3px rgba(var(--color-foreground), 0.2);
}


.product-name,
.product-category-title {
  font-size: calc(var(--font-heading-scale) * 1.8rem)
}

.segment-title {
  font-size: 1.2rem;
}

.card__information .card__heading {
  font-weight: 600;
}