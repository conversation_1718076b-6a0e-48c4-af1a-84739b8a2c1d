{% schema %}
    {
      "name": "Premium Showcase",
      "tag": "section",
      "class": "section",
      "settings": [
        {
          "type": "text",
          "id": "title",
          "label": "Section Title",
          "default": "Premium Collections"
        },
        {
          "type": "color",
          "id": "title_color",
          "label": "Title Color",
          "default": "#d1b073"
        },
        {
          "type": "color",
          "id": "text_color",
          "label": "Text Color",
          "default": "#222228"
        },
        {
          "type": "color",
          "id": "background_color",
          "label": "Background Color",
          "default": "#ffffff"
        },
        {
          "type": "color",
          "id": "accent_color",
          "label": "Accent Color",
          "default": "#d1b073"
        },
        {
          "type": "select",
          "id": "layout",
          "label": "Layout Style",
          "options": [
            {
              "value": "tiles",
              "label": "Tiles"
            },
            {
              "value": "staggered",
              "label": "Staggered"
            }
          ],
          "default": "tiles"
        }
      ],
      "blocks": [
        {
          "type": "product_showcase",
          "name": "Product Showcase",
          "settings": [
            {
              "type": "image_picker",
              "id": "image",
              "label": "Product Image"
            },
            {
              "type": "text",
              "id": "product_name",
              "label": "Product Name",
              "default": "TRAVERTINO STYLE"
            },
            {
              "type": "text",
              "id": "product_tagline",
              "label": "Product Tagline",
              "default": "Timeless Elegance"
            },
            {
              "type": "url",
              "id": "link",
              "label": "Product Link"
            }
          ]
        }
      ],
      "presets": [
        {
          "name": "Premium Showcase",
          "blocks": [
            {
              "type": "product_showcase"
            },
            {
              "type": "product_showcase"
            },
            {
              "type": "product_showcase"
            },
            {
              "type": "product_showcase"
            }
          ]
        }
      ]
    }
    {% endschema %}
    
    <div class="premium-showcase-section" 
         style="background-color: {{ section.settings.background_color }}; 
                color: {{ section.settings.text_color }};"
         data-layout="{{ section.settings.layout }}">
      <div class="page-width">
        <h2 class="section-title" style="color: {{ section.settings.title_color }};">
          {{ section.settings.title }}
        </h2>
    
        <div class="premium-showcase-grid">
          {% for block in section.blocks %}
            {% if block.type == 'product_showcase' %}
              <div class="showcase-item" {{ block.shopify_attributes }}>
                <div class="showcase-item-inner">
                  {% if block.settings.image != blank %}
                    <div class="showcase-image">
                      {{ block.settings.image | image_url: width: 850 | image_tag:
                        loading: 'lazy',
                        widths: '365, 650, 850, 1200',
                        sizes: '(min-width: 990px) 33vw, (min-width: 750px) 50vw, 100vw'
                      }}
                    </div>
                  {% else %}
                    <div class="showcase-image placeholder">
                      {{ 'image' | placeholder_svg_tag: 'placeholder-svg' }}
                    </div>
                  {% endif %}
                  
                  <div class="showcase-content">
                    <div class="product-name">{{ block.settings.product_name }}</div>
                    <div class="product-tagline">{{ block.settings.product_tagline }}</div>
                    
                    {% if block.settings.link != blank %}
                      <a href="{{ block.settings.link }}" class="showcase-link" 
                         style="color: {{ section.settings.accent_color }};">
                        Discover
                        <span class="link-arrow">
                          <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <path d="M1 8H15" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                            <path d="M8 1L15 8L8 15" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                          </svg>
                        </span>
                      </a>
                    {% endif %}
                  </div>
                  
                  <div class="showcase-overlay"></div>
                </div>
              </div>
            {% endif %}
          {% endfor %}
        </div>
      </div>
    </div>
    
    <style>
      .premium-showcase-section {
        padding: 80px 0;
      }
      
      .section-title {
        margin-bottom: 60px;
        letter-spacing: 0.05em;
        text-transform: uppercase;
        text-align: center;
        line-height: 1.2;
      }
      
      /* Tiles Layout */
      [data-layout="tiles"] .premium-showcase-grid {
        display: grid;
        grid-template-columns: repeat(2, 1fr);
        gap: 20px;
      }
      
      /* Staggered Layout */
      [data-layout="staggered"] .premium-showcase-grid {
        display: grid;
        grid-template-columns: repeat(12, 1fr);
        grid-auto-rows: minmax(250px, auto);
        gap: 20px;
      }
      
      [data-layout="staggered"] .showcase-item:nth-child(4n+1) {
        grid-column: span 7;
        grid-row: span 1;
      }
      
      [data-layout="staggered"] .showcase-item:nth-child(4n+2) {
        grid-column: span 5;
        grid-row: span 1;
      }
      
      [data-layout="staggered"] .showcase-item:nth-child(4n+3) {
        grid-column: span 5;
        grid-row: span 1;
      }
      
      [data-layout="staggered"] .showcase-item:nth-child(4n+4) {
        grid-column: span 7;
        grid-row: span 1;
      }
      
      /* Showcase Item */
      .showcase-item {
        position: relative;
        overflow: hidden;
        height: 100%;
      }
      
      .showcase-item-inner {
        height: 100%;
        position: relative;
        overflow: hidden;
        border-radius: 10px;
        box-shadow: 0 10px 30px rgba(0, 0, 0, 0.08);
      }
      
      .showcase-image {
        height: 100%;
        width: 100%;
      }
      
      .showcase-image img,
      .showcase-image .placeholder-svg {
        width: 100%;
        height: 100%;
        object-fit: cover;
        display: block;
        transition: transform 0.8s ease;
      }
      
      .showcase-item:hover .showcase-image img {
        transform: scale(1.05);
      }
      
      .showcase-overlay {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: linear-gradient(to top, rgba(0, 0, 0, 0.7) 0%, rgba(0, 0, 0, 0) 50%);
        z-index: 1;
      }
      
      .showcase-content {
        position: absolute;
        bottom: 0;
        left: 0;
        width: 100%;
        padding: 30px;
        z-index: 2;
        color: #ffffff;
      }
      
      .product-name {
        font-weight: 700;
        margin-bottom: 5px;
        letter-spacing: 0.05em;
        text-transform: uppercase;
      }
      
      .product-tagline {
        font-size: 1.1rem;
        margin-bottom: 15px;
        font-weight: 300;
        opacity: 0.8;
      }
      
      .showcase-link {
        display: inline-flex;
        align-items: center;
        font-size: 1rem;
        font-weight: 600;
        text-decoration: none;
        transition: transform 0.3s ease;
      }
      
      .showcase-link:hover {
        transform: translateX(5px);
      }
      
      .link-arrow {
        margin-left: 8px;
        display: inline-flex;
      }
      
      /* Responsive */
      @media screen and (max-width: 989px) {
        .section-title {
          font-size: calc(var(--font-heading-scale) * 2rem);
        }
        
        [data-layout="staggered"] .premium-showcase-grid {
          grid-template-columns: 1fr;
        }
        
        [data-layout="staggered"] .showcase-item:nth-child(n) {
          grid-column: span 1;
        }
      }
      
      @media screen and (max-width: 749px) {
        .premium-showcase-section {
          padding: 60px 0;
        }
        
        .section-title {
          font-size: calc(var(--font-heading-scale) * 1.8rem);
          margin-bottom: 40px;
        }
        
        [data-layout="tiles"] .premium-showcase-grid {
          grid-template-columns: 1fr;
        }
        
        .product-name {
          font-size: 1.5rem;
        }
        
        .product-tagline {
          font-size: 1rem;
        }
      }
    </style>