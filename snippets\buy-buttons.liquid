{% comment %}
  Renders product buy-buttons.
  Accepts:
  - product: {Object} product object.
  - block: {Object} passing the block information.
  - product_form_id: {String} product form id.
  - section_id: {String} id of section to which this snippet belongs.
  - show_pickup_availability: {<PERSON><PERSON><PERSON>} for the pickup availability. If true the pickup availability is rendered, false - not rendered (optional).

  Usage:
  {% render 'buy-buttons', block: block, product: product, product_form_id: product_form_id, section_id: section.id, show_pickup_availability: true %}
{% endcomment %}
<div {{ block.shopify_attributes }}>
  {%- if product != blank -%}
    {%- liquid
      assign gift_card_recipient_feature_active = false
      if block.settings.show_gift_card_recipient and product.gift_card?
        assign gift_card_recipient_feature_active = true
      endif

      assign show_dynamic_checkout = false
      if block.settings.show_dynamic_checkout and gift_card_recipient_feature_active == false
        assign show_dynamic_checkout = true
      endif
    -%}

    <product-form
      class="product-form"
      data-hide-errors="{{ gift_card_recipient_feature_active }}"
      data-section-id="{{ section.id }}"
    >
      <div class="product-form__error-message-wrapper" role="alert" hidden>
        <svg
          aria-hidden="true"
          focusable="false"
          class="icon icon-error"
          viewBox="0 0 13 13"
        >
          <circle cx="6.5" cy="6.50049" r="5.5" stroke="white" stroke-width="2"/>
          <circle cx="6.5" cy="6.5" r="5.5" fill="#EB001B" stroke="#EB001B" stroke-width="0.7"/>
          <path d="M5.87413 3.52832L5.97439 7.57216H7.02713L7.12739 3.52832H5.87413ZM6.50076 9.66091C6.88091 9.66091 7.18169 9.37267 7.18169 9.00504C7.18169 8.63742 6.88091 8.34917 6.50076 8.34917C6.12061 8.34917 5.81982 8.63742 5.81982 9.00504C5.81982 9.37267 6.12061 9.66091 6.50076 9.66091Z" fill="white"/>
          <path d="M5.87413 3.17832H5.51535L5.52424 3.537L5.6245 7.58083L5.63296 7.92216H5.97439H7.02713H7.36856L7.37702 7.58083L7.47728 3.537L7.48617 3.17832H7.12739H5.87413ZM6.50076 10.0109C7.06121 10.0109 7.5317 9.57872 7.5317 9.00504C7.5317 8.43137 7.06121 7.99918 6.50076 7.99918C5.94031 7.99918 5.46982 8.43137 5.46982 9.00504C5.46982 9.57872 5.94031 10.0109 6.50076 10.0109Z" fill="white" stroke="#EB001B" stroke-width="0.7">
        </svg>
        <span class="product-form__error-message"></span>
      </div>

      {%- form 'product',
        product,
        id: product_form_id,
        class: 'form',
        novalidate: 'novalidate',
        data-type: 'add-to-cart-form'
      -%}
        <input
          type="hidden"
          name="id"
          value="{{ product.selected_or_first_available_variant.id }}"
          {% if product.selected_or_first_available_variant.available == false or quantity_rule_soldout %}
            disabled
          {% endif %}
          class="product-variant-id"
        >

        <script type="application/json" data-variant-id="{{ product.selected_or_first_available_variant.id }}">
          {
            "id": {{ product.selected_or_first_available_variant.id }},
            "title": {{ product.selected_or_first_available_variant.title | json }},
            "inventory_management": {{ product.selected_or_first_available_variant.inventory_management | json }},
            "inventory_policy": {{ product.selected_or_first_available_variant.inventory_policy | json }},
            "inventory_quantity": {{ product.selected_or_first_available_variant.inventory_quantity | json }}
          }
        </script>

        {%- if gift_card_recipient_feature_active -%}
          {%- render 'gift-card-recipient-form', product: product, form: form, section: section -%}
        {%- endif -%}

        <div class="product-form__buttons">
          {%- liquid
            assign check_against_inventory = true
            if product.selected_or_first_available_variant.inventory_management != 'shopify' or product.selected_or_first_available_variant.inventory_policy == 'continue'
              assign check_against_inventory = false
            endif
            if product.selected_or_first_available_variant.quantity_rule.min > product.selected_or_first_available_variant.inventory_quantity and check_against_inventory
              assign quantity_rule_soldout = true
            endif
          -%}
          <button
            id="ProductSubmitButton-{{ section_id }}"
            type="submit"
            name="add"
            class="product-form__submit button button--full-width {% if show_dynamic_checkout %}button--secondary{% else %}button--primary{% endif %}"
            data-variant-id="{{ product.selected_or_first_available_variant.id }}"
            data-inventory-management="{{ product.selected_or_first_available_variant.inventory_management }}"
            data-inventory-policy="{{ product.selected_or_first_available_variant.inventory_policy }}"
            data-inventory-quantity="{{ product.selected_or_first_available_variant.inventory_quantity }}"
            {% if product.selected_or_first_available_variant.available == false or quantity_rule_soldout %}
              disabled
            {% endif %}
          >
            <span>
              {%- if product.selected_or_first_available_variant.available == false or quantity_rule_soldout -%}
                {{ 'products.product.sold_out' | t }}
              {%- else -%}
                {{ 'products.product.add_to_cart' | t }}
              {%- endif -%}
            </span>
            {%- render 'loading-spinner' -%}
          </button>
          {%- if show_dynamic_checkout -%}
            {{ form | payment_button }}
          {%- endif -%}
        </div>
      {%- endform -%}
    </product-form>
  {%- else -%}
    <div class="product-form">
      <div class="product-form__buttons form">
        <button
          type="submit"
          name="add"
          class="product-form__submit button button--full-width button--primary"
          disabled
        >
          {{ 'products.product.sold_out' | t }}
        </button>
      </div>
    </div>
  {%- endif -%}

  {%- if show_pickup_availability -%}
    {{ 'component-pickup-availability.css' | asset_url | stylesheet_tag }}

    {%- assign pick_up_availabilities = product.selected_or_first_available_variant.store_availabilities
      | where: 'pick_up_enabled', true
    -%}

    <pickup-availability
      class="product__pickup-availabilities no-js-hidden quick-add-hidden"
      {% if product.selected_or_first_available_variant.available and pick_up_availabilities.size > 0 %}
        available
      {% endif %}
      data-root-url="{{ routes.root_url }}"
      data-variant-id="{{ product.selected_or_first_available_variant.id }}"
      data-has-only-default-variant="{{ product.has_only_default_variant }}"
      data-product-page-color-scheme="gradient color-{{ section.settings.color_scheme }}"
    >
      <template>
        <pickup-availability-preview class="pickup-availability-preview">
          {% render 'icon-unavailable' %}
          <div class="pickup-availability-info">
            <p class="caption-large">{{ 'products.product.pickup_availability.unavailable' | t }}</p>
            <button class="pickup-availability-button link link--text underlined-link">
              {{ 'products.product.pickup_availability.refresh' | t }}
            </button>
          </div>
        </pickup-availability-preview>
      </template>
    </pickup-availability>

    <script src="{{ 'pickup-availability.js' | asset_url }}" defer="defer"></script>
  {%- endif -%}
</div>
