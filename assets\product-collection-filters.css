
.filters-container {
  margin-bottom: 2rem;
  padding-bottom: 1rem;
  border-bottom: 1px solid #e8e8e8;
}

.filters-toolbar {
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
}

.filters-toolbar__left {
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  gap: 1rem;
}

.filters-toolbar__right {
  display: flex;
  align-items: center;
}

.filter-label {
  font-weight: 500;
  margin-right: 0.5rem;
}

.filter-dropdown {
  position: relative;
  min-width: 180px;
}

.filter-dropdown-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.6rem 1rem;
  border: 1px solid #e1e1e1;
  border-radius: 4px;
  background-color: #fff;
  cursor: pointer;
  transition: all 0.2s ease;
}

.filter-dropdown-header:hover {
  background-color: #f9f9f9;
}

.filter-dropdown-arrow {
  width: 10px;
  height: 10px;
  transition: transform 0.3s ease;
}

.filter-dropdown-header.active .filter-dropdown-arrow {
  transform: rotate(180deg);
}

.filter-dropdown-content {
  position: absolute;
  top: 100%;
  left: 0;
  width: 100%;
  max-height: 300px;
  overflow-y: auto;
  background-color: #fff;
  border: 1px solid #e1e1e1;
  border-top: none;
  border-radius: 0 0 4px 4px;
  z-index: 10;
  display: none;
  padding: 0.5rem 0;
  margin-top: -1px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
}

.filter-dropdown-content.active {
  display: block;
}

 .filter-option {
   padding: 0.5rem 1rem;
   display: flex;
   align-items: center;
   cursor: pointer;
   transition: background-color 0.2s ease;
 }

 .filter-option:hover {
     background-color: #f9f9f9;
 }

.filter-option input[type="checkbox"] {
  margin-right: 0.5rem;
  flex-shrink: 0;
}

 .filter-option.disabled {
   opacity: 0.4;
   cursor: not-allowed;
   background-color: #f8f8f8;
   text-decoration: line-through;
   color: #999;
 }

  .filter-option.disabled label {
     cursor: not-allowed;
  }

  .filter-option.disabled input[type="checkbox"] {
     cursor: not-allowed;
  }

.filter-option-label {
  text-transform: capitalize;
}

.selected-filters {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
  margin-top: 1rem;
}

.selected-filter-tag {
  background-color: #f5f5f5;
  border-radius: 4px;
  padding: 0.3rem 0.6rem;
  display: flex;
  align-items: center;
  text-transform: capitalize;
}

.selected-filter-tag .remove-filter {
  margin-left: 0.5rem;
  cursor: pointer;
}

.clear-filters {
  text-decoration: underline;
  color: #555;
  cursor: pointer;
  margin-top: 1rem;
  display: none;
}

.clear-filters.active {
  display: block;
}

.sort-dropdown-wrapper {
  position: relative;
  min-width: 180px;
}

.sort-select {
  appearance: none;
  -webkit-appearance: none;
  -moz-appearance: none;
  padding: 0.6rem 2.5rem 0.6rem 1rem;
  border: 1px solid #e1e1e1;
  border-radius: 4px;
  background-color: #fff;
  line-height: normal;
  width: 100%;
  height: 45px;
  font-size: 1.5rem;
  color: rgba(var(--color-foreground), 0.75);
  box-sizing: border-box;
  transition: background-color 0.2s ease, border-color 0.2s ease;
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='10' height='10' viewBox='0 0 10 10' fill='none'%3E%3Cpath d='M1 3.5L5 7.5L9 3.5' stroke='%23333333' stroke-width='1.5' stroke-linecap='round' stroke-linejoin='round'/%3E%3C/svg%3E");
  background-repeat: no-repeat;
  background-position: right 1rem center;
  background-size: 10px;
}

.sort-select:focus {
  outline: none;
  border-color: #c4c4c4;
}

@media screen and (max-width: 749px) {
  .filters-toolbar {
    flex-direction: column;
    align-items: flex-start;
    gap: 1rem;
  }

  .filters-toolbar__left {
    flex-direction: column;
    width: 100%;
    align-items: flex-start;
  }

  .filters-toolbar__right {
    width: 100%;
    justify-content: flex-end;
  }

  .filter-dropdown {
    width: 100%;
  }

  .custom-sort-wrapper {
    width: 100%;
    justify-content: space-between;
  }

  .custom-sort-select {
    flex-grow: 1;
  }
}

.sort-select {
  appearance: none;
  -webkit-appearance: none;
  -moz-appearance: none;
  padding: 0.6rem 2.5rem 0.6rem 1rem;
  border: 1px solid #e1e1e1;
  border-radius: 4px;
  background-color: #fff;
  line-height: normal;
  font-family: var(--font-body-family);
  width: 100%;
  height: 45px;
  font-size: 1.5rem;
  color: rgba(var(--color-foreground), 0.75);
  box-sizing: border-box;
  transition: background-color 0.2s ease, border-color 0.2s ease;
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='10' height='10' viewBox='0 0 10 10' fill='none'%3E%3Cpath d='M1 3.5L5 7.5L9 3.5' stroke='%23333333' stroke-width='1.5' stroke-linecap='round' stroke-linejoin='round'/%3E%3C/svg%3E");
  background-repeat: no-repeat;
  background-position: right 1rem center;
  background-size: 10px;
}

.sort-select:hover {
    background-color: #f9f9f9;
    border-color: #d1d1d1;
}

.sort-select:focus {
    outline: none;
    border-color: #a1a1a1;
    box-shadow: 0 0 0 2px rgba(0, 0, 0, 0.05);
}

@media screen and (max-width: 749px) {
  .filters-toolbar__right {
      gap: 0.5rem;
  }
    .sort-dropdown-wrapper {
        width: auto;
        flex-grow: 1;
        min-width: 150px;
    }
}