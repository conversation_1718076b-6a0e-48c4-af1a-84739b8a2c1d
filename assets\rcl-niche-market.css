.market-presence-container {
  width: 100%;
  max-width: 1300px;
  margin: 0 auto;
  padding: 0 1.5rem;
}

@media screen and (min-width: 750px) {
  .market-presence-container {
    padding: 0 3rem;
  }
}

.market-presence-content {
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  gap: 4rem;
  position: relative;
}

/* Equal height styling */
.market-presence-content.equal-height {
  align-items: stretch;
}

.market-presence-content.equal-height .market-presence-media,
.market-presence-content.equal-height .market-presence-text {
  display: flex;
  flex-direction: column;
}

/* Ensure single image has proper equal height */
.market-presence-content.equal-height .market-presence-media > div:not(.image-collage) {
  flex-grow: 1;
  height: 100%;
  display: flex;
  flex-direction: column;
}

.market-presence-content.equal-height .market-presence-media .main-image {
  height: 100%;
  object-fit: cover;
}

/* Fix for collage layout with equal height */
.market-presence-content.equal-height .image-collage {
  height: 100%;
  display: grid;
  grid-template-columns: 1fr 1fr;
  grid-template-rows: 1fr 1fr;
}

.market-presence-content.image_left {
  flex-direction: row;
}

.market-presence-content.image_right {
  flex-direction: row-reverse;
}

/* Media Container Styles */
.market-presence-media {
  flex: 1;
  min-width: 280px;
  position: relative;
}

/* Single Image Styles */
.market-presence-media img {
  width: 100%;
  height: auto;
  display: block;
  object-fit: cover;
}

.market-presence-media.adapt {
  height: auto;
}

.market-presence-media.portrait {
  aspect-ratio: 3/4;
}

.market-presence-media.square {
  aspect-ratio: 1/1;
}

.market-presence-media.landscape {
  aspect-ratio: 4/3;
}

.market-presence-media .main-image {
  width: 100%;
  display: block;
  position: relative;
  z-index: 1;
}

.market-presence-media.default .main-image {
  border-radius: 8px;
}

.market-presence-media.shadow .main-image {
  border-radius: 8px;
  box-shadow: 0 15px 30px rgba(0,0,0,0.15);
}

.market-presence-media.border .main-image {
  border-radius: 8px;
  border: 5px solid #fff;
  box-shadow: 0 5px 15px rgba(0,0,0,0.08);
}

.market-presence-media.rounded .main-image {
  border-radius: 16px;
}

.market-presence-media.floating .main-image {
  border-radius: 8px;
  box-shadow: 0 20px 40px rgba(0,0,0,0.1);
  transform: translateY(-10px);
  transition: transform 0.5s ease, box-shadow 0.5s ease;
}

.market-presence-media.floating:hover .main-image {
  transform: translateY(-20px);
  box-shadow: 0 25px 50px rgba(0,0,0,0.15);
}

/* Enhanced Responsive Stacked Images */
.stacked-images-container {
position: relative;
width: 100%;
max-width: 100%;
/* Fluid margin calculation based on viewport width */
margin-top: calc(1rem + 1vw);
transition: transform 0.3s ease-in-out;
}

.stacked-images-container:hover {
transform: scale(1.02);
}

.stacked-image {
border-radius: clamp(4px, 0.8vw, 12px);
box-shadow: 0 calc(5px + 0.5vw) calc(15px + 1.5vw) rgba(0, 0, 0, 0.15);
position: relative;
z-index: 1;
width: 100%;
transition: transform 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
}

.stacked-images-container:hover .stacked-image {
transform: translateY(-5px);
}

.secondary-image-container {
position: absolute;
/* Fluid width calculation */
width: calc(40% + 15%);
height: auto;
/* Dynamic positioning */
bottom: calc(-10% - 2%);
right: calc(-6% - 2%);
z-index: 2;
opacity: 0.92;
transition: all 0.5s cubic-bezier(0.175, 0.885, 0.32, 1.275);
}

.stacked-images-container:hover .secondary-image-container {
transform: translate(-8px, -8px) rotate(2deg);
opacity: 1;
box-shadow: 0 15px 30px rgba(0, 0, 0, 0.2);
}

.tertiary-image-container {
position: absolute;
/* Fluid width calculation */
width: calc(25% + 10%);
height: auto;
/* Dynamic positioning */
top: calc(-8% - 2%);
left: calc(-3% - 2%);
z-index: 3;
opacity: 0.92;
transition: all 0.5s cubic-bezier(0.175, 0.885, 0.32, 1.275);
}

.stacked-images-container:hover .tertiary-image-container {
transform: translate(8px, 8px) rotate(-2deg);
opacity: 1;
box-shadow: 0 15px 30px rgba(0, 0, 0, 0.2);
}

.secondary-image,
.tertiary-image {
border-radius: clamp(4px, 0.8vw, 12px);
box-shadow: 0 calc(5px + 0.5vw) calc(10px + 1vw) rgba(0, 0, 0, 0.12);
width: 100%;
height: auto;
border: clamp(3px, 0.4vw, 6px) solid white;
transition: transform 0.3s ease;
}

/* Responsive adjustments - more fluid approach */
@media screen and (min-width: 1401px) {
.market-presence-content {
  gap: calc(2rem + 2vw);
}

.stacked-images-container {
  margin-top: calc(1rem + 1.5vw);
}
}

@media screen and (max-width: 1400px) and (min-width: 1201px) {
.market-presence-content {
  gap: calc(2rem + 1.5vw);
}

.stacked-images-container {
  margin-top: calc(1rem + 1vw);
}

.tertiary-image-container {
  width: calc(25% + 9%);
  top: calc(-8% - 1%);
}
}

@media screen and (max-width: 1200px) and (min-width: 993px) {
.market-presence-content {
  gap: calc(1.5rem + 1.5vw);
}

.stacked-images-container {
  margin-top: calc(1rem + 1.5vw);
  max-width: 95%;
  margin-left: auto;
  margin-right: auto;
}

.secondary-image-container {
  width: calc(35% + 15%);
  right: calc(-4% - 1%);
}

.tertiary-image-container {
  width: calc(22% + 10%);
  top: calc(-7% - 1%);
}
}

@media screen and (max-width: 992px) and (min-width: 768px) {
.market-presence-content {
  flex-direction: column !important;
  gap: calc(2rem + 2vw);
}

.stacked-images-container {
  max-width: 85%;
  margin: 0 auto;
}

.secondary-image-container {
  width: calc(35% + 10%);
  bottom: calc(-8% - 0%);
  right: calc(-3% - 1%);
}

.tertiary-image-container {
  width: calc(20% + 10%);
  top: calc(-9% - 1%);
  left: calc(-3% - 1%);
}
}

@media screen and (max-width: 767px) and (min-width: 481px) {
.market-presence-content {
  gap: calc(1.5rem + 1.5vw);
}

.stacked-images-container {
  max-width: 90%;
  margin: 0 auto;
}

.secondary-image-container {
  width: calc(30% + 10%);
  bottom: calc(-6% - 0%);
  right: calc(-2% - 1%);
}

.tertiary-image-container {
  width: calc(18% + 10%);
  top: calc(-7% - 1%);
  left: calc(-2% - 1%);
}

/* Enhanced pop effect for smaller screens */
.stacked-images-container:hover .secondary-image-container {
  transform: translate(-6px, -6px) rotate(1.5deg);
}

.stacked-images-container:hover .tertiary-image-container {
  transform: translate(6px, 6px) rotate(-1.5deg);
}
}

@media screen and (max-width: 480px) {
.market-presence-content {
  gap: calc(1rem + 1vw);
}

.stacked-images-container {
  max-width: 95%;
  margin: 0 auto;
}

.secondary-image-container {
  width: calc(28% + 10%);
  bottom: calc(-5% - 0%);
  right: calc(-1% - 1%);
}

.tertiary-image-container {
  width: calc(15% + 10%);
  top: calc(-6% - 1%);
  left: calc(-1% - 1%);
}

/* Adjusted pop effect for smallest screens */
.stacked-images-container:hover .secondary-image-container {
  transform: translate(-4px, -4px) rotate(1deg);
}

.stacked-images-container:hover .tertiary-image-container {
  transform: translate(4px, 4px) rotate(-1deg);
}

.secondary-image,
.tertiary-image {
  border: clamp(2px, 0.3vw, 4px) solid white;
}
}

/* Image Collage Styles */
.image-collage {
  display: grid;
  grid-template-columns: 1fr 1fr;
  grid-template-rows: auto auto;
  gap: 15px;
}

.collage-image {
  border-radius: 8px;
  overflow: hidden;
  height: 100%;
}

.collage-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.5s ease;
}

.collage-image:hover img {
  transform: scale(1.05);
}

.collage-main {
  grid-column: 1 / 2;
  grid-row: 1 / 3;
  height: 100%;
}

.collage-secondary {
  grid-column: 2 / 3;
  grid-row: 1 / 2;
}

.collage-tertiary {
  grid-column: 2 / 3;
  grid-row: 2 / 3;
}

/* Video Styles */
.video-container {
  position: relative;
  width: 100%;
  border-radius: 8px;
  overflow: hidden;
}

.video-container.shadow {
  box-shadow: 0 15px 30px rgba(0,0,0,0.15);
}

.video-container iframe {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  border: 0;
}

.video-cover {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-size: cover;
  background-position: center;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  z-index: 3;
  transition: opacity 0.3s ease;
}

.video-cover.hidden {
  opacity: 0;
  pointer-events: none;
}

.play-button {
  width: 80px;
  height: 80px;
  background-color: rgba(209,176,115,0.9);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: transform 0.3s ease, background-color 0.3s ease;
}

.play-button:hover {
  transform: scale(1.1);
  background-color: #d1b073;
}

.play-button svg {
  width: 30px;
  height: 30px;
  fill: white;
  margin-left: 5px; /* Slight adjustment for play icon */
}

.video-aspect-ratio {
  padding-bottom: 56.25%; /* 16:9 aspect ratio */
  position: relative;
}

/* Self-hosted Video Styles */
.self-hosted-video-container {
  position: relative;
  width: 100%;
  border-radius: 8px;
  overflow: hidden;
}

.self-hosted-video-container.shadow {
  box-shadow: 0 15px 30px rgba(0,0,0,0.15);
}

.self-hosted-video-container video {
  width: 100%;
  height: auto;
  display: block;
  border-radius: 8px;
}

.self-hosted-video-container.dark video {
  --video-controls-color: #222228;
}

.self-hosted-video-container.light video {
  --video-controls-color: #ffffff;
}

/* Content Styles */
.market-presence-text {
  flex: 1;
  min-width: 280px;
}

.market-presence-main-text {
  line-height: 1.7;
  color: #222228;
  margin-bottom: 2.5rem;
}

.market-segments {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
  gap: 2rem;
}

.market-segment-item {
  background-color: #f9f7f2;
  border-radius: 12px;
  padding: 2rem;
  transition: all 0.3s ease;
  height: 100%;
  box-shadow: 0 5px 15px rgba(0,0,0,0.03);
}

.market-segment-item:hover {
  transform: translateY(-10px);
  box-shadow: 0 15px 30px rgba(209,176,115,0.2);
}

.segment-icon {
  width: 60px;
  height: 60px;
  background-color: #d1b073;
  border-radius: 50%;
  display: flex!important;
  align-items: center;
  justify-content: center;
  margin-bottom: 1.5rem;
  transition: transform 0.3s ease;
}

.market-segment-item:hover .segment-icon {
  transform: scale(1.1);
}

.segment-icon svg {
  width: 30px;
  height: 30px;
  fill: #fff;
}

.segment-title {
  font-weight: 600;
  margin-bottom: 1rem;
  color: #222228;
}

.segment-description {
  line-height: 1.6;
  color: #555;
}

/* Animation Styles */
.animate {
  opacity: 0;
  transition: opacity 1s ease, transform 1s ease;
}

.animate.fade-in {
  opacity: 1;
}

.animate.slide-up {
  transform: translateY(30px);
}

.animate.slide-up.active {
  transform: translateY(0);
  opacity: 1;
}

.animate.slide-right {
  transform: translateX(-30px);
}

.animate.slide-right.active {
  transform: translateX(0);
  opacity: 1;
}

.animate.slide-left {
  transform: translateX(30px);
}

.animate.slide-left.active {
  transform: translateX(0);
  opacity: 1;
}

/* Preserve icon styles */
.icon-building {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' width='24' height='24'%3E%3Cpath fill='white' d='M19 10h1a1 1 0 0 1 1 1v10a1 1 0 0 1-1 1H4a1 1 0 0 1-1-1V11a1 1 0 0 1 1-1h1V8a7 7 0 0 1 14 0v2zm-7 8a2 2 0 1 0 0-4 2 2 0 0 0 0 4z'/%3E%3C/svg%3E");
  background-repeat: no-repeat;
  background-position: center;
}

.icon-home {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' width='24' height='24'%3E%3Cpath fill='white' d='M21 20a1 1 0 0 1-1 1H4a1 1 0 0 1-1-1V9.49a1 1 0 0 1 .386-.79l8-6.222a1 1 0 0 1 1.228 0l8 6.222a1 1 0 0 1 .386.79V20z'/%3E%3C/svg%3E");
  background-repeat: no-repeat;
  background-position: center;
}

.icon-paint {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' width='24' height='24'%3E%3Cpath fill='white' d='M12 2a9 9 0 0 1 9 9 9 9 0 0 1-9 9 9 9 0 0 1-9-9 9 9 0 0 1 9-9z'/%3E%3C/svg%3E");
  background-repeat: no-repeat;
  background-position: center;
}

.icon-brush {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' width='24' height='24'%3E%3Cpath fill='white' d='M14 3.25a1 1 0 0 1 1.707-.707l6 6a1 1 0 0 1 0 1.414l-8.586 8.586-9 3L2 19.422l3-9 8.586-8.586a1 1 0 0 1 .707-.293zM7.904 15.68l-1.586 1.586 1.414 1.414 1.586-1.586-1.414-1.414z'/%3E%3C/svg%3E");
  background-repeat: no-repeat;
  background-position: center;
}

.icon-tool {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' width='24' height='24'%3E%3Cpath fill='white' d='M20 7v13a1 1 0 0 1-1 1H5a1 1 0 0 1-1-1V4a1 1 0 0 1 1-1h7.414l2 2H14a1 1 0 0 1 1 1v1h2a1 1 0 0 1 1 1v1h1a1 1 0 0 1 1 1z'/%3E%3C/svg%3E");
  background-repeat: no-repeat;
  background-position: center;
}

.icon-settings {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' width='24' height='24'%3E%3Cpath fill='white' d='M12 1l9.5 5.5v11L12 23l-9.5-5.5v-11L12 1zm0 14a3 3 0 1 0 0-6 3 3 0 0 0 0 6z'/%3E%3C/svg%3E");
  background-repeat: no-repeat;
  background-position: center;
}

/* Responsive styles to fix the image/text overlap issue */
@media screen and (max-width: 1400px) {
  .market-presence-content {
    gap: 3.5rem;
  }
}

@media screen and (max-width: 1200px) {
  .market-presence-content {
    gap: 3rem;
  }
  
  .secondary-image-container {
    width: 50%;
    right: -5%;
  }
  
  .tertiary-image-container {
    width: 32%;
  }
}

@media screen and (max-width: 992px) {
  .market-presence-content {
    flex-direction: column !important;
    gap: 4rem;
  }
  
  /* On tablet, make each column full width for better spacing */
  .market-presence-media,
  .market-presence-text {
    width: 100%;
    max-width: 100%;
    flex: 0 0 100%;
  }
  
  /* When stacked, images can be larger */
  .stacked-images-container {
    max-width: 90%;
    margin: 0 auto;
  }
  
  .secondary-image-container {
    width: 45%;
    bottom: -8%;
    right: -4%;
  }
  
  .tertiary-image-container {
    width: 30%;
    top: -8%;
    left: -4%;
  }

  
  .play-button {
    width: 70px;
    height: 70px;
  }
  
  .play-button svg {
    width: 28px;
    height: 28px;
  }
}

@media screen and (max-width: 767px) {
  .market-presence-container {
    padding: 0 1.2rem;
  }
  
  .market-segments {
    grid-template-columns: 1fr;
  }
  
  .secondary-image-container {
    width: 40%;
  }
  
  .tertiary-image-container {
    width: 28%;
  }
  
  /* Adjust collage layout for better mobile display */
  .collage-main {
    grid-column: 1 / 3;
    grid-row: 1 / 2;
  }
  
  .collage-secondary {
    grid-column: 1 / 2;
    grid-row: 2 / 3;
  }
  
  .collage-tertiary {
    grid-column: 2 / 3;
    grid-row: 2 / 3;
  }
  
  .image-collage {
    gap: 10px;
  }
  
  .play-button {
    width: 60px;
    height: 60px;
  }
  
  .play-button svg {
    width: 24px;
    height: 24px;
  }
}

@media screen and (max-width: 480px) {  
  .market-presence-content {
    gap: 2.5rem;
  }
  
  .secondary-image-container {
    width: 40%;
    bottom: -6%;
    right: -3%;
  }
  
  .tertiary-image-container {
    width: 35%;
    top: -6%;
    left: -3%;
  }
}