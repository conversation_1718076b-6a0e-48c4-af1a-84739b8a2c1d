.video-reel {
  padding: var(--grid-desktop-vertical-spacing) 0;
  position: relative;
  overflow: hidden;
}

.video-reel__header {
  text-align: center;
  margin-bottom: 2rem;
}

.video-reel__subtitle {
  max-width: 650px;
  margin-left: auto;
  margin-right: auto;
  line-height: 1.5;
}

.video-reel__grid {
  display: grid;
  gap: 20px;
  transition: opacity 0.3s ease;
}

/* Animation classes */
.animate {
  transform: translateY(30px);
}

.slide-up {
  animation: slideUp 0.7s cubic-bezier(0.25, 0.46, 0.45, 0.94) forwards;
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.video-reel__item {
  position: relative;
  border-radius: 8px;
  overflow: hidden;
  background-color: rgba(var(--color-background), 1);
  box-shadow: 0 4px 12px rgba(var(--color-foreground), 0.08);
  transition: transform 0.5s cubic-bezier(0.25, 0.46, 0.45, 0.94),
              box-shadow 0.3s ease;
  display: flex;
  flex-direction: column;
}

.video-reel__item:hover {
  box-shadow: 0 10px 25px rgba(var(--color-foreground), 0.12);
  transform: translateY(-5px) scale(1.01);
}

.video-reel__video-wrapper {
  position: relative;
  width: 100%;
  height: 0;
  padding-bottom: 56.25%; /* 16:9 Aspect Ratio */
  background-color: rgba(var(--color-foreground), 0.04);
  cursor: pointer;
  border-radius: 8px 8px 0 0;
  overflow: hidden;
}

.video-reel__thumbnail {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: opacity 0.4s ease, transform 0.6s ease;
  will-change: transform;
}

.video-reel__item:hover .video-reel__thumbnail {
  transform: scale(1.05);
}

.video-reel__deferred-media {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
}

.video-reel__poster {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  background: transparent;
  border: none;
  cursor: pointer;
  padding: 0;
  margin: 0;
}

.video-reel__play-button {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 70px;
  height: 70px;
  background-color: rgba(209, 176, 115, 0.85); /* #d1b073 with opacity */
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 2;
  transition: all 0.3s ease;
}

.video-reel__play-button svg {
  width: 45px;
  height: 45px;
  fill: #fff;
  filter: drop-shadow(0 1px 1px rgba(0, 0, 0, 0.1));
}

.video-reel__item:hover .video-reel__play-button {
  background-color: #d1b073;
  transform: translate(-50%, -50%) scale(1.1);
}

.video-reel__caption {
  padding: 1.5rem;
  text-align: center;
  flex-grow: 1;
  display: flex;
  flex-direction: column;
  justify-content: center;
  background-color: #fff;
  border-radius: 0 0 8px 8px;
}

.video-reel__name {
  font-size: 1.5rem;
  font-weight: 600;
  margin-bottom: 0.8rem;
  color: #222228;
}

.video-reel__description {
  font-size: 1.2rem;
  line-height: 1.5;
  color: rgba(34, 34, 40, 0.8);
}



/* Video Modal & Embed styles */
.video-modal {
  display: none;
}

.video-reel__embed {
  position: relative;
  padding-bottom: 56.25%;
  height: 0;
  width: 100%;
  background-color: #000;
}

.video-reel__embed iframe,
.video-reel__embed video {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  object-fit: contain;
  background-color: #000;
}

/* Video overlay gradient effect */
.video-reel__overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(to bottom,
                 rgba(34, 34, 40, 0) 0%,
                 rgba(34, 34, 40, 0.5) 100%);
  z-index: 1;
  pointer-events: none;
  transition: opacity 0.3s ease;
}

.video-reel__item:hover .video-reel__overlay {
  opacity: 0.7;
}

/* Lightbox trigger area */
.video-reel__lightbox-trigger {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 3;
  cursor: pointer;
}

.video-reel__lightbox-trigger a {
  display: block;
  width: 100%;
  height: 100%;
  position: absolute;
  top: 0;
  left: 0;
}

/* Hover container for play-on-hover */
.video-reel__hover-container {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 2;
  overflow: hidden;
  background-color: #000;
}

.video-reel__hover-container video,
.video-reel__hover-container iframe {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  object-fit: contain;
}

/* Pulse animation for play button */
@keyframes pulse {
  0% {
    box-shadow: 0 0 0 0 rgba(209, 176, 115, 0.7);
  }
  70% {
    box-shadow: 0 0 0 15px rgba(209, 176, 115, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(209, 176, 115, 0);
  }
}

.video-reel__item:hover .video-reel__play-button {
  animation: pulse 1.5s infinite;
}

/* Responsive adjustments */
@media screen and (max-width: 989px) {
  .video-reel__play-button {
    width: 60px;
    height: 60px;
  }

  .video-reel__play-button svg {
    width: 24px;
    height: 24px;
  }
}

@media screen and (max-width: 749px) {
  .video-reel__header {
    margin-bottom: 2rem;
  }

  .video-reel__play-button {
    width: 50px;
    height: 50px;
  }

  .video-reel__play-button svg {
    width: 20px;
    height: 20px;
  }
}