<!doctype html>
<html class="no-js" lang="{{ request.locale.iso_code }}">
  <head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width,initial-scale=1">
    <meta name="theme-color" content="">
    <link rel="canonical" href="{{ canonical_url }}">

    {%- if settings.favicon != blank -%}
      <link rel="icon" type="image/png" href="{{ settings.favicon | image_url: width: 32, height: 32 }}">
    {%- endif -%}

    {%- unless settings.type_header_font.system? and settings.type_body_font.system? -%}
      <link rel="preconnect" href="https://fonts.shopifycdn.com" crossorigin>
    {%- endunless -%}

    <title>
      {{ page_title }}
      {%- if current_tags %} &ndash; tagged "{{ current_tags | join: ', ' }}"{% endif -%}
      {%- if current_page != 1 %} &ndash; Page {{ current_page }}{% endif -%}
      {%- unless page_title contains shop.name %} &ndash; {{ shop.name }}{% endunless -%}
    </title>

    {% if page_description %}
      <meta name="description" content="{{ page_description | escape }}">
    {% endif %}

    {% render 'meta-tags' %}

    <script src="{{ 'constants.js' | asset_url }}" defer="defer"></script>
    <script src="{{ 'pubsub.js' | asset_url }}" defer="defer"></script>
    <script src="{{ 'global.js' | asset_url }}" defer="defer"></script>
    {%- if settings.animations_reveal_on_scroll -%}
      <script src="{{ 'animations.js' | asset_url }}" defer="defer"></script>
    {%- endif -%}

    {{ content_for_header }}

    {%- liquid
      assign body_font_bold = settings.type_body_font | font_modify: 'weight', 'bold'
      assign body_font_italic = settings.type_body_font | font_modify: 'style', 'italic'
      assign body_font_bold_italic = body_font_bold | font_modify: 'style', 'italic'
    %}

    {% style %}
      {{ settings.type_body_font | font_face: font_display: 'swap' }}
      {{ body_font_bold | font_face: font_display: 'swap' }}
      {{ body_font_italic | font_face: font_display: 'swap' }}
      {{ body_font_bold_italic | font_face: font_display: 'swap' }}
      {{ settings.type_header_font | font_face: font_display: 'swap' }}

      {% for scheme in settings.color_schemes -%}
        {% assign scheme_classes = scheme_classes | append: ', .color-' | append: scheme.id %}
        {% if forloop.index == 1 -%}
          :root,
        {%- endif %}
        .color-{{ scheme.id }} {
          --color-background: {{ scheme.settings.background.red }},{{ scheme.settings.background.green }},{{ scheme.settings.background.blue }};
        {% if scheme.settings.background_gradient != empty %}
          --gradient-background: {{ scheme.settings.background_gradient }};
        {% else %}
          --gradient-background: {{ scheme.settings.background }};
        {% endif %}

        {% liquid
          assign background_color = scheme.settings.background
          assign background_color_brightness = background_color | color_brightness
          if background_color_brightness <= 26
            assign background_color_contrast = background_color | color_lighten: 50
          elsif background_color_brightness <= 65
            assign background_color_contrast = background_color | color_lighten: 5
          else
            assign background_color_contrast = background_color | color_darken: 25
          endif
        %}

        --color-foreground: {{ scheme.settings.text.red }},{{ scheme.settings.text.green }},{{ scheme.settings.text.blue }};
        --color-background-contrast: {{ background_color_contrast.red }},{{ background_color_contrast.green }},{{ background_color_contrast.blue }};
        --color-shadow: {{ scheme.settings.shadow.red }},{{ scheme.settings.shadow.green }},{{ scheme.settings.shadow.blue }};
        --color-button: {{ scheme.settings.button.red }},{{ scheme.settings.button.green }},{{ scheme.settings.button.blue }};
        --color-button-text: {{ scheme.settings.button_label.red }},{{ scheme.settings.button_label.green }},{{ scheme.settings.button_label.blue }};
        --color-secondary-button: {{ scheme.settings.background.red }},{{ scheme.settings.background.green }},{{ scheme.settings.background.blue }};
        --color-secondary-button-text: {{ scheme.settings.secondary_button_label.red }},{{ scheme.settings.secondary_button_label.green }},{{ scheme.settings.secondary_button_label.blue }};
        --color-link: {{ scheme.settings.secondary_button_label.red }},{{ scheme.settings.secondary_button_label.green }},{{ scheme.settings.secondary_button_label.blue }};
        --color-badge-foreground: {{ scheme.settings.text.red }},{{ scheme.settings.text.green }},{{ scheme.settings.text.blue }};
        --color-badge-background: {{ scheme.settings.background.red }},{{ scheme.settings.background.green }},{{ scheme.settings.background.blue }};
        --color-badge-border: {{ scheme.settings.text.red }},{{ scheme.settings.text.green }},{{ scheme.settings.text.blue }};
        --payment-terms-background-color: rgb({{ scheme.settings.background.rgb }});
      }
      {% endfor %}

      {{ scheme_classes | prepend: 'body' }} {
        color: rgba(var(--color-foreground), 0.75);
        background-color: rgb(var(--color-background));
      }

      :root {
        --font-body-style: {{ settings.type_body_font.style }};
        --font-body-weight: {{ settings.type_body_font.weight }};
        --font-body-weight-bold: {{ settings.type_body_font.weight | plus: 300 | at_most: 1000 }};

        --font-heading-style: {{ settings.type_header_font.style }};
        --font-heading-weight: {{ settings.type_header_font.weight }};

        --font-body-scale: {{ settings.body_scale | divided_by: 100.0 }};
        --font-heading-scale: {{ settings.heading_scale | times: 1.0 | divided_by: settings.body_scale }};

        --media-padding: {{ settings.media_padding }}px;
        --media-border-opacity: {{ settings.media_border_opacity | divided_by: 100.0 }};
        --media-border-width: {{ settings.media_border_thickness }}px;
        --media-radius: {{ settings.media_radius }}px;
        --media-shadow-opacity: {{ settings.media_shadow_opacity | divided_by: 100.0 }};
        --media-shadow-horizontal-offset: {{ settings.media_shadow_horizontal_offset }}px;
        --media-shadow-vertical-offset: {{ settings.media_shadow_vertical_offset }}px;
        --media-shadow-blur-radius: {{ settings.media_shadow_blur }}px;
        --media-shadow-visible: {% if settings.media_shadow_opacity > 0 %}1{% else %}0{% endif %};

        --page-width: {{ settings.page_width | divided_by: 10 }}rem;
        --page-width-margin: {% if settings.page_width == '1600' %}2{% else %}0{% endif %}rem;

        --product-card-image-padding: {{ settings.card_image_padding | divided_by: 10.0 }}rem;
        --product-card-corner-radius: {{ settings.card_corner_radius | divided_by: 10.0 }}rem;
        --product-card-text-alignment: {{ settings.card_text_alignment }};
        --product-card-border-width: {{ settings.card_border_thickness | divided_by: 10.0 }}rem;
        --product-card-border-opacity: {{ settings.card_border_opacity | divided_by: 100.0 }};
        --product-card-shadow-opacity: {{ settings.card_shadow_opacity | divided_by: 100.0 }};
        --product-card-shadow-visible: {% if settings.card_shadow_opacity > 0 %}1{% else %}0{% endif %};
        --product-card-shadow-horizontal-offset: {{ settings.card_shadow_horizontal_offset | divided_by: 10.0 }}rem;
        --product-card-shadow-vertical-offset: {{ settings.card_shadow_vertical_offset | divided_by: 10.0 }}rem;
        --product-card-shadow-blur-radius: {{ settings.card_shadow_blur | divided_by: 10.0 }}rem;

        --collection-card-image-padding: {{ settings.collection_card_image_padding | divided_by: 10.0 }}rem;
        --collection-card-corner-radius: {{ settings.collection_card_corner_radius | divided_by: 10.0 }}rem;
        --collection-card-text-alignment: {{ settings.collection_card_text_alignment }};
        --collection-card-border-width: {{ settings.collection_card_border_thickness | divided_by: 10.0 }}rem;
        --collection-card-border-opacity: {{ settings.collection_card_border_opacity | divided_by: 100.0 }};
        --collection-card-shadow-opacity: {{ settings.collection_card_shadow_opacity | divided_by: 100.0 }};
        --collection-card-shadow-visible: {% if settings.collection_card_shadow_opacity > 0 %}1{% else %}0{% endif %};
        --collection-card-shadow-horizontal-offset: {{ settings.collection_card_shadow_horizontal_offset | divided_by: 10.0 }}rem;
        --collection-card-shadow-vertical-offset: {{ settings.collection_card_shadow_vertical_offset | divided_by: 10.0 }}rem;
        --collection-card-shadow-blur-radius: {{ settings.collection_card_shadow_blur | divided_by: 10.0 }}rem;

        --blog-card-image-padding: {{ settings.blog_card_image_padding | divided_by: 10.0 }}rem;
        --blog-card-corner-radius: {{ settings.blog_card_corner_radius | divided_by: 10.0 }}rem;
        --blog-card-text-alignment: {{ settings.blog_card_text_alignment }};
        --blog-card-border-width: {{ settings.blog_card_border_thickness | divided_by: 10.0 }}rem;
        --blog-card-border-opacity: {{ settings.blog_card_border_opacity | divided_by: 100.0 }};
        --blog-card-shadow-opacity: {{ settings.blog_card_shadow_opacity | divided_by: 100.0 }};
        --blog-card-shadow-visible: {% if settings.blog_card_shadow_opacity > 0 %}1{% else %}0{% endif %};
        --blog-card-shadow-horizontal-offset: {{ settings.blog_card_shadow_horizontal_offset | divided_by: 10.0 }}rem;
        --blog-card-shadow-vertical-offset: {{ settings.blog_card_shadow_vertical_offset | divided_by: 10.0 }}rem;
        --blog-card-shadow-blur-radius: {{ settings.blog_card_shadow_blur | divided_by: 10.0 }}rem;

        --badge-corner-radius: {{ settings.badge_corner_radius | divided_by: 10.0 }}rem;

        --popup-border-width: {{ settings.popup_border_thickness }}px;
        --popup-border-opacity: {{ settings.popup_border_opacity | divided_by: 100.0 }};
        --popup-corner-radius: {{ settings.popup_corner_radius }}px;
        --popup-shadow-opacity: {{ settings.popup_shadow_opacity | divided_by: 100.0 }};
        --popup-shadow-horizontal-offset: {{ settings.popup_shadow_horizontal_offset }}px;
        --popup-shadow-vertical-offset: {{ settings.popup_shadow_vertical_offset }}px;
        --popup-shadow-blur-radius: {{ settings.popup_shadow_blur }}px;

        --drawer-border-width: {{ settings.drawer_border_thickness }}px;
        --drawer-border-opacity: {{ settings.drawer_border_opacity | divided_by: 100.0 }};
        --drawer-shadow-opacity: {{ settings.drawer_shadow_opacity | divided_by: 100.0 }};
        --drawer-shadow-horizontal-offset: {{ settings.drawer_shadow_horizontal_offset }}px;
        --drawer-shadow-vertical-offset: {{ settings.drawer_shadow_vertical_offset }}px;
        --drawer-shadow-blur-radius: {{ settings.drawer_shadow_blur }}px;

        --spacing-sections-desktop: {{ settings.spacing_sections }}px;
        --spacing-sections-mobile: {% if settings.spacing_sections < 24 %}{{ settings.spacing_sections }}{% else %}{{ settings.spacing_sections | times: 0.7 | round | at_least: 20 }}{% endif %}px;

        --grid-desktop-vertical-spacing: {{ settings.spacing_grid_vertical }}px;
        --grid-desktop-horizontal-spacing: {{ settings.spacing_grid_horizontal }}px;
        --grid-mobile-vertical-spacing: {{ settings.spacing_grid_vertical | divided_by: 2 }}px;
        --grid-mobile-horizontal-spacing: {{ settings.spacing_grid_horizontal | divided_by: 2 }}px;

        --text-boxes-border-opacity: {{ settings.text_boxes_border_opacity | divided_by: 100.0 }};
        --text-boxes-border-width: {{ settings.text_boxes_border_thickness }}px;
        --text-boxes-radius: {{ settings.text_boxes_radius }}px;
        --text-boxes-shadow-opacity: {{ settings.text_boxes_shadow_opacity | divided_by: 100.0 }};
        --text-boxes-shadow-visible: {% if settings.text_boxes_shadow_opacity > 0 %}1{% else %}0{% endif %};
        --text-boxes-shadow-horizontal-offset: {{ settings.text_boxes_shadow_horizontal_offset }}px;
        --text-boxes-shadow-vertical-offset: {{ settings.text_boxes_shadow_vertical_offset }}px;
        --text-boxes-shadow-blur-radius: {{ settings.text_boxes_shadow_blur }}px;

        --buttons-radius: {{ settings.buttons_radius }}px;
        --buttons-radius-outset: {% if settings.buttons_radius > 0 %}{{ settings.buttons_radius | plus: settings.buttons_border_thickness }}{% else %}0{% endif %}px;
        --buttons-border-width: {% if settings.buttons_border_opacity > 0 %}{{ settings.buttons_border_thickness }}{% else %}0{% endif %}px;
        --buttons-border-opacity: {{ settings.buttons_border_opacity | divided_by: 100.0 }};
        --buttons-shadow-opacity: {{ settings.buttons_shadow_opacity | divided_by: 100.0 }};
        --buttons-shadow-visible: {% if settings.buttons_shadow_opacity > 0 %}1{% else %}0{% endif %};
        --buttons-shadow-horizontal-offset: {{ settings.buttons_shadow_horizontal_offset }}px;
        --buttons-shadow-vertical-offset: {{ settings.buttons_shadow_vertical_offset }}px;
        --buttons-shadow-blur-radius: {{ settings.buttons_shadow_blur }}px;
        --buttons-border-offset: {% if settings.buttons_radius > 0 or settings.buttons_shadow_opacity > 0 %}0.3{% else %}0{% endif %}px;

        --inputs-radius: {{ settings.inputs_radius }}px;
        --inputs-border-width: {{ settings.inputs_border_thickness }}px;
        --inputs-border-opacity: {{ settings.inputs_border_opacity | divided_by: 100.0 }};
        --inputs-shadow-opacity: {{ settings.inputs_shadow_opacity | divided_by: 100.0 }};
        --inputs-shadow-horizontal-offset: {{ settings.inputs_shadow_horizontal_offset }}px;
        --inputs-margin-offset: {% if settings.inputs_shadow_vertical_offset != 0 and settings.inputs_shadow_opacity > 0 %}{{ settings.inputs_shadow_vertical_offset | abs }}{% else %}0{% endif %}px;
        --inputs-shadow-vertical-offset: {{ settings.inputs_shadow_vertical_offset }}px;
        --inputs-shadow-blur-radius: {{ settings.inputs_shadow_blur }}px;
        --inputs-radius-outset: {% if settings.inputs_radius > 0 %}{{ settings.inputs_radius | plus: settings.inputs_border_thickness }}{% else %}0{% endif %}px;

        --variant-pills-radius: {{ settings.variant_pills_radius }}px;
        --variant-pills-border-width: {{ settings.variant_pills_border_thickness }}px;
        --variant-pills-border-opacity: {{ settings.variant_pills_border_opacity | divided_by: 100.0 }};
        --variant-pills-shadow-opacity: {{ settings.variant_pills_shadow_opacity | divided_by: 100.0 }};
        --variant-pills-shadow-horizontal-offset: {{ settings.variant_pills_shadow_horizontal_offset }}px;
        --variant-pills-shadow-vertical-offset: {{ settings.variant_pills_shadow_vertical_offset }}px;
        --variant-pills-shadow-blur-radius: {{ settings.variant_pills_shadow_blur }}px;

        --product-primary: #222228;
        --product-primary-light: #444;
        --product-accent: #D1B073;
        --product-accent-light: #D1B073;
        --product-bg-light: #f8f9fa;
        --product-border-radius: 10px;
        --product-shadow: 0 8px 30px rgba(0, 0, 0, 0.08);
        --product-transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
      }

      *,
      *::before,
      *::after {
        box-sizing: inherit;
      }

      html {
        box-sizing: border-box;
        font-size: calc(var(--font-body-scale) * 62.5%);
        height: 100%;
      }

      body {
        display: grid;
        grid-template-rows: auto auto 1fr auto;
        grid-template-columns: 100%;
        min-height: 100%;
        margin: 0;
        font-size: 1.5rem;
        letter-spacing: 0.06rem;
        line-height: calc(1 + 0.8 / var(--font-body-scale));
        font-family: var(--font-body-family);
        font-style: var(--font-body-style);
        font-weight: var(--font-body-weight);
      }

      @media screen and (min-width: 750px) {
        body {
          font-size: 1.6rem;
        }
      }
    {% endstyle %}    
    {% render 'custom-fonts' %}
    {{ 'custom-fonts.css' | asset_url | stylesheet_tag }}
    {{ 'rcl-custom-styles.css' | asset_url | stylesheet_tag }}
    {{ 'base.css' | asset_url | stylesheet_tag }}

    {%- unless settings.type_body_font.system? -%}
      <link rel="preload" as="font" href="{{ settings.type_body_font | font_url }}" type="font/woff2" crossorigin>
    {%- endunless -%}
    {%- unless settings.type_header_font.system? -%}
      <link rel="preload" as="font" href="{{ settings.type_header_font | font_url }}" type="font/woff2" crossorigin>
    {%- endunless -%}

    {%- if localization.available_countries.size > 1 or localization.available_languages.size > 1 -%}
      {{ 'component-localization-form.css' | asset_url | stylesheet_tag: preload: true }}
      <script src="{{ 'localization-form.js' | asset_url }}" defer="defer"></script>
    {%- endif -%}

    {%- if settings.predictive_search_enabled -%}
      <link
        rel="stylesheet"
        href="{{ 'component-predictive-search.css' | asset_url }}"
        media="print"
        onload="this.media='all'"
      >
    {%- endif -%}

    <script>
      document.documentElement.className = document.documentElement.className.replace('no-js', 'js');
      if (Shopify.designMode) {
        document.documentElement.classList.add('shopify-design-mode');
      }
    </script>
    <meta name="google-site-verification" content="FBlE902OkSIOyaOjcnl1wuQB7BL9vWztQu3eLJZ0gF4" />
  </head>

  <body class="gradient{% if settings.animations_hover_elements != 'none' %} animate--hover-{{ settings.animations_hover_elements }}{% endif %}">
    <a class="skip-to-content-link button visually-hidden" href="#MainContent">
      {{ 'accessibility.skip_to_text' | t }}
    </a>

    {%- if settings.cart_type == 'drawer' -%}
      {%- render 'cart-drawer' -%}
    {%- endif -%}

    {% sections 'header-group' %}

    <main id="MainContent" class="content-for-layout focus-none" role="main" tabindex="-1">
      {{ content_for_layout }}
    </main>

    {% sections 'footer-group' %}

    <ul hidden>
      <li id="a11y-refresh-page-message">{{ 'accessibility.refresh_page' | t }}</li>
      <li id="a11y-new-window-message">{{ 'accessibility.link_messages.new_window' | t }}</li>
    </ul>

    <script>
      window.shopUrl = '{{ request.origin }}';
      window.routes = {
        cart_add_url: '{{ routes.cart_add_url }}',
        cart_change_url: '{{ routes.cart_change_url }}',
        cart_update_url: '{{ routes.cart_update_url }}',
        cart_url: '{{ routes.cart_url }}',
        predictive_search_url: '{{ routes.predictive_search_url }}',
      };

      window.cartStrings = {
        error: `{{ 'sections.cart.cart_error' | t }}`,
        quantityError: `{{ 'sections.cart.cart_quantity_error_html' | t: quantity: '[quantity]' }}`,
      };

      window.variantStrings = {
        addToCart: `{{ 'products.product.add_to_cart' | t }}`,
        soldOut: `{{ 'products.product.sold_out' | t }}`,
        unavailable: `{{ 'products.product.unavailable' | t }}`,
        unavailable_with_option: `{{ 'products.product.value_unavailable' | t: option_value: '[value]' }}`,
      };

      window.quickOrderListStrings = {
        itemsAdded: `{{ 'sections.quick_order_list.items_added.other' | t: quantity: '[quantity]' }}`,
        itemAdded: `{{ 'sections.quick_order_list.items_added.one' | t: quantity: '[quantity]' }}`,
        itemsRemoved: `{{ 'sections.quick_order_list.items_removed.other' | t: quantity: '[quantity]' }}`,
        itemRemoved: `{{ 'sections.quick_order_list.items_removed.one' | t: quantity: '[quantity]' }}`,
        viewCart: `{{- 'sections.quick_order_list.view_cart' | t -}}`,
        each: `{{- 'sections.quick_order_list.each' | t: money: '[money]' }}`,
      };

      window.accessibilityStrings = {
        imageAvailable: `{{ 'products.product.media.image_available' | t: index: '[index]' }}`,
        shareSuccess: `{{ 'general.share.success_message' | t }}`,
        pauseSlideshow: `{{ 'sections.slideshow.pause_slideshow' | t }}`,
        playSlideshow: `{{ 'sections.slideshow.play_slideshow' | t }}`,
        recipientFormExpanded: `{{ 'recipient.form.expanded' | t }}`,
        recipientFormCollapsed: `{{ 'recipient.form.collapsed' | t }}`,
        countrySelectorSearchCount: `{{ 'localization.country_results_count' | t: count: '[count]' }}`,
      };
    </script>

    {%- if settings.predictive_search_enabled -%}
      <script src="{{ 'predictive-search.js' | asset_url }}" defer="defer"></script>
    {%- endif -%}
  </body>
</html>
