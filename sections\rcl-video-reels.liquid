{{ 'section-video-reel.css' | asset_url | stylesheet_tag: preload: true }}
{{ 'component-deferred-media.css' | asset_url | stylesheet_tag: preload: true }}

{%- liquid
  # Initialize a flag to track if any valid videos are found
  assign has_valid_videos = false
  assign valid_blocks = 0

  # Check each block for valid video content
  for block in section.blocks
    if block.type == 'video'
      assign valid_video = false

      # Check for metafield video
      if block.settings.use_metafield and block.settings.metafield_path != blank
        assign metafield_parts = block.settings.metafield_path | split: '.'
        if metafield_parts.size == 2
          assign namespace = metafield_parts[0]
          assign key = metafield_parts[1]

          if product.metafields[namespace][key] != blank
            # Valid metafield video URL found
            assign valid_video = true
            assign has_valid_videos = true
            assign valid_blocks = valid_blocks | plus: 1
          endif
        endif
      endif

      # If no valid metafield, check for direct video URL
      if valid_video == false and block.settings.video_url != blank
        assign valid_video = true
        assign has_valid_videos = true
        assign valid_blocks = valid_blocks | plus: 1
      endif

      # If no valid URL, check for self-hosted video
      if valid_video == false and block.settings.video != blank
        assign valid_video = true
        assign has_valid_videos = true
        assign valid_blocks = valid_blocks | plus: 1
      endif
    endif
  endfor
-%}

{% if has_valid_videos %}
  <script src="https://code.jquery.com/jquery-3.7.1.min.js" integrity="sha256-/JqT3SQfawRcv/BIHPThkBvs0OEvtFFmqPF/lYI/Cxo=" crossorigin="anonymous"></script>
  {{ 'lity.min.css' | asset_url | stylesheet_tag: preload: true }}
  {{ 'lity-custom.css' | asset_url | stylesheet_tag: preload: true }}
  <script src="{{ 'lity.min.js' | asset_url }}"></script>

  {%- style -%}
    .section-{{ section.id }}-padding {
      background-color: {{ section.settings.background_color }};
      padding-top: {{ section.settings.padding_top | times: 0.5 | round: 0 }}px;
      padding-bottom: {{ section.settings.padding_bottom | times: 0.5 | round: 0 }}px;
    }

    @media screen and (min-width: 750px) {
      .section-{{ section.id }}-padding {
        padding-top: {{ section.settings.padding_top }}px;
        padding-bottom: {{ section.settings.padding_bottom }}px;
      }
    }

    #VideoReel-{{ section.id }} .section-title {
      color: {{ section.settings.title_color }};
      text-align: center;
      margin-bottom: 1.5rem;
    }

    #VideoReel-{{ section.id }} .video-reel__subtitle {
      margin-bottom: 3rem;
    }
  {%- endstyle -%}

  <div class="video-reel section-{{ section.id }}-padding" id="VideoReel-{{ section.id }}">
    <div class="page-width">
      {%- if section.settings.title != blank -%}
        <div class="video-reel__header">
          <h2 class="section-title animate slide-up">{{ section.settings.title | escape }}</h2>
          {%- if section.settings.subtitle != blank -%}
            <div class="video-reel__subtitle animate slide-up" style="animation-delay: 100ms;">
              {{ section.settings.subtitle }}
            </div>
          {%- endif -%}
        </div>
      {%- endif -%}

      {%- assign columns_desktop = section.settings.columns_desktop | plus: 0 -%}
      {%- assign columns_tablet = section.settings.columns_tablet | plus: 0 -%}
      {%- assign columns_mobile = section.settings.columns_mobile | plus: 0 -%}

      <div class="video-reel__grid"
           style="grid-template-columns: repeat({{ columns_desktop }}, 1fr);"
           data-columns-desktop="{{ columns_desktop }}"
           data-columns-tablet="{{ columns_tablet }}"
           data-columns-mobile="{{ columns_mobile }}">
        {%- for block in section.blocks -%}
          {%- case block.type -%}
            {%- when 'video' -%}
              {%- liquid
                assign valid_video = false
                assign metafield_video_url = ''
                assign video_platform = ''
                assign video_id = ''

                if block.settings.use_metafield and block.settings.metafield_path != blank
                  assign metafield_parts = block.settings.metafield_path | split: '.'
                  if metafield_parts.size == 2
                    assign namespace = metafield_parts[0]
                    assign key = metafield_parts[1]

                    if product.metafields[namespace][key] != blank
                      assign metafield_video_url = product.metafields[namespace][key]
                      assign valid_video = true

                      if metafield_video_url contains 'youtube.com' or metafield_video_url contains 'youtu.be'
                        assign video_platform = 'youtube'
                        if metafield_video_url contains 'youtube.com/watch?v='
                          assign youtube_parts = metafield_video_url | split: 'watch?v='
                          if youtube_parts.size > 1
                            assign youtube_id = youtube_parts[1] | split: '&' | first
                          endif
                        elsif metafield_video_url contains 'youtu.be/'
                          assign youtube_parts = metafield_video_url | split: 'youtu.be/'
                          if youtube_parts.size > 1
                            assign youtube_id = youtube_parts[1] | split: '?' | first
                          endif
                        endif
                        assign video_id = youtube_id
                      elsif metafield_video_url contains 'vimeo.com'
                        assign video_platform = 'vimeo'
                        assign vimeo_parts = metafield_video_url | split: 'vimeo.com/'
                        if vimeo_parts.size > 1
                          assign vimeo_id = vimeo_parts[1] | split: '?' | first
                        endif
                        assign video_id = vimeo_id
                      endif
                    endif
                  endif
                endif

                if video_id == blank
                  if block.settings.video_url != blank
                    assign video_id = block.settings.video_url.id
                    if block.settings.video_url.type == 'youtube'
                      assign video_platform = 'youtube'
                    elsif block.settings.video_url.type == 'vimeo'
                      assign video_platform = 'vimeo'
                    endif
                    assign valid_video = true
                  elsif block.settings.video != blank
                    assign video_id = block.settings.video.id
                    assign valid_video = true
                  endif
                endif

                assign appvideo_thumbnail = null
                if block.settings.use_appvideo_thumbnail
                  if product.metafields['custom']['appvideo_thumbnail'] != blank
                    assign appvideo_thumbnail = product.metafields['custom']['appvideo_thumbnail']
                  endif
                endif

                assign video_alt = block.settings.video.alt | default: block.settings.description
                assign alt = 'sections.video.load_video' | t: description: block.settings.title | escape

                if appvideo_thumbnail != blank
                  assign poster = appvideo_thumbnail
                else
                  assign poster = block.settings.video.preview_image | default: block.settings.cover_image
                endif
              -%}

              {% if valid_video %}
                <div class="video-reel__item"
                     id="VideoReelItem-{{ block.id }}"
                     data-index="{{ forloop.index0 }}"
                     {{ block.shopify_attributes }}>
                  <div class="video-reel__video-wrapper{% if section.settings.play_on_hover and section.settings.enable_lightbox == false %} hover-play{% endif %}">

                    <div class="video-reel__overlay"></div>

                    {% if section.settings.enable_lightbox %}
                      <div class="video-reel__play-button">
                        <svg viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                          <path d="M8 5v14l11-7z"/>
                        </svg>
                      </div>

                      {% if poster != blank %}
                        <img src="{{ poster | image_url: width: 1200 }}"
                             class="video-reel__thumbnail"
                             loading="lazy"
                             width="900"
                             height="506"
                             alt="{{ block.settings.title | escape }}">
                      {% endif %}

                      <div class="video-reel__lightbox-trigger">
                        {% if video_platform == 'youtube' and video_id != blank %}
                          <a href="https://www.youtube.com/watch?v={{ video_id }}"
                             data-lity
                             aria-label="{{ 'sections.video.play_video' | t: title: block.settings.title | escape }}">
                            <span class="visually-hidden">{{ 'sections.video.play_video' | t: title: block.settings.title | escape }}</span>
                          </a>
                        {% elsif video_platform == 'vimeo' and video_id != blank %}
                          <a href="https://vimeo.com/{{ video_id }}"
                             data-lity
                             aria-label="{{ 'sections.video.play_video' | t: title: block.settings.title | escape }}">
                            <span class="visually-hidden">{{ 'sections.video.play_video' | t: title: block.settings.title | escape }}</span>
                          </a>
                        {% elsif block.settings.video != blank %}
                          <a href="#VideoModal-{{ section.id }}-{{ block.id }}"
                             data-lity
                             aria-label="{{ 'sections.video.play_video' | t: title: block.settings.title | escape }}">
                            <span class="visually-hidden">{{ 'sections.video.play_video' | t: title: block.settings.title | escape }}</span>
                          </a>
                        {% endif %}
                      </div>

                    {% else %}
                      <deferred-media
                        class="video-reel__deferred-media deferred-media no-js-hidden"
                        data-media-id="{{ video_id }}"
                        {% if poster != blank %}
                          style="--ratio-percent: {{ 1 | divided_by: poster.aspect_ratio | times: 100 }}%;"
                        {% endif %}
                      >
                        <button
                          id="Deferred-Poster-{{ video_id }}"
                          class="video-reel__poster deferred-media__poster"
                          type="button"
                          aria-label="{{ alt }}"
                        >
                          {% if poster != blank %}
                            <img src="{{ poster | image_url: width: 1200 }}"
                                 class="video-reel__thumbnail"
                                 loading="lazy"
                                 width="900"
                                 height="506"
                                 alt="{{ block.settings.title | escape }}">
                          {% endif %}

                          <div class="video-reel__play-button">
                            <svg viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                              <path d="M8 5v14l11-7z"/>
                            </svg>
                          </div>
                        </button>

                        <template>
                          {%- liquid
                            assign loop = ''
                            if section.settings.enable_video_looping
                              assign loop = '&loop=1&playlist=' | append: video_id
                            endif
                          -%}
                          {%- if video_platform == 'youtube' and video_id != blank -%}
                            <iframe
                              src="https://www.youtube.com/embed/{{ video_id }}?enablejsapi=1&autoplay=1{{ loop }}"
                              class="js-youtube"
                              allow="autoplay; encrypted-media"
                              allowfullscreen
                              title="{{ block.settings.title | escape }}"
                            ></iframe>
                          {%- elsif video_platform == 'vimeo' and video_id != blank -%}
                            <iframe
                              src="https://player.vimeo.com/video/{{ video_id }}?autoplay=1{{ loop }}"
                              class="js-vimeo"
                              allow="autoplay; encrypted-media"
                              allowfullscreen
                              title="{{ block.settings.title | escape }}"
                            ></iframe>
                          {%- elsif block.settings.video != blank -%}
                            {{
                              block.settings.video
                              | video_tag:
                                image_size: '1100x',
                                autoplay: true,
                                loop: section.settings.enable_video_looping,
                                controls: true,
                                muted: false,
                                preload: "metadata"
                            }}
                          {%- endif -%}
                        </template>
                      </deferred-media>
                    {% endif %}
                  </div>

                  {% if block.settings.title != blank or block.settings.description != blank %}
                    <div class="video-reel__caption">
                      {% if block.settings.title != blank %}
                        <h3 class="video-reel__name">{{ block.settings.title | escape }}</h3>
                      {% endif %}
                      {% if block.settings.description != blank %}
                        <div class="video-reel__description">{{ block.settings.description }}</div>
                      {% endif %}
                    </div>
                  {% endif %}
                </div>
              {% endif %}
          {%- endcase -%}
        {%- endfor -%}
      </div>
    </div>
  </div>

  {% if section.settings.enable_lightbox %}
    {% for block in section.blocks %}
      {% if block.settings.video != blank %}
        <div id="VideoModal-{{ section.id }}-{{ block.id }}" class="lity-hide video-modal">
          <div class="video-reel__embed">
            {{
              block.settings.video
              | video_tag:
                controls: true,
                autoplay: true,
                muted: false,
                loop: section.settings.enable_video_looping,
                preload: "auto"
            }}
          </div>
        </div>
      {% endif %}
    {% endfor %}
  {% endif %}

  <script>
    document.addEventListener('DOMContentLoaded', function() {
      const videoReel = document.getElementById('VideoReel-{{ section.id }}');
      if (!videoReel) return;

      const videoGrid = videoReel.querySelector('.video-reel__grid');
      const videoItems = videoReel.querySelectorAll('.video-reel__item');

      if (videoItems.length === 0) return;

      function adjustGrid() {
        const columnsDesktop = parseInt(videoGrid.dataset.columnsDesktop) || 3;
        const columnsTablet = parseInt(videoGrid.dataset.columnsTablet) || 2;
        const columnsMobile = parseInt(videoGrid.dataset.columnsMobile) || 1;

        if (window.innerWidth >= 990) {
          videoGrid.style.gridTemplateColumns = `repeat(${columnsDesktop}, 1fr)`;
        } else if (window.innerWidth >= 750) {
          videoGrid.style.gridTemplateColumns = `repeat(${columnsTablet}, 1fr)`;
        } else {
          videoGrid.style.gridTemplateColumns = `repeat(${columnsMobile}, 1fr)`;
        }
      }

      adjustGrid();

      window.addEventListener('resize', adjustGrid);

      function setEqualHeights() {
        videoItems.forEach(item => {
          item.style.height = 'auto';
        });

        const rows = {};
        videoItems.forEach(item => {
          item.classList.add('animate');

          const rect = item.getBoundingClientRect();
          const rowTop = Math.floor(rect.top / 10) * 10;

          if (!rows[rowTop]) {
            rows[rowTop] = [];
          }
          rows[rowTop].push(item);
        });

        Object.values(rows).forEach(rowItems => {
          if (rowItems.length) {
            const maxHeight = Math.max(...rowItems.map(item => item.offsetHeight));
            rowItems.forEach(item => {
              item.style.height = `${maxHeight}px`;
            });
          }
        });
      }

      videoItems.forEach((item, index) => {
        setTimeout(() => {
          item.classList.add('slide-up');
          item.style.animationDelay = `${index * 100}ms`;
        }, 100);
      });

      window.addEventListener('load', () => {
        setTimeout(setEqualHeights, 250);
      });

      let resizeTimer;
      window.addEventListener('resize', () => {
        clearTimeout(resizeTimer);
        resizeTimer = setTimeout(setEqualHeights, 250);
      });

      if (window.jQuery && window.jQuery.fn.lity && {{ section.settings.enable_lightbox | json }}) {
        jQuery(document).on('lity:open', function(event, instance) {
          const videoContainer = instance.element().find('video');
          if (videoContainer.length) {
            videoContainer[0].load();
            setTimeout(() => {
              videoContainer[0].play();
            }, 100);
          }
        });
      }

      {% if section.settings.play_on_hover and section.settings.enable_lightbox == false %}
        videoItems.forEach(item => {
          const videoContainer = item.querySelector('.hover-play');
          if (!videoContainer) return;

          const deferredMedia = videoContainer.querySelector('deferred-media');
          if (!deferredMedia) return;

          const template = deferredMedia.querySelector('template');
          if (!template) return;

          const hoverContent = template.content.cloneNode(true);
          const videoElement = hoverContent.querySelector('video');
          const iframeElement = hoverContent.querySelector('iframe');

          if (videoElement || iframeElement) {
            const hoverContainer = document.createElement('div');
            hoverContainer.className = 'video-reel__hover-container';
            hoverContainer.style.display = 'none';

            if (videoElement) {
              videoElement.muted = true;
              videoElement.loop = true;
              videoElement.controls = false;
              videoElement.preload = "metadata";
              hoverContainer.appendChild(videoElement);
            } else if (iframeElement) {
              let src = iframeElement.src;
              if (src.indexOf('youtube.com') > -1) {
                src = src + '&mute=1';
              } else if (src.indexOf('vimeo.com') > -1) {
                src = src + '&muted=1';
              }
              iframeElement.src = src;
              hoverContainer.appendChild(iframeElement);
            }

            videoContainer.appendChild(hoverContainer);

            item.addEventListener('mouseenter', function() {
              if (window.innerWidth >= 750) {
                hoverContainer.style.display = 'block';

                if (videoElement) {
                  videoElement.play().catch(e => console.log('Auto-play prevented:', e));
                }

                const poster = videoContainer.querySelector('.video-reel__poster');
                if (poster) poster.style.opacity = '0.3';
              }
            });

            item.addEventListener('mouseleave', function() {
              if (window.innerWidth >= 750) {
                hoverContainer.style.display = 'none';

                if (videoElement) {
                  videoElement.pause();
                }

                const poster = videoContainer.querySelector('.video-reel__poster');
                if (poster) poster.style.opacity = '1';
              }
            });
          }
        });
      {% endif %}
    });
  </script>
{% endif %}

{% schema %}
{
  "name": "Video Reel",
  "tag": "section",
  "class": "section",
  "disabled_on": {
    "groups": ["header", "footer"]
  },
  "settings": [
    {
      "type": "color",
      "id": "background_color",
      "label": "Background Color",
      "default": "transparent"
    },
    {
      "type": "text",
      "id": "title",
      "default": "Video Gallery",
      "label": "Heading"
    },
    {
      "type": "color",
      "id": "title_color",
      "label": "Title Color",
      "default": "#d1b073"
    },
    {
      "type": "richtext",
      "id": "subtitle",
      "default": "<p>Showcase your videos in a beautiful animated gallery</p>",
      "label": "Subheading"
    },
    {
      "type": "header",
      "content": "Video Display Settings"
    },
    {
      "type": "checkbox",
      "id": "enable_lightbox",
      "label": "Enable lightbox",
      "default": true,
      "info": "Opens videos in a popup lightbox when clicked"
    },
    {
      "type": "checkbox",
      "id": "play_on_hover",
      "label": "Play on hover",
      "default": false,
      "info": "Automatically plays videos when hovering (only works when lightbox is disabled)"
    },
    {
      "type": "checkbox",
      "id": "enable_video_looping",
      "label": "Loop videos",
      "default": false,
      "info": "When enabled, videos will play in a continuous loop"
    },
    {
      "type": "range",
      "id": "columns_desktop",
      "min": 1,
      "max": 4,
      "step": 1,
      "default": 3,
      "label": "Videos per row (desktop)"
    },
    {
      "type": "range",
      "id": "columns_tablet",
      "min": 1,
      "max": 3,
      "step": 1,
      "default": 2,
      "label": "Videos per row (tablet)"
    },
    {
      "type": "range",
      "id": "columns_mobile",
      "min": 1,
      "max": 3,
      "step": 1,
      "default": 1,
      "label": "Videos per row (mobile)"
    },
    {
      "type": "header",
      "content": "Section Padding"
    },
    {
      "type": "range",
      "id": "padding_top",
      "min": 0,
      "max": 100,
      "step": 4,
      "unit": "px",
      "label": "t:sections.all.padding.padding_top",
      "default": 36
    },
    {
      "type": "range",
      "id": "padding_bottom",
      "min": 0,
      "max": 100,
      "step": 4,
      "unit": "px",
      "label": "t:sections.all.padding.padding_bottom",
      "default": 36
    }
  ],
  "blocks": [
    {
      "type": "video",
      "name": "Video",
      "settings": [
        {
          "type": "text",
          "id": "title",
          "default": "Video title",
          "label": "Title"
        },
        {
          "type": "richtext",
          "id": "description",
          "default": "<p>Brief description of the video content</p>",
          "label": "Description"
        },
        {
          "type": "header",
          "content": "Video Source"
        },
        {
          "type": "checkbox",
          "id": "use_metafield",
          "label": "Use metafield for video URL",
          "default": false,
          "info": "Pull video URL from a product metafield"
        },
        {
          "type": "text",
          "id": "metafield_path",
          "label": "Metafield path",
          "default": "custom.video_url",
          "info": "Format: namespace.key (e.g., custom.video_url)",
          "placeholder": "custom.video_url"
        },
        {
          "type": "paragraph",
          "content": "The metafield should contain a YouTube or Vimeo URL"
        },
        {
          "type": "video_url",
          "id": "video_url",
          "accept": ["youtube", "vimeo"],
          "label": "Video URL",
          "info": "YouTube or Vimeo link (used if metafield is not set)"
        },
        {
          "type": "header",
          "content": "OR upload your own video"
        },
        {
          "type": "video",
          "id": "video",
          "label": "Self-hosted video",
          "info": "MP4 format recommended for best compatibility"
        },
        {
          "type": "header",
          "content": "Thumbnail Options"
        },
        {
          "type": "checkbox",
          "id": "use_appvideo_thumbnail",
          "label": "Use App Video Thumbnail metafield",
          "default": false,
          "info": "Use thumbnail from custom.appvideo_thumbnail metafield"
        },
        {
          "type": "image_picker",
          "id": "cover_image",
          "label": "Cover image",
          "info": "Shown until the video plays. 16:9 aspect ratio recommended."
        }
      ]
    }
  ],
  "presets": [
    {
      "name": "Video Reel",
      "blocks": [
        {
          "type": "video"
        },
        {
          "type": "video"
        },
        {
          "type": "video"
        }
      ]
    }
  ]
}
{% endschema %}