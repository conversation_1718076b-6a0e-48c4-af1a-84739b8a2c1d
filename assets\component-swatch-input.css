/* swatch-input lives in its own file for reusability of the swatch in other areas than the product form context */
.swatch-input__input + .swatch-input__label {
  --swatch-input--size: 4.4rem;
  --swatch-input--border-radius: 50%;

  display: inline-block;
  border-radius: var(--swatch-input--border-radius);
  cursor: pointer;
  outline-offset: 0.2rem;
  forced-color-adjust: none;
}

.swatch-input__input + .swatch-input__label.swatch-input__label--square {
  --swatch-input--border-radius: 0.2rem;
}

.swatch-input__input + .swatch-input__label:hover {
  outline: 0.2rem solid rgba(var(--color-foreground), 0.4);
}

.swatch-input__input:active + .swatch-input__label,
.swatch-input__input:checked + .swatch-input__label {
  outline: 0.1rem solid rgb(var(--color-foreground));
}

/* Visually disabled */
.swatch-input__input.disabled:not(:active):not(:checked) + .swatch-input__label:hover {
  outline: none;
}

/* Focus visible */
.swatch-input__input:focus-visible + .swatch-input__label {
  box-shadow: 0 0 0 0.5rem rgb(var(--color-background)), 0 0 0 0.7rem rgba(var(--color-foreground), 0.55);
}

/* Overrides for swatch snippet when used inside disabled swatch-input */
.swatch-input__input:disabled + .swatch-input__label > .swatch,
.swatch-input__input.disabled + .swatch-input__label > .swatch {
  position: relative;
  overflow: hidden;
}

/* Display white semi-transparent overlay over swatch when input is disabled */
.swatch-input__input:disabled + .swatch-input__label > .swatch::before,
.swatch-input__input.disabled + .swatch-input__label > .swatch::before {
  content: '';
  position: absolute;
  inset: 0;
  background: rgba(250, 250, 250, 0.5);
}

/* Display crossed out line over swatch when input is disabled */
.swatch-input__input:disabled + .swatch-input__label > .swatch::after,
.swatch-input__input.disabled + .swatch-input__label > .swatch::after {
  /* Diagonal of a square = length of the side * sqrt(2)  */
  --diagonal--size: calc(var(--swatch-input--size) * 1.414);
  --crossed-line--size: 0.1rem;
  --crossed-line--color: rgb(0, 0, 0);

  content: '';
  position: absolute;
  bottom: calc(var(--crossed-line--size) * -0.5);
  left: 0;
  width: var(--diagonal--size);
  height: var(--crossed-line--size);
  background-color: var(--crossed-line--color);
  transform: rotate(-45deg);
  transform-origin: left;
}
