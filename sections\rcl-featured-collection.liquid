{{ 'component-card.css' | asset_url | stylesheet_tag }}
{{ 'component-price.css' | asset_url | stylesheet_tag }}
{{ 'component-slider.css' | asset_url | stylesheet_tag }}

{%- if section.settings.enable_quick_add -%}
  {{ 'quick-add.css' | asset_url | stylesheet_tag }}

  <script src="{{ 'quick-add.js' | asset_url }}" defer="defer"></script>
  <script src="{{ 'product-form.js' | asset_url }}" defer="defer"></script>
{%- endif -%}

{% style %}
  .featured-collection-{{ section.id }} {
    padding-top: {{ section.settings.padding_top | times: 0.75 | round: 0 }}px;
    padding-bottom: {{ section.settings.padding_bottom | times: 0.75 | round: 0 }}px;
    background-color: {{ section.settings.background_color }};
    position: relative;
    overflow: hidden;
  }

  @media screen and (min-width: 750px) {
    .featured-collection-{{ section.id }} {
      padding-top: {{ section.settings.padding_top }}px;
      padding-bottom: {{ section.settings.padding_bottom }}px;
    }
  }

  .featured-collection__inner {
    position: relative;
    z-index: 1;
  }

  .featured-collection-{{ section.id }} .section-header {
    margin-bottom: 4.5rem;
  }

  .featured-collection__grid {
    list-style: none;
    padding: 0;
    margin: 0;
    display: grid;
    grid-template-columns: repeat({{ section.settings.columns_mobile }}, 1fr);
    gap: {{ section.settings.product_spacing_mobile }}px;
  }

  @media screen and (min-width: 750px) {
    .featured-collection__grid {
      grid-template-columns: repeat({{ section.settings.columns_desktop }}, 1fr);
      gap: {{ section.settings.product_spacing }}px;
    }
  }

  .featured-collection__card {
    position: relative;
    background: {{ settings.colors_background_1 }};
    height: 100%;
    transition: transform 0.4s cubic-bezier(0.16, 1, 0.3, 1);
  }

  .featured-collection__card .card {
    height: 100%;
    display: flex;
    flex-direction: column;
    background: transparent;
  }

  .featured-collection__card .card__media {
    overflow: hidden;
    border-radius: {% if section.settings.card_style == 'elevated' %}12px{% else %}0{% endif %};
  }

  .featured-collection__card .card__content {
    padding: 2rem 1.5rem;
    text-align: {{ section.settings.text_alignment }};
  }

  .featured-collection__card:hover {
    transform: translateY(-5px);
  }

  .featured-collection__card:hover .card__media img {
    transform: scale(1.03);
  }

  .card__media img {
    transition: transform 0.8s cubic-bezier(0.23, 1, 0.32, 1);
  }

  .featured-collection__view-all {
    margin-top: 5rem;
    text-align: center;
  }

  .featured-collection-{{ section.id }} .quick-add__submit {
    color: {{ section.settings.view_all_text_color }};
    background-color: {{ section.settings.view_all_background_color }}
  }

  .featured-collection__view-button {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    min-width: 220px;
    background-color: {{ section.settings.view_all_background_color }};
    border-radius: 4px;
    color: {{ section.settings.view_all_text_color }};
    text-decoration: none;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    border: 2px solid transparent;
  }

  .featured-collection__view-button:hover {
    background-color: {{ section.settings.accent_color }};
    color: #fff;
    transform: translateY(-2px);
    box-shadow: 0 10px 20px rgba({{ section.settings.accent_color | color_to_rgb }}, 0.2);
  }

  {% if section.settings.show_decorative_elements %}
    .featured-collection__accent {
      position: absolute;
      background: {{ section.settings.accent_color | color_modify: 'alpha', 0.07 }};
      border-radius: 50%;
      filter: blur(60px);
      z-index: 0;
    }

    .featured-collection__accent--top {
      top: -20%;
      right: -10%;
      width: 400px;
      height: 400px;
    }

    .featured-collection__accent--bottom {
      bottom: -30%;
      left: -15%;
      width: 500px;
      height: 500px;
    }
  {% endif %}

  @media screen and (max-width: 749px) {
    .featured-collection__grid {
      grid-template-columns: repeat(2, 1fr);
    }

    .featured-collection__card .card__content {
      padding: 1.5rem 1rem;
    }
  }
{% endstyle %}

<section class="featured-collection-{{ section.id }}" id="section-{{ section.id }}">
  {% if section.settings.show_decorative_elements %}
    <div class="featured-collection__accent featured-collection__accent--top"></div>
    <div class="featured-collection__accent featured-collection__accent--bottom"></div>
  {% endif %}
  <div class="page-width featured-collection__inner">
    <div class="section-header text-center">
      <h2 class="section-title animation slide-up" style="color: {{ section.settings.title_color }}; margin-bottom: 2rem;">
        {{ section.settings.title | escape }}
      </h2>
    </div>
    <ul class="featured-collection__grid">
      {%- for product in section.settings.collection.products limit: section.settings.products_to_show -%}
        <li class="featured-collection__card">
          {% render 'card-product',
            card_product: product,
            media_aspect_ratio: section.settings.image_ratio,
            show_secondary_image: section.settings.show_secondary_image,
            show_vendor: section.settings.show_vendor,
            show_rating: section.settings.show_rating,
            show_quick_add: section.settings.enable_quick_add,
            section_id: section.id
          %}
        </li>
      {%- else -%}
        {%- for i in (1..4) -%}
          <li class="featured-collection__card">
            {% render 'card-product', show_vendor: section.settings.show_vendor %}
          </li>
        {%- endfor -%}
      {%- endfor -%}
    </ul>

    {%- if section.settings.show_view_all -%}
      <div class="featured-collection__view-all">
        <a href="{{ section.settings.collection.url }}" class="rcl-button featured-collection__view-button">
          {{ 'sections.featured_collection.view_all' | t }}
        </a>
      </div>
    {%- endif -%}
  </div>
</section>
{% if section.settings.enable_animations %}
  <script>
    document.addEventListener('DOMContentLoaded', function() {
      const animatedElements = document.querySelectorAll('.animate');
      const observer = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
          if (entry.isIntersecting) {
            const element = entry.target;
            const delay = element.dataset.delay || 0;

            setTimeout(() => {
              element.classList.add('in-view');
            }, delay);

            observer.unobserve(element);
          }
        });
      }, { threshold: 0.15 });

      animatedElements.forEach(element => {
        observer.observe(element);
      });
    });
  </script>
{% endif %}

{% schema %}
{
  "name": "Featured Collection",
  "tag": "section",
  "class": "section",
  "settings": [
    {
      "type": "text",
      "id": "title",
      "default": "Featured Collection",
      "label": "Title"
    },
    {
      "type": "color",
      "id": "title_color",
      "default": "#d1b073",
      "label": "Title color"
    },
    {
      "type": "color",
      "id": "background_color",
      "default": "#ffffff",
      "label": "Background color"
    },
    {
      "type": "color",
      "id": "text_color",
      "default": "#222228",
      "label": "Text color"
    },
    {
      "type": "color",
      "id": "accent_color",
      "default": "#d1b073",
      "label": "Accent color"
    },
    {
      "type": "collection",
      "id": "collection",
      "label": "Collection"
    },
    {
      "type": "checkbox",
      "id": "enable_animations",
      "label": "Enable animations",
      "default": true,
      "info": "Subtle animations that won't impact page loading speed"
    },
    {
      "type": "checkbox",
      "id": "show_decorative_elements",
      "label": "Show decorative elements",
      "default": true,
      "info": "Subtle design elements that enhance visual appeal"
    },
    {
      "type": "range",
      "id": "products_to_show",
      "min": 2,
      "max": 12,
      "step": 1,
      "default": 4,
      "label": "Products to show"
    },
    {
      "type": "select",
      "id": "card_style",
      "options": [
        {
          "value": "standard",
          "label": "Standard"
        },
        {
          "value": "minimal",
          "label": "Minimal"
        },
        {
          "value": "elevated",
          "label": "Elevated"
        },
        {
          "value": "bordered",
          "label": "Bordered"
        }
      ],
      "default": "elevated",
      "label": "Card style"
    },
    {
      "type": "color",
      "id": "border_color",
      "default": "#d1b073",
      "label": "Border color",
      "info": "For bordered card style"
    },
    {
      "type": "range",
      "id": "border_radius",
      "min": 0,
      "max": 20,
      "step": 1,
      "default": 8,
      "label": "Border radius",
      "info": "For bordered card style"
    },
    {
      "type": "range",
      "id": "columns_desktop",
      "min": 1,
      "max": 5,
      "step": 1,
      "default": 4,
      "label": "Columns desktop"
    },
    {
      "type": "range",
      "id": "product_spacing",
      "min": 10,
      "max": 50,
      "step": 5,
      "default": 25,
      "label": "Product spacing (desktop)"
    },
    {
      "type": "select",
      "id": "image_ratio",
      "options": [
        {
          "value": "adapt",
          "label": "Adapt to image"
        },
        {
          "value": "portrait",
          "label": "Portrait"
        },
        {
          "value": "square",
          "label": "Square"
        }
      ],
      "default": "adapt",
      "label": "Image ratio"
    },
    {
      "type": "checkbox",
      "id": "show_secondary_image",
      "default": true,
      "label": "Show second image on hover"
    },
    {
      "type": "checkbox",
      "id": "show_vendor",
      "default": false,
      "label": "Show vendor"
    },
    {
      "type": "checkbox",
      "id": "show_rating",
      "default": true,
      "label": "Show product rating"
    },
    {
      "type": "checkbox",
      "id": "enable_quick_add",
      "default": true,
      "label": "Enable quick add"
    },
    {
      "type": "header",
      "content": "View All Button"
    },
    {
      "type": "checkbox",
      "id": "show_view_all",
      "default": true,
      "label": "Show view all button"
    },
    {
      "type": "color",
      "id": "view_all_background_color",
      "default": "#d1b073",
      "label": "Button background color"
    },
    {
      "type": "color",
      "id": "view_all_text_color",
      "default": "#ffffff",
      "label": "Button text color"
    },
    {
      "type": "header",
      "content": "Mobile Settings"
    },
    {
      "type": "select",
      "id": "columns_mobile",
      "options": [
        {
          "value": "1",
          "label": "1 column"
        },
        {
          "value": "2",
          "label": "2 columns"
        }
      ],
      "default": "2",
      "label": "Columns mobile"
    },
    {
      "type": "range",
      "id": "product_spacing_mobile",
      "min": 5,
      "max": 30,
      "step": 5,
      "default": 15,
      "label": "Product spacing (mobile)"
    },
    {
      "type": "header",
      "content": "Section Padding"
    },
    {
      "type": "range",
      "id": "padding_top",
      "min": 0,
      "max": 100,
      "step": 4,
      "unit": "px",
      "label": "Padding top",
      "default": 48
    },
    {
      "type": "range",
      "id": "padding_bottom",
      "min": 0,
      "max": 100,
      "step": 4,
      "unit": "px",
      "label": "Padding bottom",
      "default": 48
    }
  ],
  "presets": [
    {
      "name": "Featured Collection",
      "settings": {
        "title": "Featured Collection",
        "collection": "",
        "products_to_show": 4,
        "columns_desktop": 4,
        "enable_animations": true,
        "show_decorative_elements": true,
        "card_style": "elevated",
        "show_view_all": true,
        "title_color": "#d1b073",
        "accent_color": "#d1b073",
        "background_color": "#ffffff",
        "text_color": "#222228"
      }
    }
  ]
}
{% endschema %}
