{{ 'component-card.css' | asset_url | stylesheet_tag }}
{{ 'component-price.css' | asset_url | stylesheet_tag }}
{{ 'section-related-products.css' | asset_url | stylesheet_tag }}
{{ 'section-enhanced-related-products.css' | asset_url | stylesheet_tag }}
<script src="{{ 'enhanced-related-products.js' | asset_url }}" defer="defer"></script>

{% if section.settings.image_shape == 'blob' %}
  {{ 'mask-blobs.css' | asset_url | stylesheet_tag }}
{%- endif -%}

{%- style -%}
  .section-{{ section.id }}-padding {
    padding-top: {{ section.settings.padding_top | times: 0.75 | round: 0 }}px;
    padding-bottom: {{ section.settings.padding_bottom | times: 0.75 | round: 0 }}px;
  }

  @media screen and (min-width: 750px) {
    .section-{{ section.id }}-padding {
      padding-top: {{ section.settings.padding_top }}px;
      padding-bottom: {{ section.settings.padding_bottom }}px;
    }
  }
{%- endstyle -%}

<div class="color-{{ section.settings.color_scheme }} gradient">
  <div class="enhanced-related-products page-width section-{{ section.id }}-padding isolate{% if settings.animations_reveal_on_scroll %} scroll-trigger animate--slide-in{% endif %}">
    {% comment %} Enhanced related products logic {% endcomment %}
    {% assign related_products = "" | split: "" %}
    {% assign related_product_ids = "" | split: "" %}
    {% assign current_product_tags = product.tags | map: 'handle' %}

    {% comment %} Step 1: Get products from the same collections {% endcomment %}
    {% for collection in product.collections %}
      {% for collection_product in collection.products limit: 20 %}
        {% if collection_product.id != product.id %}
          {% unless related_product_ids contains collection_product.id %}
            {% assign score = 0 %}

            {% comment %} Score: Same collection {% endcomment %}
            {% assign score = score | plus: section.settings.collection_weight %}

            {% comment %} Score: Same product type {% endcomment %}
            {% if collection_product.type == product.type %}
              {% assign score = score | plus: section.settings.product_type_weight %}
            {% endif %}

            {% comment %} Score: Shared tags {% endcomment %}
            {% assign product_tags = collection_product.tags | map: 'handle' %}
            {% assign shared_tags_count = 0 %}

            {% for tag in product_tags %}
              {% if current_product_tags contains tag %}
                {% assign shared_tags_count = shared_tags_count | plus: 1 %}
              {% endif %}
            {% endfor %}

            {% assign tag_score = shared_tags_count | times: section.settings.tag_weight %}
            {% assign score = score | plus: tag_score %}

            {% comment %} Score: Metafields similarity {% endcomment %}
            {% if product.metafields.custom.use != blank and collection_product.metafields.custom.use != blank %}
              {% if product.metafields.custom.use == collection_product.metafields.custom.use %}
                {% assign score = score | plus: section.settings.metafield_weight %}
              {% endif %}
            {% endif %}

            {% if product.metafields.custom.composition != blank and collection_product.metafields.custom.composition != blank %}
              {% if product.metafields.custom.composition == collection_product.metafields.custom.composition %}
                {% assign score = score | plus: section.settings.metafield_weight %}
              {% endif %}
            {% endif %}

            {% if product.metafields.custom.benefits != blank and collection_product.metafields.custom.benefits != blank %}
              {% if product.metafields.custom.benefits == collection_product.metafields.custom.benefits %}
                {% assign score = score | plus: section.settings.metafield_weight %}
              {% endif %}
            {% endif %}

            {% if product.metafields.custom.effect != blank and collection_product.metafields.custom.effect != blank %}
              {% if product.metafields.custom.effect == collection_product.metafields.custom.effect %}
                {% assign score = score | plus: section.settings.metafield_weight %}
              {% endif %}
            {% endif %}

            {% comment %} Add product with its score to the related products array {% endcomment %}
            {% assign related_product_with_score = collection_product | append: '::' | append: score %}
            {% assign related_products = related_products | concat: related_product_with_score | split: ',' %}
            {% assign related_product_ids = related_product_ids | concat: collection_product.id | split: ',' %}
          {% endunless %}
        {% endif %}
      {% endfor %}
    {% endfor %}

    {% comment %} Step 2: If we don't have enough products, add more from the same product type {% endcomment %}
    {% if related_products.size < section.settings.products_to_show %}
      {% for collection in collections %}
        {% for collection_product in collection.products limit: 20 %}
          {% if collection_product.id != product.id and collection_product.type == product.type %}
            {% unless related_product_ids contains collection_product.id %}
              {% assign score = 0 %}

              {% comment %} Score: Same product type {% endcomment %}
              {% assign score = score | plus: section.settings.product_type_weight %}

              {% comment %} Score: Shared tags {% endcomment %}
              {% assign product_tags = collection_product.tags | map: 'handle' %}
              {% assign shared_tags_count = 0 %}

              {% for tag in product_tags %}
                {% if current_product_tags contains tag %}
                  {% assign shared_tags_count = shared_tags_count | plus: 1 %}
                {% endif %}
              {% endfor %}

              {% assign tag_score = shared_tags_count | times: section.settings.tag_weight %}
              {% assign score = score | plus: tag_score %}

              {% comment %} Add product with its score to the related products array {% endcomment %}
              {% assign related_product_with_score = collection_product | append: '::' | append: score %}
              {% assign related_products = related_products | concat: related_product_with_score | split: ',' %}
              {% assign related_product_ids = related_product_ids | concat: collection_product.id | split: ',' %}

              {% if related_products.size >= section.settings.products_to_show %}
                {% break %}
              {% endif %}
            {% endunless %}
          {% endif %}
        {% endfor %}

        {% if related_products.size >= section.settings.products_to_show %}
          {% break %}
        {% endif %}
      {% endfor %}
    {% endif %}

    {% comment %} Step 3: Sort related products by score (descending) {% endcomment %}
    {% assign sorted_related_products = "" | split: "" %}

    {% comment %} First, create an array of scores for sorting {% endcomment %}
    {% assign scores = "" | split: "" %}
    {% for related_product_with_score in related_products %}
      {% assign parts = related_product_with_score | split: '::' %}
      {% assign score = parts[1] | plus: 0 %}
      {% assign scores = scores | concat: score | split: ',' %}
    {% endfor %}

    {% comment %} Sort scores in descending order {% endcomment %}
    {% assign sorted_scores = scores | sort | reverse %}

    {% comment %} Create sorted related products array based on scores {% endcomment %}
    {% for sorted_score in sorted_scores %}
      {% for related_product_with_score in related_products %}
        {% assign parts = related_product_with_score | split: '::' %}
        {% assign related_product = parts[0] %}
        {% assign score = parts[1] | plus: 0 %}

        {% if score == sorted_score %}
          {% unless sorted_related_products contains related_product %}
            {% assign sorted_related_products = sorted_related_products | concat: related_product | split: ',' %}
            {% break %}
          {% endunless %}
        {% endif %}
      {% endfor %}

      {% if sorted_related_products.size >= section.settings.products_to_show %}
        {% break %}
      {% endif %}
    {% endfor %}

    {% comment %} Display related products {% endcomment %}
    {% if sorted_related_products.size > 0 %}
      <h2 class="related-products__heading inline-richtext {{ section.settings.heading_size }}">
        {{ section.settings.heading }}
      </h2>
      <ul
        class="grid product-grid grid--{{ section.settings.columns_desktop }}-col-desktop grid--{{ section.settings.columns_mobile }}-col-tablet-down"
        role="list"
      >
        {% for related_product in sorted_related_products limit: section.settings.products_to_show %}
          <li class="grid__item">
            {% render 'card-product',
              card_product: related_product,
              media_aspect_ratio: section.settings.image_ratio,
              image_shape: section.settings.image_shape,
              show_secondary_image: section.settings.show_secondary_image,
              show_vendor: section.settings.show_vendor,
              show_rating: section.settings.show_rating
            %}
          </li>
        {% endfor %}
      </ul>
    {% endif %}
  </div>
  {% if section.settings.image_shape == 'arch' %}
    {% render 'mask-arch' %}
  {%- endif -%}
</div>

{% schema %}
{
  "name": "Enhanced Related Products",
  "tag": "section",
  "class": "section",
  "settings": [
    {
      "type": "paragraph",
      "content": "This section uses an enhanced algorithm to show truly related products based on collections, tags, product type, and metafields."
    },
    {
      "type": "inline_richtext",
      "id": "heading",
      "default": "You may also like",
      "label": "Heading"
    },
    {
      "type": "select",
      "id": "heading_size",
      "options": [
        {
          "value": "h2",
          "label": "Small"
        },
        {
          "value": "h1",
          "label": "Medium"
        },
        {
          "value": "h0",
          "label": "Large"
        }
      ],
      "default": "h1",
      "label": "Heading size"
    },
    {
      "type": "range",
      "id": "products_to_show",
      "min": 2,
      "max": 10,
      "step": 1,
      "default": 4,
      "label": "Maximum products to show"
    },
    {
      "type": "range",
      "id": "columns_desktop",
      "min": 1,
      "max": 5,
      "step": 1,
      "default": 4,
      "label": "Number of columns on desktop"
    },
    {
      "type": "header",
      "content": "Relevance Weights"
    },
    {
      "type": "range",
      "id": "collection_weight",
      "min": 1,
      "max": 10,
      "step": 1,
      "default": 3,
      "label": "Same collection weight"
    },
    {
      "type": "range",
      "id": "product_type_weight",
      "min": 1,
      "max": 10,
      "step": 1,
      "default": 5,
      "label": "Same product type weight"
    },
    {
      "type": "range",
      "id": "tag_weight",
      "min": 1,
      "max": 10,
      "step": 1,
      "default": 2,
      "label": "Per shared tag weight"
    },
    {
      "type": "range",
      "id": "metafield_weight",
      "min": 1,
      "max": 10,
      "step": 1,
      "default": 4,
      "label": "Same metafield value weight"
    },
    {
      "type": "color_scheme",
      "id": "color_scheme",
      "label": "Color scheme",
      "info": "Affects card backgrounds",
      "default": "scheme-1"
    },
    {
      "type": "header",
      "content": "Product Cards"
    },
    {
      "type": "select",
      "id": "image_ratio",
      "options": [
        {
          "value": "adapt",
          "label": "Adapt to image"
        },
        {
          "value": "portrait",
          "label": "Portrait"
        },
        {
          "value": "square",
          "label": "Square"
        }
      ],
      "default": "adapt",
      "label": "Image ratio"
    },
    {
      "type": "select",
      "id": "image_shape",
      "options": [
        {
          "value": "default",
          "label": "Default"
        },
        {
          "value": "arch",
          "label": "Arch"
        },
        {
          "value": "blob",
          "label": "Blob"
        },
        {
          "value": "round",
          "label": "Round"
        }
      ],
      "default": "default",
      "label": "Image shape",
      "info": "To use Arch or Blob shapes, update your theme"
    },
    {
      "type": "checkbox",
      "id": "show_secondary_image",
      "default": false,
      "label": "Show second image on hover"
    },
    {
      "type": "checkbox",
      "id": "show_vendor",
      "default": false,
      "label": "Show vendor"
    },
    {
      "type": "checkbox",
      "id": "show_rating",
      "default": false,
      "label": "Show product rating",
      "info": "To display a rating, add a product rating app"
    },
    {
      "type": "header",
      "content": "Mobile Layout"
    },
    {
      "type": "select",
      "id": "columns_mobile",
      "default": "2",
      "label": "Number of columns on mobile",
      "options": [
        {
          "value": "1",
          "label": "1 column"
        },
        {
          "value": "2",
          "label": "2 columns"
        }
      ]
    },
    {
      "type": "header",
      "content": "Section Padding"
    },
    {
      "type": "range",
      "id": "padding_top",
      "min": 0,
      "max": 100,
      "step": 4,
      "unit": "px",
      "label": "Padding top",
      "default": 36
    },
    {
      "type": "range",
      "id": "padding_bottom",
      "min": 0,
      "max": 100,
      "step": 4,
      "unit": "px",
      "label": "Padding bottom",
      "default": 36
    }
  ],
  "presets": [
    {
      "name": "Enhanced Related Products",
      "category": "Product Information"
    }
  ]
}
{% endschema %}
