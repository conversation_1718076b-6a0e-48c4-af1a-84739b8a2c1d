{% schema %}
    {
      "name": "European Craftsmanship",
      "tag": "section",
      "class": "section",
      "settings": [
        {
          "type": "text",
          "id": "title",
          "label": "Section Title",
          "default": "European Craftsmanship"
        },
        {
          "type": "color",
          "id": "title_color",
          "label": "Title Color",
          "default": "#d1b073"
        },
        {
          "type": "color",
          "id": "text_color",
          "label": "Text Color",
          "default": "#222228"
        },
        {
          "type": "color",
          "id": "background_color",
          "label": "Background Color",
          "default": "#f8f8f8"
        },
        {
          "type": "color",
          "id": "accent_color",
          "label": "Accent Color",
          "default": "#d1b073"
        },
        {
          "type": "image_picker",
          "id": "background_image",
          "label": "Background Pattern (Optional)"
        }
      ],
      "blocks": [
        {
          "type": "origin_country",
          "name": "Origin Country",
          "settings": [
            {
              "type": "text",
              "id": "country",
              "label": "Country",
              "default": "Italy"
            },
            {
              "type": "image_picker",
              "id": "flag",
              "label": "Flag Icon"
            },
            {
              "type": "image_picker",
              "id": "material_image",
              "label": "Material Image"
            },
            {
              "type": "text",
              "id": "material_name",
              "label": "Material Name",
              "default": "Italian Lime"
            },
            {
              "type": "textarea",
              "id": "description",
              "label": "Description",
              "default": "Lorem ipsum dolor sit amet, consectetur adipiscing elit. Sed dapibus, magna sit amet fermentum blandit, massa orci tempor mi."
            }
          ]
        },
        {
          "type": "central_image",
          "name": "Central Image",
          "limit": 1,
          "settings": [
            {
              "type": "image_picker",
              "id": "image",
              "label": "Central Image"
            },
            {
              "type": "text",
              "id": "tagline",
              "label": "Tagline",
              "default": "Finest European Materials"
            }
          ]
        }
      ],
      "presets": [
        {
          "name": "European Craftsmanship",
          "blocks": [
            {
              "type": "origin_country"
            },
            {
              "type": "origin_country"
            },
            {
              "type": "origin_country"
            },
            {
              "type": "origin_country"
            },
            {
              "type": "central_image"
            }
          ]
        }
      ]
    }
    {% endschema %}
    
    <div class="european-craftsmanship-section" 
         style="background-color: {{ section.settings.background_color }}; 
                color: {{ section.settings.text_color }};">
      
      {% if section.settings.background_image != blank %}
        <div class="craftsmanship-bg">
          {{ section.settings.background_image | image_url: width: 1500 | image_tag:
            loading: 'lazy',
            class: 'craftsmanship-bg-image'
          }}
        </div>
      {% endif %}
      
      <div class="page-width">
        <h2 class="section-title" style="color: {{ section.settings.title_color }};">
          {{ section.settings.title }}
        </h2>
    
        <div class="craftsmanship-container">
          {% assign central_image_block = section.blocks | where: "type", "central_image" | first %}
          {% assign origin_countries = section.blocks | where: "type", "origin_country" %}
          
          <div class="countries-grid">
            {% for block in origin_countries %}
              <div class="country-item" {{ block.shopify_attributes }}>
                <div class="country-card" style="border-color: {{ section.settings.accent_color }};">
                  <div class="country-header">
                    {% if block.settings.flag != blank %}
                      <div class="country-flag">
                        {{ block.settings.flag | image_url: width: 60 | image_tag:
                          loading: 'lazy',
                          width: 30,
                          height: 30
                        }}
                      </div>
                    {% endif %}
                    
                    <div class="country-name" style="color: {{ section.settings.accent_color }};">
                      {{ block.settings.country }}
                    </div>
                  </div>
                  
                  {% if block.settings.material_image != blank %}
                    <div class="material-image">
                      {{ block.settings.material_image | image_url: width: 400 | image_tag:
                        loading: 'lazy',
                        class: 'material-img',
                        widths: '275, 400, 550',
                        sizes: '(min-width: 750px) 25vw, 50vw'
                      }}
                    </div>
                  {% else %}
                    <div class="material-image placeholder">
                      {{ 'image' | placeholder_svg_tag: 'placeholder-svg material-img' }}
                    </div>
                  {% endif %}
                  
                  <div class="material-name">
                    {{ block.settings.material_name }}
                  </div>
                  
                  <div class="material-description">
                    {{ block.settings.description }}
                  </div>
                </div>
              </div>
            {% endfor %}
            
            {% if central_image_block != nil %}
              <div class="central-image-container" {{ central_image_block.shopify_attributes }}>
                {% if central_image_block.settings.image != blank %}
                  <div class="central-image">
                    {{ central_image_block.settings.image | image_url: width: 500 | image_tag:
                      loading: 'lazy',
                      class: 'central-img',
                      widths: '275, 400, 500',
                      sizes: '(min-width: 750px) 30vw, 50vw'
                    }}
                  </div>
                {% else %}
                  <div class="central-image placeholder">
                    {{ 'image' | placeholder_svg_tag: 'placeholder-svg central-img' }}
                  </div>
                {% endif %}
                
                {% if central_image_block.settings.tagline != blank %}
                  <div class="central-tagline" style="color: {{ section.settings.accent_color }};">
                    {{ central_image_block.settings.tagline }}
                  </div>
                {% endif %}
              </div>
            {% endif %}
          </div>
        </div>
      </div>
    </div>
    
    <style>
      .european-craftsmanship-section {
        padding: 80px 0;
        position: relative;
        overflow: hidden;
      }
      
      .craftsmanship-bg {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        opacity: 0.1;
        z-index: 0;
      }
      
      .craftsmanship-bg-image {
        width: 100%;
        height: 100%;
        object-fit: cover;
      }
      
      .page-width {
        position: relative;
        z-index: 1;
      }
      
      .section-title {
        margin-bottom: 60px;
        font-size: calc(var(--font-heading-scale) * 2.5rem);
        letter-spacing: 0.05em;
        text-transform: uppercase;
        text-align: center;
        line-height: 1.2;
      }
      
      .craftsmanship-container {
        position: relative;
      }
      
      .countries-grid {
        display: grid;
        grid-template-columns: repeat(4, 1fr);
        grid-template-rows: auto;
        gap: 30px;
      }
      
      .central-image-container {
        grid-column: 2 / 4;
        grid-row: 1;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
      }
      
      .country-item:nth-child(1),
      .country-item:nth-child(2) {
        grid-row: 2;
      }
      
      .country-item:nth-child(3),
      .country-item:nth-child(4) {
        grid-row: 3;
      }
      
      .country-item:nth-child(1),
      .country-item:nth-child(3) {
        grid-column: 1 / 3;
      }
      
      .country-item:nth-child(2),
      .country-item:nth-child(4) {
        grid-column: 3 / 5;
      }
      
      /* Country Card */
      .country-card {
        background-color: #ffffff;
        border-radius: 10px;
        overflow: hidden;
        box-shadow: 0 10px 30px rgba(0, 0, 0, 0.05);
        padding: 25px;
        height: 100%;
        border-top: 4px solid;
        transition: transform 0.3s ease, box-shadow 0.3s ease;
      }
      
      .country-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 15px 40px rgba(0, 0, 0, 0.1);
      }
      
      .country-header {
        display: flex;
        align-items: center;
        margin-bottom: 20px;
      }
      
      .country-flag {
        margin-right: 10px;
        display: flex;
        align-items: center;
      }
      
      .country-name {
        font-size: 1.2rem;
        font-weight: 600;
        letter-spacing: 0.05em;
        text-transform: uppercase;
      }
      
      .material-image {
        width: 100%;
        height: 0;
        padding-bottom: 65%;
        position: relative;
        overflow: hidden;
        border-radius: 6px;
        margin-bottom: 15px;
      }
      
      .material-img,
      .material-image .placeholder-svg {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        object-fit: cover;
        transition: transform 0.6s ease;
      }
      
      .country-card:hover .material-img {
        transform: scale(1.05);
      }
      
      .material-name {
        font-size: 1.1rem;
        font-weight: 600;
        margin-bottom: 10px;
      }
      
      .material-description {
        font-size: 0.95rem;
        line-height: 1.5;
        color: rgba(34, 34, 40, 0.75);
      }
      
      /* Central Image */
      .central-image {
        width: 220px;
        height: 220px;
        border-radius: 50%;
        overflow: hidden;
        margin: 0 auto 20px;
        border: 5px solid {{ section.settings.accent_color }};
        position: relative;
        box-shadow: 0 10px 30px rgba(209, 176, 115, 0.3);
      }
      
      .central-img,
      .central-image .placeholder-svg {
        width: 100%;
        height: 100%;
        object-fit: cover;
      }
      
      .central-tagline {
        text-align: center;
        font-size: 1.5rem;
        font-weight: 600;
        letter-spacing: 0.05em;
        text-transform: uppercase;
      }
      
      /* Responsive Designs */
      @media screen and (max-width: 989px) {
        .countries-grid {
          grid-template-columns: 1fr 1fr;
        }
        
        .central-image-container {
          grid-column: 1 / 3;
          grid-row: 1;
          margin-bottom: 30px;
        }
        
        .country-item:nth-child(1) {
          grid-column: 1;
          grid-row: 2;
        }
        
        .country-item:nth-child(2) {
          grid-column: 2;
          grid-row: 2;
        }
        
        .country-item:nth-child(3) {
          grid-column: 1;
          grid-row: 3;
        }
        
        .country-item:nth-child(4) {
          grid-column: 2;
          grid-row: 3;
        }
      }
      
      @media screen and (max-width: 749px) {
        .european-craftsmanship-section {
          padding: 60px 0;
        }
        
        .section-title {
          font-size: calc(var(--font-heading-scale) * 1.8rem);
          margin-bottom: 40px;
        }
        
        .countries-grid {
          grid-template-columns: 1fr;
        }
        
        .central-image-container {
          grid-column: 1;
          grid-row: 1;
        }
        
        .country-item:nth-child(n) {
          grid-column: 1;
        }
        
        .country-item:nth-child(1) {
          grid-row: 2;
        }
        
        .country-item:nth-child(2) {
          grid-row: 3;
        }
        
        .country-item:nth-child(3) {
          grid-row: 4;
        }
        
        .country-item:nth-child(4) {
          grid-row: 5;
        }
        
        .central-image {
          width: 180px;
          height: 180px;
        }
        
        .central-tagline {
          font-size: 1.3rem;
        }
      }
    </style>