.visual-display {
  --visual-display__size: min(2.4rem, 100%);
  position: relative;
  width: var(--visual-display__size);
  max-width: 100%;
  border: 0.1rem solid rgba(var(--color-foreground), 0.2);
  aspect-ratio: 1/1;
}

.visual-display.empty {
  border-style: dashed;
}

.visual-display--presentation-swatch {
  --visual-display__size: min(2.4rem, 100%);

  border-radius: 100%;
  overflow: hidden;
}

.visual-display-parent .visual-display--presentation-swatch {
  outline-offset: 0.2rem;
}

/* Hover, active, and focus states */
:is(
    .visual-display-parent:hover .visual-display--presentation-swatch,
    .visual-display-parent.active .visual-display--presentation-swatch,
    .visual-display-parent:has(:focus-visible) .visual-display--presentation-swatch
  ) {
  outline-style: solid;
}

/* Active state */
.visual-display-parent.active .visual-display--presentation-swatch {
  outline-width: 0.2rem;
  outline-color: rgb(var(--color-foreground), 1);
}

/* Hover state */
.visual-display-parent:hover .visual-display--presentation-swatch {
  outline-width: 0.2rem;
  outline-color: rgb(var(--color-foreground), 0.4);
}

/* Focus state */
.visual-display-parent:has(:focus-visible) .visual-display--presentation-swatch {
  outline-width: 0.2rem;
  outline-color: rgb(var(--color-foreground), 0.4);
  box-shadow: 0 0 0 0.6rem rgb(var(--color-background)), 0 0 0 0.8rem rgba(var(--color-foreground), 0.5),
    0 0 1.2rem 0.4rem rgba(var(--color-foreground), 0.3);
}

/* Focus state for older browsers */
@supports not selector(:has(a, b)) {
  .visual-display-parent:focus-within .visual-display--presentation-swatch {
    outline-offset: 0.2rem;
    outline: 0.2rem solid rgb(var(--color-foreground), 0.4);
    box-shadow: 0 0 0 0.6rem rgb(var(--color-background)), 0 0 0 0.8rem rgba(var(--color-foreground), 0.5),
      0 0 1.2rem 0.4rem rgba(var(--color-foreground), 0.3);
  }
}

.visual-display-parent.disabled {
  opacity: 0.4;
  pointer-events: none;
}

/* Used to display the disabled dash */
.visual-display-parent.disabled .visual-display::after {
  display: block;
  content: '';

  /* 1.414 is not a magic number, it's the square root of 2, or the length of the diagonal */
  width: calc(var(--visual-display__size) * 1.414);
  border-bottom: 0.1rem solid rgb(var(--color-background-contrast));
  transform: rotate(-45deg);
  transform-origin: left;
}

.visual-display .visual-display__child {
  display: block;
  height: 100%;
  width: 100%;
  forced-color-adjust: none;
}

.visual-display--presentation-swatch .visual-display__image {
  object-fit: cover;
}
