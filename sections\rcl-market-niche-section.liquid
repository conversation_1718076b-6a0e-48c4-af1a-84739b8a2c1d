{% schema %}
    {
      "name": "Market Niche",
      "tag": "section",
      "class": "section",
      "settings": [
        {
          "type": "text",
          "id": "title",
          "label": "Section Title",
          "default": "Our Market Niche"
        },
        {
          "type": "color",
          "id": "title_color",
          "label": "Title Color",
          "default": "#d1b073"
        },
        {
          "type": "richtext",
          "id": "description",
          "label": "Section Description",
          "default": "<p>In recent years, we have occupied our niche in all segments of the market: starting from professional author's interiors, ending with more budgetary tender objects and simple repairs.</p>"
        },
        {
          "type": "color",
          "id": "text_color",
          "label": "Text Color",
          "default": "#ffffff"
        },
        {
          "type": "color",
          "id": "background_color",
          "label": "Background Color",
          "default": "#222228"
        },
        {
          "type": "color",
          "id": "card_background_color",
          "label": "Card Background Color",
          "default": "#ffffff"
        },
        {
          "type": "color",
          "id": "accent_color",
          "label": "Accent Color",
          "default": "#d1b073"
        },
        {
          "type": "image_picker",
          "id": "background_image",
          "label": "Background Image"
        },
        {
          "type": "range",
          "id": "overlay_opacity",
          "min": 0,
          "max": 95,
          "step": 5,
          "unit": "%",
          "label": "Overlay Opacity",
          "default": 80
        },
        {
          "type": "select",
          "id": "layout",
          "label": "Layout Style",
          "options": [
            {
              "value": "grid",
              "label": "Grid"
            },
            {
              "value": "carousel",
              "label": "Carousel"
            }
          ],
          "default": "grid"
        },
        {
          "type": "range",
          "id": "columns",
          "min": 1,
          "max": 3,
          "step": 1,
          "default": 3,
          "label": "Columns on Desktop"
        }
      ],
      "blocks": [
        {
          "type": "market_segment",
          "name": "Market Segment",
          "settings": [
            {
              "type": "text",
              "id": "title",
              "label": "Segment Title",
              "default": "Premium Interiors"
            },
            {
              "type": "color",
              "id": "title_color",
              "label": "Title Color",
              "default": "#222228"
            },
            {
              "type": "richtext",
              "id": "description",
              "label": "Segment Description",
              "default": "<p>We create unique decorative coatings for professional author's interiors and high-end projects with exceptional attention to detail.</p>"
            },
            {
              "type": "image_picker",
              "id": "icon",
              "label": "Segment Icon"
            },
            {
              "type": "color",
              "id": "card_accent_color",
              "label": "Card Accent Color",
              "default": "#d1b073"
            }
          ]
        }
      ],
      "presets": [
        {
          "name": "Market Niche",
          "blocks": [
            {
              "type": "market_segment"
            },
            {
              "type": "market_segment"
            },
            {
              "type": "market_segment"
            }
          ]
        }
      ]
    }
    {% endschema %}
    
    <div class="market-niche-section" style="color: {{ section.settings.text_color }};">
      {% if section.settings.background_image != blank %}
        <div class="market-niche-bg">
          {{ section.settings.background_image | image_url: width: 1500 | image_tag:
            loading: 'lazy',
            class: 'market-niche-bg-image'
          }}
          <div class="market-niche-overlay" 
               style="background-color: {{ section.settings.background_color }}; 
                      opacity: {{ section.settings.overlay_opacity | divided_by: 100.0 }};"></div>
        </div>
      {% else %}
        <div class="market-niche-bg-color" style="background-color: {{ section.settings.background_color }};"></div>
      {% endif %}
      
      <div class="page-width">
        <div class="section-header text-center">
          <h2 class="section-title" style="color: {{ section.settings.title_color }};">
            {{ section.settings.title }}
          </h2>
          
          <div class="section-description rte">
            {{ section.settings.description }}
          </div>
        </div>
    
        <div class="market-segments-container" 
             data-layout="{{ section.settings.layout }}" 
             data-columns="{{ section.settings.columns }}">
          
          {% for block in section.blocks %}
            {% if block.type == 'market_segment' %}
              <div class="market-segment-item" {{ block.shopify_attributes }}>
                <div class="market-segment-card" style="background-color: {{ section.settings.card_background_color }};">
                  <div class="market-segment-accent" style="background-color: {{ block.settings.card_accent_color }};"></div>
                  
                  {% if block.settings.icon != blank %}
                    <div class="market-segment-icon">
                      {{ block.settings.icon | image_url: width: 120 | image_tag:
                        loading: 'lazy',
                        width: 80,
                        height: 80
                      }}
                    </div>
                  {% endif %}
                  
                  <h3 class="market-segment-title" style="color: {{ block.settings.title_color }};">
                    {{ block.settings.title }}
                  </h3>
                  
                  <div class="market-segment-description rte">
                    {{ block.settings.description }}
                  </div>
                </div>
              </div>
            {% endif %}
          {% endfor %}
        </div>
        
        {% if section.settings.layout == 'carousel' %}
          <div class="market-carousel-navigation">
            <button class="market-carousel-prev" aria-label="Previous segment" style="color: {{ section.settings.accent_color }}; border-color: {{ section.settings.accent_color }};">
              <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M15 18L9 12L15 6" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
              </svg>
            </button>
            <button class="market-carousel-next" aria-label="Next segment" style="color: {{ section.settings.accent_color }}; border-color: {{ section.settings.accent_color }};">
              <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M9 18L15 12L9 6" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
              </svg>
            </button>
          </div>
        {% endif %}
      </div>
    </div>
    
    <style>
      .market-niche-section {
        position: relative;
        padding: 80px 0;
        overflow: hidden;
      }
      
      .market-niche-bg,
      .market-niche-bg-color {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        z-index: -1;
      }
      
      .market-niche-bg-image {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        object-fit: cover;
      }
      
      .market-niche-overlay {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
      }
      
      /* Grid Layout */
      .market-segments-container {
        position: relative;
        z-index: 1;
      }
      
      [data-layout="grid"] .market-segments-container {
        display: grid;
        grid-template-columns: repeat(var(--columns, 3), 1fr);
        gap: 30px;
      }
      
      /* Carousel Layout */
      [data-layout="carousel"] .market-segments-container {
        display: flex;
        overflow-x: auto;
        scroll-snap-type: x mandatory;
        scroll-behavior: smooth;
        -webkit-overflow-scrolling: touch;
        scrollbar-width: none; /* Firefox */
        -ms-overflow-style: none; /* IE and Edge */
        gap: 30px;
        padding-bottom: 20px;
      }
      
      [data-layout="carousel"] .market-segments-container::-webkit-scrollbar {
        display: none; /* Chrome, Safari, Opera */
      }
      
      [data-layout="carousel"] .market-segment-item {
        flex: 0 0 calc((100% / var(--columns, 3)) - 30px);
        scroll-snap-align: start;
      }
      
      /* Segment Item */
      .market-segment-card {
        height: 100%;
        border-radius: 8px;
        overflow: hidden;
        box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
        position: relative;
        padding: 30px;
        text-align: center;
        transition: transform 0.3s ease, box-shadow 0.3s ease;
        display: flex;
        flex-direction: column;
        align-items: center;
      }
      
      .market-segment-card:hover {
        transform: translateY(-10px);
        box-shadow: 0 15px 35px rgba(0, 0, 0, 0.15);
      }
      
      .market-segment-accent {
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 6px;
        border-radius: 3px 3px 0 0;
      }
      
      .market-segment-icon {
        margin: 20px 0 25px;
        display: flex;
        justify-content: center;
        align-items: center;
        width: 80px;
        height: 80px;
      }
      
      .market-segment-title {
        margin: 0 0 20px;
        font-size: 1.5rem;
        font-weight: 600;
        position: relative;
        padding-bottom: 15px;
      }
      
      .market-segment-title:after {
        content: '';
        position: absolute;
        bottom: 0;
        left: 50%;
        transform: translateX(-50%);
        width: 40px;
        height: 2px;
        background-color: {{ section.settings.accent_color }};
        border-radius: 1px;
      }
      
      .market-segment-description {
        font-size: 1rem;
        line-height: 1.6;
        color: {{ section.settings.text_color | color_modify: 'alpha', 0.9 }};
      }
      
      /* Carousel Navigation */
      .market-carousel-navigation {
        display: flex;
        justify-content: center;
        gap: 20px;
        margin-top: 40px;
      }
      
      .market-carousel-prev,
      .market-carousel-next {
        background: none;
        border: 2px solid;
        border-radius: 50%;
        width: 44px;
        height: 44px;
        display: flex;
        align-items: center;
        justify-content: center;
        cursor: pointer;
        transition: background-color 0.3s, transform 0.3s;
      }
      
      .market-carousel-prev:hover,
      .market-carousel-next:hover {
        background-color: rgba(0, 0, 0, 0.05);
        transform: scale(1.05);
      }
      
      /* Responsive */
      @media screen and (max-width: 989px) {
        [data-layout="grid"] .market-segments-container {
          grid-template-columns: repeat(2, 1fr);
        }
        
        [data-layout="carousel"] .market-segment-item {
          flex: 0 0 calc(50% - 15px);
        }
      }
      
      @media screen and (max-width: 749px) {
        .market-niche-section {
          padding: 60px 0;
        }
        
        .section-title {
          font-size: calc(var(--font-heading-scale) * 1.8rem);
        }
        
        .section-description {
          font-size: 1rem;
        }
        
        [data-layout="grid"] .market-segments-container {
          grid-template-columns: 1fr;
        }
        
        [data-layout="carousel"] .market-segment-item {
          flex: 0 0 calc(100% - 30px);
        }
        
        .market-segment-title {
          font-size: 1.3rem;
        }
      }
    </style>
    
    <script>
      document.addEventListener('DOMContentLoaded', function() {
        if (document.querySelector('[data-layout="carousel"]')) {
          const container = document.querySelector('.market-segments-container');
          const prevButton = document.querySelector('.market-carousel-prev');
          const nextButton = document.querySelector('.market-carousel-next');
          
          if (container && prevButton && nextButton) {
            const itemWidth = container.querySelector('.market-segment-item').offsetWidth + 30; // Item width + gap
            
            prevButton.addEventListener('click', function() {
              container.scrollBy({ left: -itemWidth, behavior: 'smooth' });
            });
            
            nextButton.addEventListener('click', function() {
              container.scrollBy({ left: itemWidth, behavior: 'smooth' });
            });
          }
        }
      });
    </script>