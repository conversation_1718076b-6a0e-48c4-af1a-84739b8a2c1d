{{ 'section-custom-product-grid.css' | asset_url | stylesheet_tag: preload: true }}
{{ 'component-card.css' | asset_url | stylesheet_tag }}
{{ 'component-price.css' | asset_url | stylesheet_tag }}



<style>
  .custom-product-grid-{{ section.id }} {
    --section-background: {{ section.settings.background_color }};
    --product-primary: {{ section.settings.text_color }};
    --section-padding-top: {{ section.settings.padding_top | times: 0.75 | round: 0 }}px;
    --section-padding-bottom: {{ section.settings.padding_bottom | times: 0.75 | round: 0 }}px;
    --columns-desktop: {{ section.settings.columns_desktop }};
    --columns-mobile: {{ section.settings.columns_mobile }};
    --product-spacing-desktop: {{ section.settings.product_spacing }}px;
    --product-spacing-mobile: {{ section.settings.product_spacing_mobile }}px;
  }

  @media screen and (min-width: 750px) {
    .custom-product-grid-{{ section.id }} {
      --section-padding-top: {{ section.settings.padding_top }}px;
      --section-padding-bottom: {{ section.settings.padding_bottom }}px;
    }
  }

  .filters-container {
    margin-bottom: 2rem;
    padding-bottom: 1rem;
    border-bottom: 1px solid #e8e8e8;
  }

  .filters-toolbar {
    display: flex;
    flex-wrap: wrap;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1rem;
  }

  .filters-toolbar__left {
    display: flex;
    flex-wrap: wrap;
    align-items: center;
    gap: 1rem;
  }

  .filters-toolbar__right {
    display: flex;
    align-items: center;
  }

  .filter-label {
    font-weight: 500;
    margin-right: 0.5rem;
  }

  .filter-dropdown {
    position: relative;
    min-width: 180px;
  }

  .filter-dropdown-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.6rem 1rem;
    border: 1px solid #e1e1e1;
    border-radius: 4px;
    background-color: #fff;
    cursor: pointer;
    transition: all 0.2s ease;
  }

  .filter-dropdown-header:hover {
    background-color: #f9f9f9;
  }

  .filter-dropdown-arrow {
    width: 10px;
    height: 10px;
    transition: transform 0.3s ease;
  }

  .filter-dropdown-header.active .filter-dropdown-arrow {
    transform: rotate(180deg);
  }

  .filter-dropdown-content {
    position: absolute;
    top: 100%;
    left: 0;
    width: 100%;
    max-height: 300px;
    overflow-y: auto;
    background-color: #fff;
    border: 1px solid #e1e1e1;
    border-top: none;
    border-radius: 0 0 4px 4px;
    z-index: 10;
    display: none;
    padding: 0.5rem 0;
    margin-top: -1px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
  }

  .filter-dropdown-content.active {
    display: block;
  }

   .filter-option {
     padding: 0.5rem 1rem;
     display: flex;
     align-items: center;
     cursor: pointer;
     transition: background-color 0.2s ease;
   }

   .filter-option:hover {
       background-color: #f9f9f9;
   }

  .filter-option input[type="checkbox"] {
    margin-right: 0.5rem;
    flex-shrink: 0;
  }

   .filter-option.disabled {
     opacity: 0.4;
     cursor: not-allowed;
     background-color: #f8f8f8;
     text-decoration: line-through;
     color: #999;
   }

    .filter-option.disabled label {
       cursor: not-allowed;
    }

    .filter-option.disabled input[type="checkbox"] {
       cursor: not-allowed;
    }

  .filter-option-label {
    text-transform: capitalize;
  }

  .selected-filters {
    display: flex;
    flex-wrap: wrap;
    gap: 0.5rem;
    margin-top: 1rem;
  }

  .selected-filter-tag {
    background-color: #f5f5f5;
    border-radius: 4px;
    padding: 0.3rem 0.6rem;
    display: flex;
    align-items: center;
    text-transform: capitalize;
  }

  .selected-filter-tag .remove-filter {
    margin-left: 0.5rem;
    cursor: pointer;
  }

  .clear-filters {
    text-decoration: underline;
    color: #555;
    cursor: pointer;
    margin-top: 1rem;
    display: none;
  }

  .clear-filters.active {
    display: block;
  }

  .sort-dropdown-wrapper {
    position: relative;
    min-width: 180px;
  }

  .sort-select {
    appearance: none;
    -webkit-appearance: none;
    -moz-appearance: none;
    padding: 0.6rem 2.5rem 0.6rem 1rem;
    border: 1px solid #e1e1e1;
    border-radius: 4px;
    background-color: #fff;
    line-height: normal;
    width: 100%;
    height: 45px;
    font-size: 1.5rem;
    color: rgba(var(--color-foreground), 0.75);
    box-sizing: border-box;
    transition: background-color 0.2s ease, border-color 0.2s ease;
    background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='10' height='10' viewBox='0 0 10 10' fill='none'%3E%3Cpath d='M1 3.5L5 7.5L9 3.5' stroke='%23333333' stroke-width='1.5' stroke-linecap='round' stroke-linejoin='round'/%3E%3C/svg%3E");
    background-repeat: no-repeat;
    background-position: right 1rem center;
    background-size: 10px;
  }

  .sort-select:focus {
    outline: none;
    border-color: #c4c4c4;
  }

  @media screen and (max-width: 749px) {
    .filters-toolbar {
      flex-direction: column;
      align-items: flex-start;
      gap: 1rem;
    }

    .filters-toolbar__left {
      flex-direction: column;
      width: 100%;
      align-items: flex-start;
    }

    .filters-toolbar__right {
      width: 100%;
      justify-content: flex-end;
    }

    .filter-dropdown {
      width: 100%;
    }

    .custom-sort-wrapper {
      width: 100%;
      justify-content: space-between;
    }

    .custom-sort-select {
      flex-grow: 1;
    }
  }

  .sort-select {
    appearance: none;
    -webkit-appearance: none;
    -moz-appearance: none;
    padding: 0.6rem 2.5rem 0.6rem 1rem;
    border: 1px solid #e1e1e1;
    border-radius: 4px;
    background-color: #fff;
    line-height: normal;
    font-family: var(--font-body-family);
    width: 100%;
    height: 45px;
    font-size: 1.5rem;
    color: rgba(var(--color-foreground), 0.75);
    box-sizing: border-box;
    transition: background-color 0.2s ease, border-color 0.2s ease;
    background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='10' height='10' viewBox='0 0 10 10' fill='none'%3E%3Cpath d='M1 3.5L5 7.5L9 3.5' stroke='%23333333' stroke-width='1.5' stroke-linecap='round' stroke-linejoin='round'/%3E%3C/svg%3E");
    background-repeat: no-repeat;
    background-position: right 1rem center;
    background-size: 10px;
  }

  .sort-select:hover {
      background-color: #f9f9f9;
      border-color: #d1d1d1;
  }

  .sort-select:focus {
      outline: none;
      border-color: #a1a1a1;
      box-shadow: 0 0 0 2px rgba(0, 0, 0, 0.05);
  }

    @media screen and (max-width: 749px) {
      .filters-toolbar__right {
          gap: 0.5rem;
      }
       .sort-dropdown-wrapper {
            width: auto;
            flex-grow: 1;
            min-width: 150px;
       }
    }

</style>

<div class="custom-product-grid custom-product-grid-{{ section.id }}" id="ProductGrid">
  {%- paginate collection.products by section.settings.products_per_page -%}
    <div class="page-width">
      <div class="filters-container">
        <div class="filters-toolbar">
          <div class="filters-toolbar__left">
            <div class="filter-label">Filters:</div>
            <div class="filter-dropdown" data-filter-type="use">
              <div class="filter-dropdown-header">
                <span>Use</span>
                <svg class="filter-dropdown-arrow" viewBox="0 0 10 10" xmlns="http://www.w3.org/2000/svg">
                  <path d="M1 3.5L5 7.5L9 3.5" stroke="currentColor" fill="none" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
                </svg>
              </div>
              <div class="filter-dropdown-content">
                <div class="filter-option">
                  <input type="checkbox" id="use-interior" data-value="interior">
                  <label class="filter-option-label" for="use-interior">Interior</label>
                </div>
                <div class="filter-option">
                  <input type="checkbox" id="use-exterior" data-value="exterior">
                  <label class="filter-option-label" for="use-exterior">Exterior</label>
                </div>
                <div class="filter-option">
                  <input type="checkbox" id="use-floor" data-value="floor">
                  <label class="filter-option-label" for="use-floor">Floor</label>
                </div>
              </div>
            </div>

            <div class="filter-dropdown" data-filter-type="benefits">
              <div class="filter-dropdown-header">
                <span>Benefits</span>
                <svg class="filter-dropdown-arrow" viewBox="0 0 10 10" xmlns="http://www.w3.org/2000/svg">
                  <path d="M1 3.5L5 7.5L9 3.5" stroke="currentColor" fill="none" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
                </svg>
              </div>
              <div class="filter-dropdown-content">
                <div class="filter-option">
                  <input type="checkbox" id="benefits-vapor-permeability" data-value="vapor permeability">
                  <label class="filter-option-label" for="benefits-vapor-permeability">Vapor permeability</label>
                </div>
                <div class="filter-option">
                  <input type="checkbox" id="benefits-moisture-resistance" data-value="moisture resistance">
                  <label class="filter-option-label" for="benefits-moisture-resistance">Moisture resistance</label>
                </div>
                <div class="filter-option">
                  <input type="checkbox" id="benefits-restoration" data-value="the possibility of restoration">
                  <label class="filter-option-label" for="benefits-restoration">The possibility of restoration</label>
                </div>
                <div class="filter-option">
                  <input type="checkbox" id="benefits-suitable-flooring" data-value="suitable for flooring">
                  <label class="filter-option-label" for="benefits-suitable-flooring">Suitable for flooring</label>
                </div>
                <div class="filter-option">
                  <input type="checkbox" id="benefits-soundproofing" data-value="soundproofing">
                  <label class="filter-option-label" for="benefits-soundproofing">Soundproofing</label>
                </div>
                <div class="filter-option">
                  <input type="checkbox" id="benefits-washable" data-value="washable">
                  <label class="filter-option-label" for="benefits-washable">Washable</label>
                </div>
                <div class="filter-option">
                  <input type="checkbox" id="benefits-high-strength" data-value="high strength">
                  <label class="filter-option-label" for="benefits-high-strength">High strength</label>
                </div>
                <div class="filter-option">
                  <input type="checkbox" id="benefits-resistance-precipitation" data-value="resistance to precipitation">
                  <label class="filter-option-label" for="benefits-resistance-precipitation">Resistance to precipitation</label>
                </div>
                <div class="filter-option">
                  <input type="checkbox" id="benefits-antifungal" data-value="Antifungal Properties">
                  <label class="filter-option-label" for="benefits-antifungal">Antifungal Properties</label>
                </div>
                <div class="filter-option">
                  <input type="checkbox" id="benefits-hides-defects" data-value="Hides defects of preparation">
                  <label class="filter-option-label" for="benefits-hides-defects">Hides defects of preparation</label>
                </div>
                <div class="filter-option">
                  <input type="checkbox" id="benefits-resistance-dirt" data-value="Resistance to dirt">
                  <label class="filter-option-label" for="benefits-resistance-dirt">Resistance to dirt</label>
                </div>
                <div class="filter-option">
                  <input type="checkbox" id="benefits-changing-shades" data-value="Changing shades">
                  <label class="filter-option-label" for="benefits-changing-shades">Changing shades</label>
                </div>
              </div>
            </div>

            <div class="filter-dropdown" data-filter-type="composition">
              <div class="filter-dropdown-header">
                <span>Composition</span>
                <svg class="filter-dropdown-arrow" viewBox="0 0 10 10" xmlns="http://www.w3.org/2000/svg">
                  <path d="M1 3.5L5 7.5L9 3.5" stroke="currentColor" fill="none" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
                </svg>
              </div>
              <div class="filter-dropdown-content">
                <div class="filter-option">
                  <input type="checkbox" id="composition-metal-powder" data-value="metal powder">
                  <label class="filter-option-label" for="composition-metal-powder">Metal powder</label>
                </div>
                <div class="filter-option">
                  <input type="checkbox" id="composition-acryl-copolymers" data-value="acryl copolymers">
                  <label class="filter-option-label" for="composition-acryl-copolymers">Acryl copolymers</label>
                </div>
                <div class="filter-option">
                  <input type="checkbox" id="composition-mineral-microspheres" data-value="mineral microspheres">
                  <label class="filter-option-label" for="composition-mineral-microspheres">Mineral Microspheres</label>
                </div>
                <div class="filter-option">
                  <input type="checkbox" id="composition-reinforcing-fibers" data-value="reinforcing fibers">
                  <label class="filter-option-label" for="composition-reinforcing-fibers">Reinforcing fibers</label>
                </div>
                <div class="filter-option">
                  <input type="checkbox" id="composition-mica" data-value="mica">
                  <label class="filter-option-label" for="composition-mica">Mica</label>
                </div>
                <div class="filter-option">
                  <input type="checkbox" id="composition-lime" data-value="lime">
                  <label class="filter-option-label" for="composition-lime">Lime</label>
                </div>
                <div class="filter-option">
                  <input type="checkbox" id="composition-pearlescent-pigments" data-value="Pearlescent Pigments">
                  <label class="filter-option-label" for="composition-pearlescent-pigments">Pearlescent Pigments</label>
                </div>
                <div class="filter-option">
                  <input type="checkbox" id="composition-mineral-fillers" data-value="Mineral Fillers">
                  <label class="filter-option-label" for="composition-mineral-fillers">Mineral Fillers</label>
                </div>
                <div class="filter-option">
                  <input type="checkbox" id="composition-ground-travertine" data-value="Ground travertine">
                  <label class="filter-option-label" for="composition-ground-travertine">Ground travertine</label>
                </div>
              </div>
            </div>

            <div class="filter-dropdown" data-filter-type="effect">
              <div class="filter-dropdown-header">
                <span>Effect</span>
                <svg class="filter-dropdown-arrow" viewBox="0 0 10 10" xmlns="http://www.w3.org/2000/svg">
                  <path d="M1 3.5L5 7.5L9 3.5" stroke="currentColor" fill="none" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
                </svg>
              </div>
              <div class="filter-dropdown-content">
                <div class="filter-option">
                  <input type="checkbox" id="effect-matte" data-value="Matte">
                  <label class="filter-option-label" for="effect-matte">Matte</label>
                </div>
                <div class="filter-option">
                  <input type="checkbox" id="effect-glossy" data-value="Glossy">
                  <label class="filter-option-label" for="effect-glossy">Glossy</label>
                </div>
                <div class="filter-option">
                  <input type="checkbox" id="effect-metallic" data-value="Metallic">
                  <label class="filter-option-label" for="effect-metallic">Metallic</label>
                </div>
                <div class="filter-option">
                  <input type="checkbox" id="effect-pearly" data-value="Pearly">
                  <label class="filter-option-label" for="effect-pearly">Pearly</label>
                </div>
                <div class="filter-option">
                  <input type="checkbox" id="effect-wet-silk" data-value="Wet silk">
                  <label class="filter-option-label" for="effect-wet-silk">Wet silk</label>
                </div>
                <div class="filter-option">
                  <input type="checkbox" id="effect-fabric" data-value="Fabric">
                  <label class="filter-option-label" for="effect-fabric">Fabric</label>
                </div>
                <div class="filter-option">
                  <input type="checkbox" id="effect-rust" data-value="Rust">
                  <label class="filter-option-label" for="effect-rust">Rust</label>
                </div>
                <div class="filter-option">
                  <input type="checkbox" id="effect-textured" data-value="Textured">
                  <label class="filter-option-label" for="effect-textured">Textured</label>
                </div>
                <div class="filter-option">
                  <input type="checkbox" id="effect-venetian-plaster" data-value="Venetian plaster">
                  <label class="filter-option-label" for="effect-venetian-plaster">Venetian plaster</label>
                </div>
                <div class="filter-option">
                  <input type="checkbox" id="effect-antique-wall" data-value="Antique wall">
                  <label class="filter-option-label" for="effect-antique-wall">Antique wall</label>
                </div>
                <div class="filter-option">
                  <input type="checkbox" id="effect-concrete" data-value="Concrete">
                  <label class="filter-option-label" for="effect-concrete">Concrete</label>
                </div>
                <div class="filter-option">
                  <input type="checkbox" id="effect-marble" data-value="Marble">
                  <label class="filter-option-label" for="effect-marble">Marble</label>
                </div>
                <div class="filter-option">
                  <input type="checkbox" id="effect-travertine" data-value="Travertine">
                  <label class="filter-option-label" for="effect-travertine">Travertine</label>
                </div>
                <div class="filter-option">
                  <input type="checkbox" id="effect-stone" data-value="Stone">
                  <label class="filter-option-label" for="effect-stone">Stone</label>
                </div>
                <div class="filter-option">
                  <input type="checkbox" id="effect-with-particles" data-value="With particles">
                  <label class="filter-option-label" for="effect-with-particles">With particles</label>
                </div>
                <div class="filter-option">
                  <input type="checkbox" id="effect-smooth" data-value="Smooth">
                  <label class="filter-option-label" for="effect-smooth">Smooth</label>
                </div>
              </div>
            </div>
          </div>

          <div class="filters-toolbar__right">
            <div class="filter-label">Sort by:</div>
            <div class="sort-dropdown-wrapper">
              <form action="{{ collection.url }}" method="get">
                <select name="sort_by" id="SortBy" class="sort-select" onchange="this.form.submit()">
                  <option value="title-ascending" {% if collection.sort_by == 'title-ascending' or collection.sort_by == blank %}selected{% endif %}>Alphabetically, A-Z</option>
                  <option value="title-descending" {% if collection.sort_by == 'title-descending' %}selected{% endif %}>Alphabetically, Z-A</option>
                </select>
              </form>
            </div>
          </div>
        </div>

        <div class="selected-filters"></div>
        <div class="clear-filters">Clear filters</div>
      </div>

      <div class="custom-product-grid__container">
        {%- if collection.products.size == 0 -%}
          <div class="custom-product-grid__empty">
            <h2 class="custom-product-grid__empty-title">
              No products found
            </h2>
            <p class="custom-product-grid__empty-text">
              <a href="{{ collection.url }}" class="underlined-link link">Clear all filters</a>
            </p>
          </div>
        {%- else -%}
          <ul class="custom-product-grid__list">
            {%- for product in collection.products -%}
              <li class="custom-product-card"
                  data-product-id="{{ product.id }}"
                  data-product-tags="{{ product.tags | join: ',' }}">
                <a href="{{ product.url }}" class="custom-product-card__link">
                  <div class="custom-product-card__media">
                    <div class="custom-product-card__media-wrap">
                      {%- if product.featured_media -%}
                        <img
                          srcset="
                            {%- if product.featured_media.width >= 165 -%}{{ product.featured_media | image_url: width: 165 }} 165w,{%- endif -%}
                            {%- if product.featured_media.width >= 360 -%}{{ product.featured_media | image_url: width: 360 }} 360w,{%- endif -%}
                            {%- if product.featured_media.width >= 533 -%}{{ product.featured_media | image_url: width: 533 }} 533w,{%- endif -%}
                            {%- if product.featured_media.width >= 720 -%}{{ product.featured_media | image_url: width: 720 }} 720w,{%- endif -%}
                            {{ product.featured_media | image_url }} {{ product.featured_media.width }}w
                          "
                          src="{{ product.featured_media | image_url: width: 533 }}"
                          sizes="(min-width: {{ settings.page_width }}px) {{ settings.page_width | minus: 130 | divided_by: 4 }}px, (min-width: 990px) calc((100vw - 130px) / 4), (min-width: 750px) calc((100vw - 120px) / 3), calc((100vw - 35px) / 2)"
                          loading="lazy"
                          alt="{{ product.featured_media.alt | escape }}"
                          width="{{ product.featured_media.width }}"
                          height="{{ product.featured_media.height }}"
                        >

                        {%- if section.settings.show_secondary_image and product.media[1] != null -%}
                          <img
                            srcset="
                              {%- if product.media[1].width >= 165 -%}{{ product.media[1] | image_url: width: 165 }} 165w,{%- endif -%}
                              {%- if product.media[1].width >= 360 -%}{{ product.media[1] | image_url: width: 360 }} 360w,{%- endif -%}
                              {%- if product.media[1].width >= 533 -%}{{ product.media[1] | image_url: width: 533 }} 533w,{%- endif -%}
                              {%- if product.media[1].width >= 720 -%}{{ product.media[1] | image_url: width: 720 }} 720w,{%- endif -%}
                              {{ product.media[1] | image_url }} {{ product.media[1].width }}w
                            "
                            src="{{ product.media[1] | image_url: width: 533 }}"
                            sizes="(min-width: {{ settings.page_width }}px) {{ settings.page_width | minus: 130 | divided_by: 4 }}px, (min-width: 990px) calc((100vw - 130px) / 4), (min-width: 750px) calc((100vw - 120px) / 3), calc((100vw - 35px) / 2)"
                            loading="lazy"
                            alt="{{ product.media[1].alt | escape }}"
                            width="{{ product.media[1].width }}"
                            height="{{ product.media[1].height }}"
                            class="secondary-media"
                          >
                        {%- endif -%}
                      {%- else -%}
                        {{ 'blank-placeholder.png' | asset_url | image_tag:
                            loading: 'lazy',
                            width: 533,
                            height: 533,
                            alt: 'Blank placeholder',
                            class: 'placeholder-img'
                        }}
                      {%- endif -%}
                    </div>
                  </div>

                  <div class="custom-product-card__info">
                    <h3 class="custom-product-card__product-name">
                      {{ product.title | escape }}
                    </h3>

                    <div class="custom-product-card__description-container">
                      <span class="custom-product-card__long-line"></span>
                      <div class="custom-product-card__product-description">
                        {% if product.metafields.custom.short_description != blank %}
                          {{ product.metafields.custom.short_description | escape }}
                        {% else %}
                          {{ product.description | strip_html | truncatewords: 15 }}
                        {% endif %}
                      </div>
                    </div>

                    <div class="custom-product-card__price">
                      {% if product.price_varies %}
                        From {{ product.price_min | money }}
                      {% else %}
                        {{ product.price | money }}
                      {% endif %}
                    </div>
                  </div>
                </a>
                <a href="{{ product.url }}" class="custom-product-card__arrow-button" aria-label="View {{ product.title }}">
                  {% render 'icon-arrow-right' %}
                </a>
              </li>
            {%- endfor -%}
          </ul>

          {%- if paginate.pages > 1 -%}
            <div class="custom-product-grid__pagination">
              {% render 'pagination', paginate: paginate %}
            </div>
          {%- endif -%}
        {%- endif -%}
      </div>
    </div>
  {%- endpaginate -%}
</div>

<script>
  class ProductGrid {
    constructor(container) {
      this.container = container;
      this.productCards = container.querySelectorAll('.custom-product-card');
      this.sortSelect = document.getElementById('SortBy');
      this.filterDropdowns = container.querySelectorAll('.filter-dropdown');
      this.filterOptions = container.querySelectorAll('.filter-option input[type="checkbox"]');
      this.clearFiltersButton = container.querySelector('.clear-filters');
      this.selectedFiltersContainer = container.querySelector('.selected-filters');

      this.activeFilters = {
        use: [],
        benefits: [],
        composition: [],
        effect: []
      };

      this.productData = [];
      this.allProductData = [];

      this.filterTagsByCategory = {
        use: [],
        benefits: [],
        composition: [],
        effect: []
      };

      this.init();
    }

    init() {
      this.initProductCardHover();
      this.initAnimations();
      this.initEqualHeights();
      this.initSorting();
      this.collectProductData();
      this.initFilterDropdowns();
      this.initFilterOptions();
      this.initFilterOptionClick();
      this.initClearFilters();

      document.addEventListener('allProductsLoaded', () => {
        console.log('All products loaded, updating filter counts');
        this.updateFilterOptionsAvailability();
      }, { once: true });
      if (window.allCollectionProducts && window.allCollectionProducts.length === window.allProductsCount) {
        this.updateFilterOptionsAvailability();
      }
    }

    initFilterOptionClick() {
      this.container.querySelectorAll('.filter-option').forEach(optionDiv => {
        optionDiv.addEventListener('click', (event) => {
          const checkbox = optionDiv.querySelector('input[type="checkbox"]');
          const label = optionDiv.querySelector('label');

          if (!checkbox) return;
          if (event.target === optionDiv) {
            checkbox.checked = !checkbox.checked;
            checkbox.dispatchEvent(new Event('change', { bubbles: true }));
          }
        });
      });
    }

    initSorting() {
    }

    collectProductData() {
      console.log('Starting to collect product data');


      this.productCards.forEach(card => {
        const productId = card.dataset.productId;
        const productTags = card.dataset.productTags.split(',');
        const tagsByCategory = this.processTagsByCategory(productTags);
        this.productData.push({ id: productId, tags: productTags, tagsByCategory: tagsByCategory, element: card });
      });
      console.log('Visible products processed:', this.productData.length);

      const processAllCollectionProducts = async () => {
        if (window.needToLoadAllProducts && window.allCollectionProducts.length < window.allProductsCount) {
          console.log('Need to fetch all products for accurate filtering');

          document.addEventListener('allProductsLoaded', () => {
            console.log('All products loaded event received');
            processProducts();
          }, { once: true });
          await window.fetchAllCollectionProducts();
        } else {
          processProducts();
        }
      };

      const processProducts = () => {
        if (window.allCollectionProducts && Array.isArray(window.allCollectionProducts)) {
          console.log('Processing all collection products, length:', window.allCollectionProducts.length);

          this.allProductData = [];
          if (window.allCollectionProducts.length > 0) {
            console.log('Sample product from collection:', window.allCollectionProducts[0]);
            console.log('Sample product tags:', window.allCollectionProducts[0].tags);
          }

          window.allCollectionProducts.forEach(product => {
            if (product && product.id) {
              // Keep original tags without lowercase conversion for exact matching
              const productTags = typeof product.tags === 'string' && product.tags.trim() !== ''
                ? product.tags.split(',').map(tag => tag.trim())
                : [];

              console.log('Product tags for', product.title, ':', productTags);

              const tagsByCategory = this.processTagsByCategory(productTags);
              this.allProductData.push({
                id: product.id.toString(),
                title: product.title || '',
                handle: product.handle || '',
                url: product.url || '',
                featured_image: product.featured_image || '',
                price: product.price || '',
                description: product.description || '',
                tags: productTags,
                tagsByCategory: tagsByCategory
              });
            } else {
              console.error('Invalid product data:', product);
            }
          });

          console.log('All collection products processed:', this.allProductData.length);
          if (this.allProductData.length > 0) {
            console.log('Sample of processed data:', this.allProductData[0]);
            console.log('Sample tags by category:', this.allProductData[0].tagsByCategory);
          }


          this.updateFilterOptionsAvailability();
        } else {
          console.error('window.allCollectionProducts is not available or not an array!');
        }
      };


      processAllCollectionProducts();
    }

    processTagsByCategory(productTags) {
      const tagsByCategory = { use: [], benefits: [], composition: [], effect: [] };
      const keywordMappings = window.tagCategoryMappings || {
        // Use filters
        'interior': 'use', 'Interior': 'use',
        'exterior': 'use', 'Exterior': 'use',
        'floor': 'use', 'Floor': 'use',

        // Benefits filters
        'vapor permeability': 'benefits', 'Vapor permeability': 'benefits',
        'moisture resistance': 'benefits', 'Moisture resistance': 'benefits',
        'the possibility of restoration': 'benefits', 'The possibility of restoration': 'benefits',
        'suitable for flooring': 'benefits', 'Suitable for flooring': 'benefits',
        'soundproofing': 'benefits', 'Soundproofing': 'benefits',
        'washable': 'benefits', 'Washable': 'benefits',
        'high strength': 'benefits', 'High strength': 'benefits',
        'resistance to precipitation': 'benefits', 'Resistance to precipitation': 'benefits',
        'antifungal properties': 'benefits', 'Antifungal Properties': 'benefits',
        'hides defects of preparation': 'benefits', 'Hides defects of preparation': 'benefits',
        'resistance to dirt': 'benefits', 'Resistance to dirt': 'benefits',
        'changing shades': 'benefits', 'Changing shades': 'benefits',

        // Composition filters
        'metal powder': 'composition', 'Metal powder': 'composition',
        'acryl copolymers': 'composition', 'Acryl copolymers': 'composition',
        'mineral microspheres': 'composition', 'Mineral microspheres': 'composition',
        'reinforcing fibers': 'composition', 'Reinforcing fibers': 'composition',
        'mica': 'composition', 'Mica': 'composition',
        'lime': 'composition', 'Lime': 'composition',
        'pearlescent pigments': 'composition', 'Pearlescent Pigments': 'composition',
        'mineral fillers': 'composition', 'Mineral Fillers': 'composition',
        'ground travertine': 'composition', 'Ground travertine': 'composition',

        // Effect filters
        'matte': 'effect', 'Matte': 'effect',
        'glossy': 'effect', 'Glossy': 'effect',
        'metallic': 'effect', 'Metallic': 'effect',
        'pearly': 'effect', 'Pearly': 'effect',
        'wet silk': 'effect', 'Wet silk': 'effect',
        'fabric': 'effect', 'Fabric': 'effect',
        'rust': 'effect', 'Rust': 'effect',
        'textured': 'effect', 'Textured': 'effect',
        'venetian plaster': 'effect', 'Venetian plaster': 'effect',
        'antique wall': 'effect', 'Antique wall': 'effect',
        'concrete': 'effect', 'Concrete': 'effect',
        'marble': 'effect', 'Marble': 'effect',
        'travertine': 'effect', 'Travertine': 'effect',
        'stone': 'effect', 'Stone': 'effect',
        'with particles': 'effect', 'With particles': 'effect',
        'smooth': 'effect', 'Smooth': 'effect'
      };

      if (!Array.isArray(productTags)) {
        console.error('productTags is not an array:', productTags);
        return tagsByCategory;
      }
      productTags.forEach(tag => {
        if (!tag) return;

        // Keep original tag for exact matching
        const originalTag = tag;
        // Create lowercase version for case-insensitive matching
        const cleanTag = typeof tag === 'string' ? tag.trim().toLowerCase() : '';
        if (!cleanTag) return;

        Object.entries(keywordMappings).forEach(([keyword, category]) => {
          // First check for exact match (case-sensitive)
          if (cleanTag === keyword.toLowerCase() || originalTag === keyword) {
            if (!tagsByCategory[category].includes(keyword)) {
              tagsByCategory[category].push(keyword);
            }
            if (!this.filterTagsByCategory[category].includes(keyword)) {
              this.filterTagsByCategory[category].push(keyword);
            }
            return; // Skip further checks if exact match found
          }

          // More flexible matching - normalize spaces and case
          const normalizedKeyword = keyword.toLowerCase().replace(/\s+/g, ' ').trim();
          const normalizedTag = cleanTag.replace(/\s+/g, ' ').trim();

          // Check if tag contains the keyword or if they're equal after normalization
          if (normalizedTag === normalizedKeyword || normalizedTag.includes(normalizedKeyword) ||
              normalizedKeyword.includes(normalizedTag)) {
            if (!tagsByCategory[category].includes(keyword)) {
              tagsByCategory[category].push(keyword);
            }
            if (!this.filterTagsByCategory[category].includes(keyword)) {
              this.filterTagsByCategory[category].push(keyword);
            }
          }
        });
      });

      return tagsByCategory;
    }

    initFilterDropdowns() {
      this.filterDropdowns.forEach(dropdown => {
        const header = dropdown.querySelector('.filter-dropdown-header');
        const content = dropdown.querySelector('.filter-dropdown-content');
        header.addEventListener('click', () => {
          const isActive = header.classList.contains('active');
          this.filterDropdowns.forEach(otherDropdown => {
             otherDropdown.querySelector('.filter-dropdown-header').classList.remove('active');
             otherDropdown.querySelector('.filter-dropdown-content').classList.remove('active');
          });
          if (!isActive) {
             header.classList.add('active');
             content.classList.add('active');
          }
        });
      });
      document.addEventListener('click', (event) => {
        if (!event.target.closest('.filter-dropdown')) {
          this.filterDropdowns.forEach(dropdown => {
            dropdown.querySelector('.filter-dropdown-header').classList.remove('active');
            dropdown.querySelector('.filter-dropdown-content').classList.remove('active');
          });
        }
      });
    }

    initFilterOptions() {
      this.filterOptions.forEach(option => {
        option.addEventListener('change', () => {
          const filterType = option.closest('.filter-dropdown').dataset.filterType;
          const filterValue = option.dataset.value;
          const dropdown = option.closest('.filter-dropdown');
          const dropdownHeader = dropdown.querySelector('.filter-dropdown-header');
          const dropdownContent = dropdown.querySelector('.filter-dropdown-content');

          if (option.checked) {
            if (!this.activeFilters[filterType].includes(filterValue)) {
              this.activeFilters[filterType].push(filterValue);
            }

            setTimeout(() => {
              dropdownHeader.classList.remove('active');
              dropdownContent.classList.remove('active');
            }, 300);
          } else {
            this.activeFilters[filterType] = this.activeFilters[filterType].filter(val => val !== filterValue);

            // Check if all filters are now removed
            const hasActiveFilters = Object.values(this.activeFilters).some(filters => filters.length > 0);
            if (!hasActiveFilters) {
                // Reload the page to restore the original collection state
                window.location.reload();
                return;
            }
          }

          this.applyFilters();
          this.updateSelectedFiltersDisplay();
          this.updateClearFiltersVisibility();
        });
      });
    }

    initClearFilters() {
      this.clearFiltersButton.addEventListener('click', () => {
        this.filterOptions.forEach(option => { option.checked = false; });
        Object.keys(this.activeFilters).forEach(key => { this.activeFilters[key] = []; });

        // Reload the page to restore the original collection state
        window.location.reload();
      });
    }

   applyFilters() {
        console.log('Applying filters');
        const activeFilterTypes = Object.keys(this.activeFilters).filter(type => this.activeFilters[type].length > 0);
        console.log('Active filter types for display:', activeFilterTypes);

        // If no active filters, show all products
        if (activeFilterTypes.length === 0) {
            console.log('No active filters, showing all products');
            this.productData.forEach(product => {
                product.element.style.display = '';
            });

            // Update UI
            const noProductsFoundElement = this.container.querySelector('.custom-product-grid__empty');
            const productListElement = this.container.querySelector('.custom-product-grid__list');
            const paginationElement = this.container.querySelector('.custom-product-grid__pagination');

            if (noProductsFoundElement) noProductsFoundElement.style.display = 'none';
            if (productListElement) productListElement.style.display = '';
            if (paginationElement) paginationElement.style.display = '';

            this.initEqualHeights();
            return;
        }


        if (window.needToLoadAllProducts && window.allCollectionProducts.length < window.allProductsCount) {
            console.log('Loading all products from collection...');


            if (typeof window.fetchAllCollectionProducts === 'function') {
                console.log('Fetching all products before applying filters...');


                const loadingIndicator = document.createElement('div');
                loadingIndicator.className = 'loading-indicator';
                loadingIndicator.innerHTML = '<div class="spinner"></div><div>Loading all products...</div>';
                loadingIndicator.style.cssText = 'position: fixed; top: 50%; left: 50%; transform: translate(-50%, -50%); background: rgba(255,255,255,0.9); padding: 20px; border-radius: 5px; box-shadow: 0 0 10px rgba(0,0,0,0.1); z-index: 1000; text-align: center;';
                loadingIndicator.querySelector('.spinner').style.cssText = 'border: 4px solid #f3f3f3; border-top: 4px solid #3498db; border-radius: 50%; width: 30px; height: 30px; animation: spin 2s linear infinite; margin: 0 auto 10px;';

                // Add keyframes for spinner animation
                const style = document.createElement('style');
                style.textContent = '@keyframes spin { 0% { transform: rotate(0deg); } 100% { transform: rotate(360deg); } }';
                document.head.appendChild(style);

                document.body.appendChild(loadingIndicator);

                // Fetch all products and then apply filters
                window.fetchAllCollectionProducts().then(() => {
                    // Remove loading indicator
                    document.body.removeChild(loadingIndicator);

                    // Re-process all products
                    this.allProductData = [];
                    window.allCollectionProducts.forEach(product => {
                        if (product && product.id) {
                            const productTags = typeof product.tags === 'string' && product.tags.trim() !== ''
                                ? product.tags.split(',').map(tag => tag.trim())
                                : [];

                            const tagsByCategory = this.processTagsByCategory(productTags);

                            this.allProductData.push({
                                id: product.id.toString(),
                                title: product.title || '',
                                handle: product.handle || '',
                                url: product.url || '',
                                featured_image: product.featured_image || '',
                                price: product.price || '',
                                description: product.description || '',
                                tags: productTags,
                                tagsByCategory: tagsByCategory
                            });
                        }
                    });

                    // Now apply filters with the complete data
                    this.applyFilters();
                });

                // Return early - we'll apply filters after fetching all products
                return;
            } else {
                console.log('fetchAllCollectionProducts function not available, using current products.');
            }
        }

        // Get matching products from all collection data
        const allMatchingProducts = this.getMatchingProductsFromAll(this.activeFilters);
        console.log('Total matching products in collection:', allMatchingProducts.length);

        // Get matching product IDs
        const matchingProductIds = new Set(allMatchingProducts.map(product => product.id));
        console.log('Matching product IDs:', matchingProductIds.size);

        // Get the product list element
        const productListElement = this.container.querySelector('.custom-product-grid__list');
        const noProductsFoundElement = this.container.querySelector('.custom-product-grid__empty');
        const paginationElement = this.container.querySelector('.custom-product-grid__pagination');

        if (!productListElement) return;

        // If no matching products, show the empty message
        if (allMatchingProducts.length === 0) {
            if (noProductsFoundElement) noProductsFoundElement.style.display = 'block';
            productListElement.style.display = 'none';
            if (paginationElement) paginationElement.style.display = 'none';
            return;
        }


        productListElement.innerHTML = '';


        allMatchingProducts.forEach(product => {

            const productData = window.allCollectionProducts.find(p => p.id === product.id);
            if (!productData) return;


            const productCard = document.createElement('li');
            productCard.className = 'custom-product-card';
            productCard.className += ' is-visible';
            productCard.dataset.productId = product.id;
            productCard.dataset.productTags = productData.tags;


            const productDescription = productData.description || productData.title;

            productCard.innerHTML = `
                <a href="${productData.url}" class="custom-product-card__link">
                    <div class="custom-product-card__media">
                        <div class="custom-product-card__media-wrap">
                            <img src="${productData.featured_image}"
                                 alt="${productData.title}"
                                 loading="lazy"
                                 width="300"
                                 height="300">
                        </div>
                    </div>
                    <div class="custom-product-card__info">
                        <h3 class="custom-product-card__product-name">
                            ${productData.title}
                        </h3>
                        <div class="custom-product-card__description-container">
                            <span class="custom-product-card__long-line"></span>
                            <div class="custom-product-card__product-description">
                                ${productDescription}
                            </div>
                        </div>
                        <div class="custom-product-card__price">
                            ${productData.price_varies
                              ? `From ${productData.formatted_price_min}`
                              : `${productData.formatted_price}`}
                        </div>
                    </div>
                </a>
                <a href="${productData.url}" class="custom-product-card__arrow-button" aria-label="View ${productData.title}">
                    <svg viewBox="0 0 14 10" fill="none" aria-hidden="true" focusable="false" class="icon icon-arrow" xmlns="http://www.w3.org/2000/svg">
                        <path fill-rule="evenodd" clip-rule="evenodd" d="M8.537.808a.5.5 0 01.817-.162l4 4a.5.5 0 010 .708l-4 4a.5.5 0 11-.708-.708L11.793 5.5H1a.5.5 0 010-1h10.793L8.646 1.354a.5.5 0 01-.109-.546z" fill="currentColor"></path>
                    </svg>
                </a>
            `;


            productListElement.appendChild(productCard);
        });


        this.productCards = this.container.querySelectorAll('.custom-product-card');
        this.productData = [];
        this.productCards.forEach(card => {
            const productId = card.dataset.productId;
            const productTags = card.dataset.productTags.split(',');
            const tagsByCategory = this.processTagsByCategory(productTags);
            this.productData.push({ id: productId, tags: productTags, tagsByCategory: tagsByCategory, element: card });
        });


        if (noProductsFoundElement) noProductsFoundElement.style.display = 'none';
        productListElement.style.display = '';
        if (paginationElement) paginationElement.style.display = 'none';


        this.initProductCardHover();
        this.initAnimations();
        this.initEqualHeights();


        this.updateFilterOptionsAvailability();

        console.log('Created product cards for all matching products:', allMatchingProducts.length);
    }

    updateSelectedFiltersDisplay() {
      this.selectedFiltersContainer.innerHTML = '';
      let hasActiveFilters = false;
      Object.entries(this.activeFilters).forEach(([filterType, values]) => {
        values.forEach(value => {
          hasActiveFilters = true;
          const filterTag = document.createElement('div');
          filterTag.className = 'selected-filter-tag';
          const formattedType = filterType.charAt(0).toUpperCase() + filterType.slice(1);
          filterTag.innerHTML = `
            ${formattedType}: ${value}
            <span class="remove-filter" data-filter-type="${filterType}" data-filter-value="${value}" aria-label="Remove filter ${value}">×</span>
          `;
          filterTag.querySelector('.remove-filter').addEventListener('click', (e) => {
             const type = e.target.dataset.filterType;
             const val = e.target.dataset.filterValue;
             const checkbox = this.container.querySelector(`.filter-option input[data-value="${val}"]`);
              if (checkbox && checkbox.closest('.filter-dropdown').dataset.filterType === type) {
                  checkbox.checked = false;
                  checkbox.dispatchEvent(new Event('change'));


                  const hasActiveFilters = Object.values(this.activeFilters).some(filters => filters.length > 0);
                  if (!hasActiveFilters) {

                      window.location.reload();
                  }
              } else {
                 this.activeFilters[type] = this.activeFilters[type].filter(v => v !== val);


                 const hasActiveFilters = Object.values(this.activeFilters).some(filters => filters.length > 0);
                 if (!hasActiveFilters) {

                     window.location.reload();
                 } else {
                     this.applyFilters();
                     this.updateSelectedFiltersDisplay();
                     this.updateFilterOptionsAvailability();
                     this.updateClearFiltersVisibility();
                 }
              }
          });
          this.selectedFiltersContainer.appendChild(filterTag);
        });
      });
      this.selectedFiltersContainer.style.display = hasActiveFilters ? 'flex' : 'none';
    }

     updateClearFiltersVisibility() {
      const hasActiveFilters = Object.values(this.activeFilters).some(filters => filters.length > 0);
      this.clearFiltersButton.classList.toggle('active', hasActiveFilters);
      this.clearFiltersButton.style.display = hasActiveFilters ? 'block' : 'none';
    }

    updateFilterOptionsAvailability() {
        if (!this.allProductData || this.allProductData.length === 0) {
            return;
        }

        const activeFilterTypes = Object.keys(this.activeFilters).filter(type => this.activeFilters[type].length > 0);

        if (activeFilterTypes.length === 0) {
            this.filterOptions.forEach(option => {
                const filterType = option.closest('.filter-dropdown').dataset.filterType;
                const filterValue = option.dataset.value;
                const optionContainer = option.closest('.filter-option');
                const label = optionContainer.querySelector('label');

                let count = 0;


                if (filterValue === 'Antifungal Properties' ||
                    filterValue === 'Mineral Fillers' ||
                    filterValue === 'Resistance to dirt' ||
                    filterValue === 'Pearlescent Pigments' ||
                    filterValue === 'Metal powder' ||
                    filterValue === 'Acryl copolymers' ||
                    filterValue === 'Mineral microspheres' ||
                    filterValue === 'Reinforcing fibers' ||
                    filterValue === 'Mica' ||
                    filterValue === 'Lime' ||
                    filterValue === 'Ground travertine' ||
                    filterValue === 'venetian plaster' ||
                    filterValue === 'antique wall' ||
                    filterValue === 'concrete' ||
                    filterValue === 'marble' ||
                    filterValue === 'travertine' ||
                    filterValue === 'stone' ||
                    filterValue === 'smooth' ||
                    filterValue === 'Venetian plaster' ||
                    filterValue === 'Antique wall' ||
                    filterValue === 'Concrete' ||
                    filterValue === 'Marble' ||
                    filterValue === 'Travertine' ||
                    filterValue === 'Stone' ||
                    filterValue === 'Smooth') {
                    let matchingProducts = [];

                    if (filterValue === 'Pearlescent Pigments') {
                        const allTags = [];
                        this.allProductData.forEach(product => {
                            if (product.tags) {
                                product.tags.forEach(tag => {
                                    if (tag.toLowerCase().includes('pearl')) {
                                        allTags.push(tag);
                                    }
                                });
                            }
                        });
                    }

                    for (const product of this.allProductData) {
                        if (product.tags && product.tags.some(tag => {
                            const tagLower = tag.toLowerCase();
                            const filterLower = filterValue.toLowerCase();

                            if (filterValue === 'Pearlescent Pigments') {
                                const isPearlTag =
                                    tag === 'Pearlescent Pigments' ||
                                    tag === 'pearlescent pigments' ||
                                    tag === 'Pearlescent pigments' ||
                                    tag === 'pearlescent' ||
                                    tag === 'Pearlescent' ||
                                    tag === 'pearl' ||
                                    tag === 'Pearl' ||
                                    tagLower.includes('pearl');

                                return isPearlTag;
                            }

                            if (filterType === 'composition') {
                                if (tagLower === filterLower) {
                                    return true;
                                }

                                if (tagLower.includes(filterLower) || filterLower.includes(tagLower)) {
                                    return true;
                                }

                                if (filterValue === 'Mineral Fillers' &&
                                    (tagLower.includes('mineral') || tagLower.includes('filler'))) {
                                    return true;
                                }

                                if (filterValue === 'Metal powder' &&
                                    (tagLower.includes('metal') || tagLower.includes('powder'))) {
                                    return true;
                                }

                                return false;
                            }

                            return tag === filterValue ||
                                   tagLower === filterLower;
                        })) {
                            matchingProducts.push(product.title);
                            count++;
                        }
                    }
                } else {
                    for (const product of this.allProductData) {
                        if (product.tagsByCategory && product.tagsByCategory[filterType]) {
                            const normalizedFilterValue = filterValue.toLowerCase().replace(/\s+/g, ' ').trim();

                            const hasMatchingTag = product.tagsByCategory[filterType].some(tag => {
                                const normalizedTag = tag.toLowerCase().replace(/\s+/g, ' ').trim();

                                if (filterType === 'composition') {
                                    if (normalizedTag === normalizedFilterValue) {
                                        return true;
                                    }

                                    if (normalizedTag.includes(normalizedFilterValue) ||
                                        normalizedFilterValue.includes(normalizedTag)) {
                                        return true;
                                    }

                                    if ((filterValue === 'Mineral Fillers' || filterValue === 'mineral fillers') &&
                                        (normalizedTag.includes('mineral') || normalizedTag.includes('filler'))) {
                                        return true;
                                    }

                                    if ((filterValue === 'Metal powder' || filterValue === 'metal powder') &&
                                        (normalizedTag.includes('metal') || normalizedTag.includes('powder'))) {
                                        return true;
                                    }

                                    if ((filterValue === 'Pearlescent Pigments' || filterValue === 'pearlescent pigments') &&
                                        (normalizedTag.includes('pearl') || normalizedTag.includes('pigment'))) {
                                        return true;
                                    }

                                    return false;
                                }

                                return normalizedTag === normalizedFilterValue;
                            });

                            if (hasMatchingTag) {
                                count++;
                            }
                        }
                    }
                }

                if (label) {
                    const labelText = label.textContent.split(' (')[0];
                    label.textContent = `${labelText} (${count})`;
                }


                if (count > 0) {
                    optionContainer.classList.remove('disabled');
                    option.disabled = false;
                } else {
                    optionContainer.classList.add('disabled');
                    option.disabled = true;
                }
            });
            return;
        }


        const matchingProducts = this.getMatchingProductsFromAll(this.activeFilters);

        const activeFiltersByType = {};
        Object.keys(this.activeFilters).forEach(type => {
            if (this.activeFilters[type].length > 0) {
                activeFiltersByType[type] = this.activeFilters[type];
            }
        });

        this.filterOptions.forEach(option => {
            const filterType = option.closest('.filter-dropdown').dataset.filterType;
            const filterValue = option.dataset.value;
            const optionContainer = option.closest('.filter-option');
            const label = optionContainer.querySelector('label');
            const isSelected = this.activeFilters[filterType].includes(filterValue);

            if (isSelected) {
                optionContainer.classList.remove('disabled');
                option.disabled = false;

                if (label) {
                    const labelText = label.textContent.split(' (')[0];
                    label.textContent = `${labelText} (${matchingProducts.length})`;
                }
                return;
            }

            const hasActiveFiltersOfThisType = this.activeFilters[filterType].length > 0;

            if (hasActiveFiltersOfThisType) {
                const tempFilters = JSON.parse(JSON.stringify(this.activeFilters));
                tempFilters[filterType].push(filterValue);

                const potentialMatchingProducts = this.getMatchingProductsFromAll(tempFilters);
                const count = potentialMatchingProducts.length;

                if (label) {
                    const labelText = label.textContent.split(' (')[0];
                    label.textContent = `${labelText} (${count})`;
                }

                if (count > 0) {
                    optionContainer.classList.remove('disabled');
                    option.disabled = false;
                } else {
                    optionContainer.classList.add('disabled');
                    option.disabled = true;
                }
            }
            else if (Object.keys(activeFiltersByType).length > 0) {
                const tempFilters = JSON.parse(JSON.stringify(this.activeFilters));
                tempFilters[filterType].push(filterValue);

                const potentialMatchingProducts = this.getMatchingProductsFromAll(tempFilters);
                const count = potentialMatchingProducts.length;

                if (label) {
                    const labelText = label.textContent.split(' (')[0];
                    label.textContent = `${labelText} (${count})`;
                }

                if (count > 0) {
                    optionContainer.classList.remove('disabled');
                    option.disabled = false;
                } else {
                    optionContainer.classList.add('disabled');
                    option.disabled = true;
                }
            }
        });
    }
    getMatchingProducts(filters) {
        const activeFilterTypes = Object.keys(filters).filter(type => filters[type].length > 0);

        return this.productData.filter(product => {
            let matches = true;
            activeFilterTypes.forEach(filterType => {
                const productHasAllMatchingTags = filters[filterType].every(filterValue => {
                    const normalizedFilterValue = filterValue.toLowerCase().replace(/\s+/g, ' ').trim();

                    return product.tagsByCategory[filterType].some(tag => {
                        const normalizedTag = tag.toLowerCase().replace(/\s+/g, ' ').trim();

                        if (filterType === 'composition') {
                            if (normalizedTag === normalizedFilterValue) {
                                return true;
                            }

                            if (normalizedTag.includes(normalizedFilterValue) ||
                                normalizedFilterValue.includes(normalizedTag)) {
                                return true;
                            }

                            if ((filterValue === 'Mineral Fillers' || filterValue === 'mineral fillers') &&
                                (normalizedTag.includes('mineral') || normalizedTag.includes('filler'))) {
                                return true;
                            }

                            if ((filterValue === 'Metal powder' || filterValue === 'metal powder') &&
                                (normalizedTag.includes('metal') || normalizedTag.includes('powder'))) {
                                return true;
                            }

                            if ((filterValue === 'Pearlescent Pigments' || filterValue === 'pearlescent pigments') &&
                                (normalizedTag.includes('pearl') || normalizedTag.includes('pigment'))) {
                                return true;
                            }

                            return false;
                        }

                        return normalizedTag === normalizedFilterValue;
                    });
                });
                if (!productHasAllMatchingTags) {
                    matches = false;
                }
            });
            return matches;
        });
    }

    getMatchingProductsFromAll(filters) {
        const activeFilterTypes = Object.keys(filters).filter(type => filters[type].length > 0);

        if (this.allProductData.length === 0) {
            return [];
        }

        if (activeFilterTypes.length === 0) {
            return this.allProductData;
        }

        const matchingProducts = this.allProductData.filter(product => {
            for (const filterType of activeFilterTypes) {
                if (filters[filterType].length === 0) continue;

                const productHasAllMatchingTags = filters[filterType].every(filterValue => {
                    if (!product.tagsByCategory || !product.tagsByCategory[filterType]) {
                        return false;
                    }

                    const normalizedFilterValue = filterValue.toLowerCase().replace(/\s+/g, ' ').trim();

                    return product.tagsByCategory[filterType].some(tag => {
                        const normalizedTag = tag.toLowerCase().replace(/\s+/g, ' ').trim();

                        if (filterType === 'composition') {
                            if (normalizedTag === normalizedFilterValue) {
                                return true;
                            }

                            if (normalizedTag.includes(normalizedFilterValue) ||
                                normalizedFilterValue.includes(normalizedTag)) {
                                return true;
                            }

                            if ((filterValue === 'Mineral Fillers' || filterValue === 'mineral fillers') &&
                                (normalizedTag.includes('mineral') || normalizedTag.includes('filler'))) {
                                return true;
                            }

                            if ((filterValue === 'Metal powder' || filterValue === 'metal powder') &&
                                (normalizedTag.includes('metal') || normalizedTag.includes('powder'))) {
                                return true;
                            }

                            if ((filterValue === 'Pearlescent Pigments' || filterValue === 'pearlescent pigments') &&
                                (normalizedTag.includes('pearl') || normalizedTag.includes('pigment'))) {
                                return true;
                            }

                            return false;
                        }

                        return normalizedTag === normalizedFilterValue;
                    });
                });

                if (!productHasAllMatchingTags) {
                    return false;
                }
            }

            return true;
        });

        return matchingProducts;
    }

    initProductCardHover() {
      this.productCards.forEach(card => {
        card.addEventListener('mouseenter', function() {
          const button = this.querySelector('.custom-product-card__arrow-button');
          if (button) { button.style.opacity = '1'; }
        });
        card.addEventListener('mouseleave', function() {
          const button = this.querySelector('.custom-product-card__arrow-button');
          if (button) { button.style.opacity = '0'; }
        });

        const cardLink = card.querySelector('.custom-product-card__link');
        if (cardLink) {
          cardLink.addEventListener('click', function(e) {
            e.stopPropagation();
          });
        }
      });
    }

    initAnimations() {
      if (typeof IntersectionObserver !== 'undefined') {
        const observer = new IntersectionObserver((entries) => {
          entries.forEach(entry => {
            if (entry.isIntersecting) {
              entry.target.classList.add('is-visible');
              observer.unobserve(entry.target);
            }
          });
        }, { threshold: 0.15, rootMargin: '0px 0px -50px 0px' });
        this.productCards.forEach(card => { observer.observe(card); });
      }
    }

   initEqualHeights() {
        requestAnimationFrame(() => {
            const visibleCards = Array.from(this.container.querySelectorAll('.custom-product-card')).filter(card => card.style.display !== 'none');
             if (visibleCards.length === 0) return;
            const productNames = visibleCards.map(card => card.querySelector('.custom-product-card__product-name')).filter(Boolean);
            const descriptions = visibleCards.map(card => card.querySelector('.custom-product-card__product-description')).filter(Boolean);
            const prices = visibleCards.map(card => card.querySelector('.custom-product-card__price')).filter(Boolean);
            const resetHeight = (elements) => elements.forEach(el => el.style.height = 'auto');
            resetHeight(productNames); resetHeight(descriptions); resetHeight(prices);
             const firstCard = visibleCards[0];
             const cardStyle = window.getComputedStyle(firstCard);
             const cardMargin = parseFloat(cardStyle.marginLeft) + parseFloat(cardStyle.marginRight);
             const cardWidthWithMargin = firstCard.offsetWidth + cardMargin;
             const containerWidth = firstCard.parentElement.offsetWidth;
             const columnsCount = Math.max(1, Math.round(containerWidth / cardWidthWithMargin));
            for (let i = 0; i < visibleCards.length; i += columnsCount) {
                 const rowCards = visibleCards.slice(i, i + columnsCount);
                const rowNames = rowCards.map(card => card.querySelector('.custom-product-card__product-name')).filter(Boolean);
                const rowDescriptions = rowCards.map(card => card.querySelector('.custom-product-card__product-description')).filter(Boolean);
                const rowPrices = rowCards.map(card => card.querySelector('.custom-product-card__price')).filter(Boolean);
                const getMaxHeight = (elements) => Math.max(0, ...elements.map(el => el.scrollHeight));
                const setHeight = (elements, height) => elements.forEach(el => el.style.height = `${height}px`);
                 if (rowNames.length > 0) setHeight(rowNames, getMaxHeight(rowNames));
                 if (rowDescriptions.length > 0) setHeight(rowDescriptions, getMaxHeight(rowDescriptions));
                 if (rowPrices.length > 0) setHeight(rowPrices, getMaxHeight(rowPrices));
            }
         });
     }
  }

  document.addEventListener('DOMContentLoaded', function() {
    const productGrids = document.querySelectorAll('.custom-product-grid');
    productGrids.forEach(grid => { new ProductGrid(grid); });
  });
</script>

{% schema %}
{
  "name": "Custom Product Grid",
  "class": "section",
  "settings": [
    {
      "type": "range",
      "id": "products_per_page",
      "min": 8,
      "max": 24,
      "step": 4,
      "default": 16,
      "label": "Products per page"
    },
    {
      "type": "range",
      "id": "columns_desktop",
      "min": 1,
      "max": 5,
      "step": 1,
      "default": 4,
      "label": "Number of columns on desktop"
    },
    {
      "type": "color_scheme",
      "id": "color_scheme",
      "label": "Color scheme",
      "default": "background-1"
    },
    {
      "type": "header",
      "content": "Product Card Settings"
    },
    {
      "type": "select",
      "id": "image_ratio",
      "options": [
        { "value": "adapt", "label": "Adapt to image" },
        { "value": "portrait", "label": "Portrait" },
        { "value": "square", "label": "Square" }
      ],
      "default": "square",
      "label": "Image ratio"
    },
    {
      "type": "select",
      "id": "image_shape",
      "options": [
        { "value": "default", "label": "Default" },
        { "value": "arch", "label": "Arch" },
        { "value": "blob", "label": "Blob" },
        { "value": "round", "label": "Round" }
      ],
      "default": "default",
      "label": "Image shape"
    },
    {
      "type": "checkbox",
      "id": "show_secondary_image",
      "default": true,
      "label": "Show second image on hover"
    },
    {
      "type": "checkbox",
      "id": "show_vendor",
      "default": false,
      "label": "Show vendor"
    },
    {
      "type": "checkbox",
      "id": "show_rating",
      "default": false,
      "label": "Show product rating"
    },
    {
      "type": "checkbox",
      "id": "enable_quick_add",
      "default": false,
      "label": "Enable quick add"
    },
    {
      "type": "header",
      "content": "Colors"
    },
    {
      "type": "color",
      "id": "text_color",
      "label": "Text color",
      "default": "#222222"
    },
    {
      "type": "color",
      "id": "background_color",
      "label": "Background color",
      "default": "#ffffff"
    },
    {
      "type": "header",
      "content": "Mobile Settings"
    },
    {
      "type": "select",
      "id": "columns_mobile",
      "default": "2",
      "label": "Number of columns on mobile",
      "options": [
        { "value": "1", "label": "1 column" },
        { "value": "2", "label": "2 columns" }
      ]
    },
    {
      "type": "range",
      "id": "product_spacing",
      "min": 10,
      "max": 50,
      "step": 5,
      "default": 25,
      "unit": "px",
      "label": "Space between products (desktop)"
    },
    {
      "type": "range",
      "id": "product_spacing_mobile",
      "min": 5,
      "max": 30,
      "step": 5,
      "default": 15,
      "unit": "px",
      "label": "Space between products (mobile)"
    },
    {
      "type": "header",
      "content": "Section Padding"
    },
    {
      "type": "range",
      "id": "padding_top",
      "min": 0,
      "max": 100,
      "step": 4,
      "unit": "px",
      "label": "Padding top",
      "default": 36
    },
    {
      "type": "range",
      "id": "padding_bottom",
      "min": 0,
      "max": 100,
      "step": 4,
      "unit": "px",
      "label": "Padding bottom",
      "default": 36
    }
  ],
  "presets": [
    {
      "name": "Custom Product Grid"
    }
  ]
}
{% endschema %}

<script>

  if (typeof Shopify === 'undefined' || typeof Shopify.formatMoney === 'undefined') {
    Shopify = Shopify || {};
    Shopify.formatMoney = function(cents, format) {
      if (typeof cents == 'string') { cents = cents.replace('.',''); }
      var value = '';
      var placeholderRegex = /\{\{\s*(\w+)\s*\}\}/;
      var formatString = (format || "£\{\{amount\}\}");

      function defaultOption(opt, def) {
        return (typeof opt == 'undefined' ? def : opt);
      }

      function formatWithDelimiters(number, precision, thousands, decimal) {
        precision = defaultOption(precision, 2);
        thousands = defaultOption(thousands, ',');
        decimal = defaultOption(decimal, '.');
        if (isNaN(number) || number == null) { return 0; }
        number = (number/100.0).toFixed(precision);
        var parts = number.split('.');
        var dollars = parts[0].replace(/(\d)(?=(\d\d\d)+(?!\d))/g, '$1' + thousands);
        var cents = parts[1] ? (decimal + parts[1]) : '';
        return dollars + cents;
      }

      switch(formatString.match(placeholderRegex)[1]) {
        case 'amount':
          value = formatWithDelimiters(cents, 2);
          break;
        case 'amount_no_decimals':
          value = formatWithDelimiters(cents, 0);
          break;
        case 'amount_with_comma_separator':
          value = formatWithDelimiters(cents, 2, '.', ',');
          break;
        case 'amount_no_decimals_with_comma_separator':
          value = formatWithDelimiters(cents, 0, '.', ',');
          break;
      }
      return formatString.replace(placeholderRegex, value);
    };
  }


  window.tagCategoryMappings = {
    // Use filters
    'interior': 'use', 'Interior': 'use',
    'exterior': 'use', 'Exterior': 'use',
    'floor': 'use', 'Floor': 'use',

    // Benefits filters
    'vapor permeability': 'benefits', 'Vapor permeability': 'benefits',
    'moisture resistance': 'benefits', 'Moisture resistance': 'benefits',
    'the possibility of restoration': 'benefits', 'The possibility of restoration': 'benefits',
    'suitable for flooring': 'benefits', 'Suitable for flooring': 'benefits',
    'soundproofing': 'benefits', 'Soundproofing': 'benefits',
    'washable': 'benefits', 'Washable': 'benefits',
    'high strength': 'benefits', 'High strength': 'benefits',
    'resistance to precipitation': 'benefits', 'Resistance to precipitation': 'benefits',
    'antifungal properties': 'benefits', 'Antifungal Properties': 'benefits',
    'hides defects of preparation': 'benefits', 'Hides defects of preparation': 'benefits',
    'resistance to dirt': 'benefits', 'Resistance to dirt': 'benefits',
    'changing shades': 'benefits', 'Changing shades': 'benefits',

    // Composition filters
    'metal powder': 'composition', 'Metal powder': 'composition',
    'acryl copolymers': 'composition', 'Acryl copolymers': 'composition',
    'mineral microspheres': 'composition', 'Mineral microspheres': 'composition',
    'reinforcing fibers': 'composition', 'Reinforcing fibers': 'composition',
    'mica': 'composition', 'Mica': 'composition',
    'lime': 'composition', 'Lime': 'composition',
    'pearlescent pigments': 'composition', 'Pearlescent Pigments': 'composition',
    'mineral fillers': 'composition', 'Mineral Fillers': 'composition',
    'ground travertine': 'composition', 'Ground travertine': 'composition',

    // Effect filters
    'matte': 'effect', 'Matte': 'effect',
    'glossy': 'effect', 'Glossy': 'effect',
    'metallic': 'effect', 'Metallic': 'effect',
    'pearly': 'effect', 'Pearly': 'effect',
    'wet silk': 'effect', 'Wet silk': 'effect',
    'fabric': 'effect', 'Fabric': 'effect',
    'rust': 'effect', 'Rust': 'effect',
    'textured': 'effect', 'Textured': 'effect',
    'venetian plaster': 'effect', 'Venetian plaster': 'effect',
    'antique wall': 'effect', 'Antique wall': 'effect',
    'concrete': 'effect', 'Concrete': 'effect',
    'marble': 'effect', 'Marble': 'effect',
    'travertine': 'effect', 'Travertine': 'effect',
    'stone': 'effect', 'Stone': 'effect',
    'with particles': 'effect', 'With particles': 'effect',
    'smooth': 'effect', 'Smooth': 'effect'
  };


  window.allCollectionProducts = [
    {% for product in collection.products %}
      {
        "id": "{{ product.id }}",
        "title": "{{ product.title | escape }}",
        "handle": "{{ product.handle }}",
        "url": "{{ product.url }}",
        "featured_image": "{{ product.featured_image | image_url: width: 300 }}",
        "price": "{{ product.price }}",
        "price_min": "{{ product.price_min }}",
        "price_varies": {{ product.price_varies }},
        "formatted_price": "{{ product.price | money }}",
        "formatted_price_min": "{{ product.price_min | money }}",
        "tags": "{{ product.tags | join: ',' | escape }}",
        "description": "{% if product.metafields.custom.short_description != blank %}{{ product.metafields.custom.short_description | escape }}{% else %}{{ product.description | strip_html | truncatewords: 15 | escape }}{% endif %}"
      }{% unless forloop.last %},{% endunless %}
    {% endfor %}
  ];

  window.allProductsCount = {{ collection.all_products_count }};

  window.formatMoney = function(cents) {
    if (!cents || isNaN(cents)) {
      return '£0.00';
    }

    cents = Math.round(parseFloat(cents));
    if (isNaN(cents)) {
      return '£0.00';
    }

    return Shopify.formatMoney(cents, "{{ shop.money_format }}");
  }

  if (window.allCollectionProducts.length < window.allProductsCount) {
    window.needToLoadAllProducts = true;
    window.fetchAllCollectionProducts = async function() {
      try {
        const pathParts = window.location.pathname.split('/');
        const collectionIndex = pathParts.indexOf('collections');
        if (collectionIndex === -1 || collectionIndex + 1 >= pathParts.length) {
          throw new Error('Could not determine collection handle from URL');
        }

        const collectionHandle = pathParts[collectionIndex + 1];


        const url = `/collections/${collectionHandle}/products.json?limit=250`;
        const response = await fetch(url);

        if (!response.ok) {
          throw new Error(`Failed to fetch products: ${response.status} ${response.statusText}`);
        }

        const data = await response.json();

        const newProducts = data.products.map(product => {
          let price = '0';
          let price_varies = false;
          let price_min = '0';

          if (product.variants && product.variants.length > 0) {
            price = product.variants[0].price;

            if (product.variants.length > 1) {
              const prices = product.variants.map(v => parseFloat(v.price));
              const minPrice = Math.min(...prices);
              const maxPrice = Math.max(...prices);
              price_varies = minPrice !== maxPrice;
              price_min = minPrice.toString();
            }
          } else if (product.price) {
            price = product.price;
          }

          const formatted_price = window.formatMoney(parseFloat(price) * 100);
          const formatted_price_min = window.formatMoney(parseFloat(price_min) * 100);

          return {
            id: product.id.toString(),
            title: product.title,
            handle: product.handle,
            url: `/products/${product.handle}`,
            featured_image: product.images && product.images.length > 0
              ? product.images[0].src.replace(/\.jpg|\.png/, '_300x300$&')
              : '',
            price: price,
            price_min: price_min,
            price_varies: price_varies,
            formatted_price: formatted_price,
            formatted_price_min: formatted_price_min,
            tags: product.tags.join(','),
            description: product.body_html
              ? product.body_html.replace(/<[^>]*>/g, '').substring(0, 100)
              : product.title
          };
        });

        window.allCollectionProducts = newProducts;

        const event = new CustomEvent('allProductsLoaded');
        document.dispatchEvent(event);

        return newProducts;
      } catch (error) {
        return [];
      }
    };
  }
</script>
