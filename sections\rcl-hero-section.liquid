{% style %}
  .hero-banner-{{ section.id }} .hero-banner {
    position: relative;
    overflow: hidden;
    margin-bottom: 40px;
    width: 100%;
  }
  
  .hero-banner-{{ section.id }} .hero-banner__image-container {
    position: relative;
    width: 100%;
    height: var(--banner-height, 600px);
    overflow: hidden;
    transition: height 0.3s ease;
  }
  
  .hero-banner-{{ section.id }} .hero-banner__image {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    object-fit: cover;
    transform: scale(1.03);
    transition: transform 8s cubic-bezier(0.215, 0.61, 0.355, 1);
    filter: saturate(1.05);
  }
  
  .hero-banner-{{ section.id }}:hover .hero-banner__image {
    transform: scale(1.08);
  }
  
  .hero-banner-{{ section.id }} .hero-banner__overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: var(--overlay-color, rgba(34, 34, 40, 0.4));
    opacity: var(--overlay-opacity, 0.4);
    z-index: 0;
  }
  
  .hero-banner-{{ section.id }} .hero-banner__content-container {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    display: flex;
    align-items: center;
    z-index: 0;
  }
  
  .hero-banner-{{ section.id }} .hero-banner__content {
    padding: 3rem 2rem;
    max-width: 800px;
    transition: all 0.4s ease-out;
    animation: fadeIn-{{ section.id }} 0.8s ease-out forwards;
  }
  
  @keyframes fadeIn-{{ section.id }} {
    from { opacity: 0; transform: translateY(20px); }
    to { opacity: 1; transform: translateY(0); }
  }
  
  .hero-banner-{{ section.id }} .text-left .hero-banner__content {
    margin-right: auto;
    text-align: left;
    padding-left: 8%;
  }
  
  .hero-banner-{{ section.id }} .text-center .hero-banner__content {
    margin: 0 auto;
    text-align: center;
  }
  
  .hero-banner-{{ section.id }} .text-right .hero-banner__content {
    margin-left: auto;
    text-align: right;
    padding-right: 8%;
  }
  
  .hero-banner-{{ section.id }} .hero-banner__heading {
    margin: 0 0 1.2rem;
    font-size: calc(var(--font-heading-scale, 1) * 4rem);
    letter-spacing: 0.08em;
    line-height: 1.1;
    text-transform: uppercase;
    font-weight: 700;
    text-shadow: 0 2px 8px rgba(0, 0, 0, 0.25);
    animation: fadeIn-{{ section.id }} 0.8s ease-out 0.2s forwards;
    opacity: 0;
    position: relative;
  }
  
  .hero-banner-{{ section.id }} .hero-banner__heading::after {
    content: "";
    display: block;
    width: 60px;
    height: 3px;
    background-color: currentColor;
    margin: 0.6rem auto 0;
    opacity: 0.8;
    transform: scaleX(0);
    transition: transform 0.5s ease-out 0.5s;
    animation: scaleIn-{{ section.id }} 0.7s ease-out 0.5s forwards;
  }
  
  @keyframes scaleIn-{{ section.id }} {
    from { transform: scaleX(0); }
    to { transform: scaleX(1); }
  }
  
  .hero-banner-{{ section.id }} .text-left .hero-banner__heading::after {
    margin-left: 0;
  }
  
  .hero-banner-{{ section.id }} .text-right .hero-banner__heading::after {
    margin-right: 0;
    margin-left: auto;
  }
  
  .hero-banner-{{ section.id }} .hero-banner__subheading {
    margin: 0 0 1.6rem;
    font-size: calc(var(--font-heading-scale, 1) * 1.8rem);
    font-weight: 400;
    line-height: 1.4;
    text-shadow: 0 1px 4px rgba(0, 0, 0, 0.2);
    animation: fadeIn-{{ section.id }} 0.8s ease-out 0.4s forwards;
    opacity: 0;
    letter-spacing: 0.02em;
  }
  
  .hero-banner-{{ section.id }} .hero-banner__description {
    margin: 0 0 2.5rem;
    font-size: 1.2rem;
    line-height: 1.7;
    max-width: 650px;
    animation: fadeIn-{{ section.id }} 0.8s ease-out 0.6s forwards;
    opacity: 0;
    font-weight: 300;
    letter-spacing: 0.01em;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
  }
  
  .hero-banner-{{ section.id }} .text-center .hero-banner__description {
    margin-left: auto;
    margin-right: auto;
  }
  
  .hero-banner-{{ section.id }} .hero-banner__button-container {
    display: flex;
    justify-content: center;
    animation: fadeIn-{{ section.id }} 0.8s ease-out 0.8s forwards;
    opacity: 0;
    width: auto;
  }
  
  .hero-banner-{{ section.id }} .text-left .hero-banner__button-container {
    justify-content: flex-start;
  }
  
  .hero-banner-{{ section.id }} .text-right .hero-banner__button-container {
    justify-content: flex-end;
  }
  
  .hero-banner-{{ section.id }} .hero-banner__button {
    padding: 0.95rem 2.5rem;
    font-size: 1.2rem;
    font-weight: 600;
    letter-spacing: 0.08em;
    line-height: 1.2;
    text-transform: uppercase;
    border: none;
    border-radius: 3px;
    transition: all 0.4s cubic-bezier(0.25, 0.8, 0.25, 1);
    display: inline-flex;
    align-items: center;
    justify-content: center;
    text-decoration: none;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    position: relative;
    min-width: 180px;
    text-align: center;
    overflow: hidden;
    z-index: 1;
    max-width: 290px;
  }
  
  .hero-banner-{{ section.id }} .hero-banner__button::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(to right, rgba(255,255,255,0) 0%, rgba(255,255,255,0.3) 50%, rgba(255,255,255,0) 100%);
    transform: translateX(-100%);
    transition: transform 0.85s cubic-bezier(0.19, 1, 0.22, 1);
    z-index: -1;
  }
  
  .hero-banner-{{ section.id }} .hero-banner__button:hover {
    transform: translateY(-3px);
    box-shadow: 0 7px 14px rgba(0, 0, 0, 0.15), 0 3px 6px rgba(0, 0, 0, 0.1);
    letter-spacing: 0.1em;
  }
  
  .hero-banner-{{ section.id }} .hero-banner__button:hover::before {
    transform: translateX(100%);
  }
  
  .hero-banner-{{ section.id }} .hero-banner__button:active {
    transform: translateY(-1px);
    box-shadow: 0 3px 8px rgba(0, 0, 0, 0.1);
  }
  
  /* For medium screens */
  @media screen and (max-width: 990px) {
    .hero-banner-{{ section.id }} .hero-banner__heading {
      font-size: calc(var(--font-heading-scale, 1) * 3rem);
    }
    
    .hero-banner-{{ section.id }} .hero-banner__subheading {
      font-size: calc(var(--font-heading-scale, 1) * 1.5rem);
    }
    
    .hero-banner-{{ section.id }} .hero-banner__description {
      font-size: 1.2rem;
    }
  }
  
  /* For mobile screens */
  @media screen and (max-width: 749px) {
    .hero-banner-{{ section.id }} .hero-banner__image-container {
      height: var(--banner-mobile-height, 450px);
    }
    
    .hero-banner-{{ section.id }} .hero-banner__content {
      padding: 2rem 1.5rem;
      max-width: 100%;
    }
    
    .hero-banner-{{ section.id }} .hero-banner__heading {
      font-size: calc(var(--font-heading-scale, 1) * 2.2rem);
      margin-bottom: 0.8rem;
    }
    
    .hero-banner-{{ section.id }} .hero-banner__subheading {
      font-size: calc(var(--font-heading-scale, 1) * 1.2rem);
      margin-bottom: 1rem;
    }
    
    .hero-banner-{{ section.id }} .hero-banner__description {
      font-size: 1.1rem;
      margin-bottom: 1.8rem;
    }
    
    .hero-banner-{{ section.id }} .hero-banner__button {
      padding: 0.85rem 2rem;
      min-width: 200px;
      font-size: 1.1rem;
    }
    
    .hero-banner-{{ section.id }} .text-left .hero-banner__content,
    .hero-banner-{{ section.id }} .text-right .hero-banner__content {
      padding-left: 1.5rem;
      padding-right: 1.5rem;
      text-align: center;
      margin: 0 auto;
    }
    
    .hero-banner-{{ section.id }} .text-left .hero-banner__button-container,
    .hero-banner-{{ section.id }} .text-right .hero-banner__button-container {
      justify-content: center;
    }
  }
  
  /* For very small screens */
  @media screen and (max-width: 480px) {
    .hero-banner-{{ section.id }} .hero-banner__heading {
      font-size: calc(var(--font-heading-scale, 1) * 1.8rem);
    }
    
    .hero-banner-{{ section.id }} .hero-banner__subheading {
      font-size: calc(var(--font-heading-scale, 1) * 1.1rem);
    }
    
    .hero-banner-{{ section.id }} .hero-banner__description {
      font-size: 1.1rem;
    }
    
    .hero-banner-{{ section.id }} .hero-banner__heading::after {
      width: 40px;
      height: 2px;
      margin-top: 0.5rem;
    }
    
    .hero-banner-{{ section.id }} .hero-banner__button-container {
      width: 100%;
    }
    
    .hero-banner-{{ section.id }} .hero-banner__button {
      width: 100%;
      min-width: unset;
      font-size: 1.1rem;
    }
  }
  
  /* Custom animation for content entry */
  @keyframes slideUp-{{ section.id }} {
    from { opacity: 0; transform: translateY(30px); }
    to { opacity: 1; transform: translateY(0); }
  }
  
  /* Additional visual enhancement with elegant color overlay */
  .hero-banner-{{ section.id }} .hero-banner__image-filter {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-image: linear-gradient(to bottom, rgba(0,0,0,0.1), rgba(0,0,0,0));
    pointer-events: none;
    z-index: 1;
    opacity: var(--filter-opacity, 0.5);
  }
{% endstyle %}

<div class="hero-banner-{{ section.id }} hero-banner" data-section-id="{{ section.id }}" data-section-type="hero-banner">
  <div class="hero-banner__image-container" 
       style="--banner-height: {{ section.settings.desktop_height }}px; 
              --banner-mobile-height: {{ section.settings.mobile_height }}px;"
       {% if section.settings.parallax_effect %}data-parallax="scroll" data-speed="0.8"{% endif %}>
    {% if section.settings.hero_image != blank %}
      {{ section.settings.hero_image | image_url: width: 2000 | image_tag: 
        class: 'hero-banner__image',
        loading: 'eager',
        width: section.settings.hero_image.width,
        height: section.settings.hero_image.height,
        sizes: '100vw'
      }}
    {% else %}
      {{ 'lifestyle-1' | placeholder_svg_tag: 'placeholder-svg hero-banner__image' }}
    {% endif %}
    
    <div class="hero-banner__overlay" 
         style="--overlay-color: {{ section.settings.overlay_color }}; 
                --overlay-opacity: {{ section.settings.overlay_opacity | divided_by: 100.0 }};">
    </div>
    
    {% if section.settings.enable_image_filter %}
    <div class="hero-banner__image-filter" 
         style="--filter-opacity: {{ section.settings.filter_opacity | divided_by: 100.0 }};">
    </div>
    {% endif %}
    
    <div class="hero-banner__content-container {{ section.settings.text_alignment }}">
      <div class="hero-banner__content page-width">
        {% for block in section.blocks %}
          {% case block.type %}
            {% when 'heading' %}
              <{{ block.settings.heading_size }} class="hero-banner__heading" style="color: {{ block.settings.heading_color }}" {{ block.shopify_attributes }}>
                {{ block.settings.heading }}
              </{{ block.settings.heading_size }}>
              
            {% when 'subheading' %}
              <h3 class="hero-banner__subheading" style="color: {{ block.settings.subheading_color }}" {{ block.shopify_attributes }}>
                {{ block.settings.subheading }}
              </h3>
              
            {% when 'text' %}
              <div class="hero-banner__description" style="color: {{ block.settings.description_color }}" {{ block.shopify_attributes }}>
                {{ block.settings.description }}
              </div>
              
            {% when 'button' %}
              {% if block.settings.button_label != blank %}
                <div class="hero-banner__button-container">
                  <a href="{{ block.settings.button_link }}" class="button hero-banner__button" 
                     style="background-color: {{ block.settings.button_bg_color }}; color: {{ block.settings.button_text_color }};" 
                     {{ block.shopify_attributes }}>
                    {{ block.settings.button_label }}
                  </a>
                </div>
              {% endif %}
          {% endcase %}
        {% endfor %}
      </div>
    </div>
  </div>
</div>

{% if section.settings.parallax_effect %}
  <script>
    document.addEventListener('DOMContentLoaded', function() {
      var parallaxElements = document.querySelectorAll('[data-parallax="scroll"]');
      
      function updateParallax() {
        parallaxElements.forEach(function(element) {
          var scrollPosition = window.pageYOffset;
          var speed = parseFloat(element.getAttribute('data-speed') || 0.5);
          var yPos = -(scrollPosition * speed);
          
          element.style.backgroundPosition = '50% ' + yPos + 'px';
        });
      }
      
      window.addEventListener('scroll', updateParallax);
      updateParallax(); 
    });
  </script>
{% endif %}

{% schema %}
  {
    "name": "Hero Banner",
    "tag": "section",
    "class": "section",
    "settings": [
      {
        "type": "image_picker",
        "id": "hero_image",
        "label": "Hero Background Image"
      },
      {
        "type": "select",
        "id": "text_alignment",
        "label": "Text Alignment",
        "options": [
          {
            "value": "text-left",
            "label": "Left"
          },
          {
            "value": "text-center",
            "label": "Center"
          },
          {
            "value": "text-right",
            "label": "Right"
          }
        ],
        "default": "text-center"
      },
      {
        "type": "range",
        "id": "desktop_height",
        "min": 100,
        "max": 900,
        "step": 10,
        "unit": "px",
        "label": "Desktop Height",
        "default": 400
      },
      {
        "type": "range",
        "id": "mobile_height",
        "min": 100,
        "max": 600,
        "step": 10,
        "unit": "px",
        "label": "Mobile Height",
        "default": 300
      },
      {
        "type": "color",
        "id": "overlay_color",
        "label": "Overlay Color",
        "default": "#222228"
      },
      {
        "type": "range",
        "id": "overlay_opacity",
        "min": 0,
        "max": 95,
        "step": 5,
        "unit": "%",
        "label": "Overlay Opacity",
        "default": 40
      },
      {
        "type": "checkbox",
        "id": "parallax_effect",
        "label": "Enable Parallax Effect",
        "default": false,
        "info": "Adds a subtle parallax scrolling effect to the background image"
      },
      {
        "type": "checkbox",
        "id": "enable_image_filter",
        "label": "Enable Image Filter",
        "default": false,
        "info": "Adds a subtle gradient overlay to enhance image appearance"
      },
      {
        "type": "range",
        "id": "filter_opacity",
        "min": 0,
        "max": 100,
        "step": 5,
        "unit": "%",
        "label": "Filter Intensity",
        "default": 50,
        "info": "Adjust the intensity of the image filter effect"
      }
    ],
    "blocks": [
      {
        "type": "heading",
        "name": "Heading",
        "limit": 1,
        "settings": [
          {
            "type": "text",
            "id": "heading",
            "default": "Elf Decor",
            "label": "Heading"
          },
          {
            "type": "select",
            "id": "heading_size",
            "options": [
              {
                "value": "h1",
                "label": "Large"
              },
              {
                "value": "h2",
                "label": "Medium"
              }
            ],
            "default": "h1",
            "label": "Heading Size"
          },
          {
            "type": "color",
            "id": "heading_color",
            "label": "Heading Color",
            "default": "#ffffff"
          }
        ]
      },
      {
        "type": "subheading",
        "name": "Subheading",
        "limit": 1,
        "settings": [
          {
            "type": "text",
            "id": "subheading",
            "default": "Premium Decorative Coatings & Plasters",
            "label": "Subheading"
          },
          {
            "type": "color",
            "id": "subheading_color",
            "label": "Subheading Color",
            "default": "#d1b073"
          }
        ]
      },
      {
        "type": "text",
        "name": "Description",
        "limit": 1,
        "settings": [
          {
            "type": "richtext",
            "id": "description",
            "default": "<p>Creating premium decorative finishes since 2006</p>",
            "label": "Description"
          },
          {
            "type": "color",
            "id": "description_color",
            "label": "Description Color",
            "default": "#ffffff"
          }
        ]
      },
      {
        "type": "button",
        "name": "Button",
        "limit": 1,
        "settings": [
          {
            "type": "text",
            "id": "button_label",
            "default": "Explore Our Products",
            "label": "Button Label"
          },
          {
            "type": "url",
            "id": "button_link",
            "label": "Button Link"
          },
          {
            "type": "color",
            "id": "button_bg_color",
            "label": "Button Background",
            "default": "#d1b073"
          },
          {
            "type": "color",
            "id": "button_text_color",
            "label": "Button Text",
            "default": "#ffffff"
          }
        ]
      }
    ],
    "presets": [
      {
        "name": "Hero Banner",
        "blocks": [
          {
            "type": "heading"
          },
          {
            "type": "subheading"
          },
          {
            "type": "text"
          },
          {
            "type": "button"
          }
        ]
      }
    ]
  }
{% endschema %}