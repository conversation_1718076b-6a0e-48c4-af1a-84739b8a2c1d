/**
 * Test script to verify default warehouse inventory validation
 * This script can be used to test the inventory validation functionality
 */

class InventoryValidationTest {
  constructor() {
    this.testResults = [];
  }

  // Test if default location inventory is being used instead of total inventory
  testDefaultLocationInventoryUsage() {
    console.log('🧪 Testing Default Location Inventory Usage...');
    
    const quantityInputs = document.querySelectorAll('[data-inventory-quantity]');
    let passed = 0;
    let failed = 0;

    quantityInputs.forEach((input, index) => {
      const defaultLocationInventory = input.dataset.inventoryQuantity;
      const totalInventory = input.dataset.totalInventoryQuantity;
      
      if (defaultLocationInventory && totalInventory) {
        const defaultQty = parseInt(defaultLocationInventory);
        const totalQty = parseInt(totalInventory);
        
        if (defaultQty <= totalQty) {
          console.log(`✅ Input ${index + 1}: Using default location inventory (${defaultQty}) instead of total (${totalQty})`);
          passed++;
        } else {
          console.log(`❌ Input ${index + 1}: Invalid inventory data - default (${defaultQty}) > total (${totalQty})`);
          failed++;
        }
      } else {
        console.log(`⚠️ Input ${index + 1}: Missing inventory data attributes`);
      }
    });

    this.testResults.push({
      test: 'Default Location Inventory Usage',
      passed,
      failed,
      total: quantityInputs.length
    });
  }

  // Test max attribute validation
  testMaxAttributeValidation() {
    console.log('🧪 Testing Max Attribute Validation...');
    
    const quantityInputs = document.querySelectorAll('[data-inventory-quantity][max]');
    let passed = 0;
    let failed = 0;

    quantityInputs.forEach((input, index) => {
      const maxValue = parseInt(input.getAttribute('max'));
      const inventoryQuantity = parseInt(input.dataset.inventoryQuantity);
      
      if (maxValue === inventoryQuantity) {
        console.log(`✅ Input ${index + 1}: Max attribute (${maxValue}) matches inventory quantity (${inventoryQuantity})`);
        passed++;
      } else {
        console.log(`❌ Input ${index + 1}: Max attribute (${maxValue}) doesn't match inventory quantity (${inventoryQuantity})`);
        failed++;
      }
    });

    this.testResults.push({
      test: 'Max Attribute Validation',
      passed,
      failed,
      total: quantityInputs.length
    });
  }

  // Test JavaScript validation functions
  testJavaScriptValidation() {
    console.log('🧪 Testing JavaScript Validation Functions...');

    // Test if validateInventoryBeforeAdd function exists and works
    if (typeof validateInventoryBeforeAdd === 'function') {
      console.log('✅ validateInventoryBeforeAdd function exists');

      // Test with a sample variant (if available)
      const firstInput = document.querySelector('[data-quantity-variant-id]');
      if (firstInput) {
        const variantId = firstInput.dataset.quantityVariantId;
        const inventoryQuantity = parseInt(firstInput.dataset.inventoryQuantity);

        // Test with valid quantity
        const validResult = validateInventoryBeforeAdd(variantId, 1);
        console.log(`✅ Valid quantity test: ${validResult ? 'PASSED' : 'FAILED'}`);

        // Test with excessive quantity (if inventory > 0)
        if (inventoryQuantity > 0) {
          const excessiveResult = validateInventoryBeforeAdd(variantId, inventoryQuantity + 10);
          console.log(`✅ Excessive quantity test: ${!excessiveResult ? 'PASSED' : 'FAILED'}`);
        }
      }
    } else {
      console.log('❌ validateInventoryBeforeAdd function not found');
    }

    // Test if getInventoryLimit function exists (in global.js context)
    const quantityInputElement = document.querySelector('quantity-input .quantity__input');
    if (quantityInputElement && quantityInputElement.closest('quantity-input')) {
      console.log('✅ Quantity input component found');
    } else {
      console.log('❌ Quantity input component not found');
    }
  }

  // Test Add to Cart button data attributes
  testAddToCartButtonAttributes() {
    console.log('🧪 Testing Add to Cart Button Data Attributes...');

    const addToCartButtons = document.querySelectorAll('[name="add"]');
    let passed = 0;
    let failed = 0;

    addToCartButtons.forEach((button, index) => {
      const variantId = button.dataset.variantId;
      const inventoryQuantity = button.dataset.inventoryQuantity;
      const inventoryManagement = button.dataset.inventoryManagement;
      const inventoryPolicy = button.dataset.inventoryPolicy;

      if (variantId && inventoryQuantity && inventoryManagement && inventoryPolicy) {
        console.log(`✅ Button ${index + 1}: Has all required data attributes (variant: ${variantId}, inventory: ${inventoryQuantity})`);
        passed++;
      } else {
        console.log(`❌ Button ${index + 1}: Missing data attributes - variant: ${variantId}, inventory: ${inventoryQuantity}, management: ${inventoryManagement}, policy: ${inventoryPolicy}`);
        failed++;
      }

      // Check if button state matches inventory
      const isDisabled = button.hasAttribute('disabled') || button.getAttribute('aria-disabled') === 'true';
      const buttonText = button.querySelector('span')?.textContent || '';
      const inventory = parseInt(inventoryQuantity);

      if (inventory <= 0 && !isDisabled) {
        console.log(`❌ Button ${index + 1}: Should be disabled (inventory: ${inventory}) but is enabled`);
        failed++;
      } else if (inventory > 0 && isDisabled && !buttonText.includes('Not enough stock')) {
        console.log(`❌ Button ${index + 1}: Should be enabled (inventory: ${inventory}) but is disabled`);
        failed++;
      } else {
        console.log(`✅ Button ${index + 1}: State matches inventory (inventory: ${inventory}, disabled: ${isDisabled})`);
        passed++;
      }
    });

    this.testResults.push({
      test: 'Add to Cart Button Attributes',
      passed,
      failed,
      total: addToCartButtons.length * 2 // We test 2 things per button
    });
  }

  // Test variant switching functionality
  testVariantSwitching() {
    console.log('🧪 Testing Variant Switching Inventory Updates...');

    const variantSelects = document.querySelectorAll('variant-selects input[type="radio"], variant-selects select');
    const quantityInput = document.querySelector('[data-quantity-variant-id]');
    const addToCartButton = document.querySelector('[name="add"]');

    if (variantSelects.length === 0) {
      console.log('⚠️ No variant selectors found - this might be a single variant product');
      return;
    }

    if (!quantityInput) {
      console.log('❌ No quantity input found');
      return;
    }

    if (!addToCartButton) {
      console.log('❌ No add to cart button found');
      return;
    }

    console.log(`✅ Found ${variantSelects.length} variant selectors`);

    // Store initial values
    const initialVariantId = quantityInput.dataset.quantityVariantId;
    const initialInventory = quantityInput.dataset.inventoryQuantity;
    const initialButtonVariantId = addToCartButton.dataset.variantId;
    const initialButtonInventory = addToCartButton.dataset.inventoryQuantity;

    console.log(`Initial quantity input - variant: ${initialVariantId}, inventory: ${initialInventory}`);
    console.log(`Initial button - variant: ${initialButtonVariantId}, inventory: ${initialButtonInventory}`);

    // Check if quantity input and button have matching data
    if (initialVariantId === initialButtonVariantId && initialInventory === initialButtonInventory) {
      console.log('✅ Quantity input and button data attributes match');
    } else {
      console.log('❌ Quantity input and button data attributes do not match');
    }

    // Test if variant change event listeners are set up
    if (typeof subscribe === 'function' && typeof PUB_SUB_EVENTS !== 'undefined') {
      console.log('✅ PubSub system available for variant change events');
    } else {
      console.log('❌ PubSub system not available');
    }

    // Test if update functions exist
    if (typeof updateInventoryDataAttributes === 'function') {
      console.log('✅ updateInventoryDataAttributes function exists (RCL template)');
    } else if (typeof updateMainProductInventoryDataAttributes === 'function') {
      console.log('✅ updateMainProductInventoryDataAttributes function exists (Main template)');
    } else {
      console.log('❌ No inventory update functions found');
    }
  }

  // Run all tests
  runAllTests() {
    console.log('🚀 Starting Inventory Validation Tests...\n');

    this.testDefaultLocationInventoryUsage();
    console.log('');

    this.testMaxAttributeValidation();
    console.log('');

    this.testAddToCartButtonAttributes();
    console.log('');

    this.testJavaScriptValidation();
    console.log('');

    this.testVariantSwitching();
    console.log('');

    this.printSummary();
  }

  // Print test summary
  printSummary() {
    console.log('📊 Test Summary:');
    console.log('================');
    
    let totalPassed = 0;
    let totalFailed = 0;
    let totalTests = 0;

    this.testResults.forEach(result => {
      console.log(`${result.test}: ${result.passed}/${result.total} passed`);
      totalPassed += result.passed;
      totalFailed += result.failed;
      totalTests += result.total;
    });

    console.log('================');
    console.log(`Overall: ${totalPassed}/${totalTests} tests passed`);
    
    if (totalFailed === 0) {
      console.log('🎉 All tests passed! Default warehouse inventory validation is working correctly.');
    } else {
      console.log(`⚠️ ${totalFailed} tests failed. Please review the implementation.`);
    }
  }
}

// Auto-run tests when script is loaded (for development/testing)
if (typeof window !== 'undefined') {
  // Wait for DOM to be ready
  if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', () => {
      const tester = new InventoryValidationTest();
      // Uncomment the line below to auto-run tests
      // tester.runAllTests();
    });
  } else {
    const tester = new InventoryValidationTest();
    // Uncomment the line below to auto-run tests
    // tester.runAllTests();
  }
}

// Export for manual testing
window.InventoryValidationTest = InventoryValidationTest;
