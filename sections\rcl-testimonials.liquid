{% schema %}
    {
      "name": "Testimonials & Projects",
      "tag": "section",
      "class": "section",
      "settings": [
        {
          "type": "text",
          "id": "title",
          "label": "Section Title",
          "default": "What Our Clients Say"
        },
        {
          "type": "color",
          "id": "title_color",
          "label": "Title Color",
          "default": "#222228"
        },
        {
          "type": "richtext",
          "id": "description",
          "label": "Section Description",
          "default": "<p>Discover what our clients have to say about our premium decorative coatings and expert application services.</p>"
        },
        {
          "type": "color",
          "id": "text_color",
          "label": "Text Color",
          "default": "#222228"
        },
        {
          "type": "color",
          "id": "background_color",
          "label": "Background Color",
          "default": "#f8f8f8"
        },
        {
          "type": "color",
          "id": "accent_color",
          "label": "Accent Color",
          "default": "#d1b073"
        },
        {
          "type": "select",
          "id": "layout",
          "label": "Layout",
          "options": [
            {
              "value": "testimonials_first",
              "label": "Testimonials First"
            },
            {
              "value": "projects_first",
              "label": "Projects First"
            }
          ],
          "default": "testimonials_first"
        }
      ],
      "blocks": [
        {
          "type": "testimonial",
          "name": "Testimonial",
          "settings": [
            {
              "type": "richtext",
              "id": "quote",
              "label": "Testimonial Quote",
              "default": "<p>\"The quality of Elf Decor's products is exceptional. The lime-based coating we chose creates a unique atmosphere in our living room that receives compliments from all our guests.\"</p>"
            },
            {
              "type": "text",
              "id": "author",
              "label": "Author Name",
              "default": "Maria Shevchenko"
            },
            {
              "type": "text",
              "id": "author_title",
              "label": "Author Title/Location",
              "default": "Kyiv, Residential Client"
            },
            {
              "type": "image_picker",
              "id": "author_image",
              "label": "Author Image"
            },
            {
              "type": "range",
              "id": "rating",
              "min": 0,
              "max": 5,
              "step": 0.5,
              "default": 5,
              "label": "Rating (out of 5)"
            }
          ]
        },
        {
          "type": "project",
          "name": "Project",
          "settings": [
            {
              "type": "image_picker",
              "id": "image",
              "label": "Project Image"
            },
            {
              "type": "text",
              "id": "title",
              "label": "Project Title",
              "default": "Luxury Apartment Renovation"
            },
            {
              "type": "text",
              "id": "location",
              "label": "Project Location",
              "default": "Odessa"
            },
            {
              "type": "select",
              "id": "category",
              "label": "Project Category",
              "options": [
                {
                  "value": "residential",
                  "label": "Residential"
                },
                {
                  "value": "commercial",
                  "label": "Commercial"
                },
                {
                  "value": "public",
                  "label": "Public Space"
                }
              ],
              "default": "residential"
            },
            {
              "type": "text",
              "id": "product_used",
              "label": "Product Used",
              "default": "TRAVERTINO STYLE"
            },
            {
              "type": "url",
              "id": "link",
              "label": "Project Link"
            }
          ]
        }
      ],
      "presets": [
        {
          "name": "Testimonials & Projects",
          "blocks": [
            {
              "type": "testimonial"
            },
            {
              "type": "testimonial"
            },
            {
              "type": "project"
            },
            {
              "type": "project"
            }
          ]
        }
      ]
    }
    {% endschema %}
    
    <div class="testimonials-projects-section" 
         style="background-color: {{ section.settings.background_color }}; 
                color: {{ section.settings.text_color }};"
         data-layout="{{ section.settings.layout }}">
      <div class="page-width">
        <div class="section-header text-center">
          <h2 class="section-title" style="color: {{ section.settings.title_color }};">
            {{ section.settings.title }}
          </h2>
          <div class="section-description rte">
            {{ section.settings.description }}
          </div>
        </div>
    
        <div class="testimonials-projects-container">
          {% assign testimonials = section.blocks | where: "type", "testimonial" %}
          {% assign projects = section.blocks | where: "type", "project" %}
          
          <div class="testimonials-container {% if section.settings.layout == 'projects_first' %}order-2{% endif %}">
            <div class="testimonials-slider">
              {% for block in testimonials %}
                <div class="testimonial-slide" {{ block.shopify_attributes }}>
                  <div class="testimonial-card">
                    <div class="testimonial-rating" style="color: {{ section.settings.accent_color }};">
                      {% assign half = block.settings.rating | modulo: 1 %}
                      {% assign int_rating = block.settings.rating | floor %}
                      {% assign next_int = int_rating | plus: 1 %}
                      {% for i in (1..5) %}
                        {% if i <= int_rating %}
                          <span class="star-icon full">★</span>
                        {% elsif half > 0 and i == next_int %}
                          <span class="star-icon half">★</span>
                        {% else %}
                          <span class="star-icon empty">★</span>
                        {% endif %}
                      {% endfor %}
                    </div>
                    
                    <div class="testimonial-quote rte">
                      {{ block.settings.quote }}
                    </div>
                    
                    <div class="testimonial-author">
                      {% if block.settings.author_image != blank %}
                        <div class="testimonial-author-image">
                          {{ block.settings.author_image | image_url: width: 100 | image_tag:
                            loading: 'lazy',
                            class: 'author-avatar',
                            height: 60,
                            width: 60
                          }}
                        </div>
                      {% endif %}
                      
                      <div class="testimonial-author-info">
                        <div class="testimonial-author-name">
                          {{ block.settings.author }}
                        </div>
                        
                        <div class="testimonial-author-title">
                          {{ block.settings.author_title }}
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              {% endfor %}
            </div>
            
            <div class="testimonials-navigation">
              <button class="testimonial-prev" aria-label="Previous testimonial">
                <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <path d="M15 18L9 12L15 6" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                </svg>
              </button>
              <button class="testimonial-next" aria-label="Next testimonial">
                <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <path d="M9 18L15 12L9 6" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                </svg>
              </button>
            </div>
          </div>
          
          <div class="projects-container {% if section.settings.layout == 'projects_first' %}order-1{% endif %}">
            <div class="projects-grid">
              {% for block in projects %}
                <div class="project-item" {{ block.shopify_attributes }}>
                  {% if block.settings.image != blank %}
                    <div class="project-image">
                      {{ block.settings.image | image_url: width: 800 | image_tag:
                        loading: 'lazy',
                        class: 'project-img',
                        widths: '275, 550, 710, 1420',
                        sizes: '(min-width: 750px) 50vw, 100vw'
                      }}
                      
                      <div class="project-overlay">
                        <div class="project-category" style="background-color: {{ section.settings.accent_color }};">
                          {{ block.settings.category | capitalize }}
                        </div>
                        
                        {% if block.settings.link != blank %}
                          <a href="{{ block.settings.link }}" class="project-link-button">
                            <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                              <path d="M18 13V19C18 19.5304 17.7893 20.0391 17.4142 20.4142C17.0391 20.7893 16.5304 21 16 21H5C4.46957 21 3.96086 20.7893 3.58579 20.4142C3.21071 20.0391 3 19.5304 3 19V8C3 7.46957 3.21071 6.96086 3.58579 6.58579C3.96086 6.21071 4.46957 6 5 6H11" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                              <path d="M15 3H21V9" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                              <path d="M10 14L21 3" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                            </svg>
                          </a>
                        {% endif %}
                      </div>
                    </div>
                  {% else %}
                    <div class="project-image placeholder">
                      {{ 'image' | placeholder_svg_tag: 'placeholder-svg' }}
                      
                      <div class="project-overlay">
                        <div class="project-category" style="background-color: {{ section.settings.accent_color }};">
                          {{ block.settings.category | capitalize }}
                        </div>
                      </div>
                    </div>
                  {% endif %}
                  
                  <div class="project-details">
                    <h3 class="project-title">
                      {{ block.settings.title }}
                    </h3>
                    
                    <div class="project-meta">
                      <div class="project-location">
                        <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
                          <path d="M8 8.5C9.10457 8.5 10 7.60457 10 6.5C10 5.39543 9.10457 4.5 8 4.5C6.89543 4.5 6 5.39543 6 6.5C6 7.60457 6.89543 8.5 8 8.5Z" stroke="currentColor" stroke-linecap="round" stroke-linejoin="round"/>
                          <path d="M13 6.5C13 11 8 14.5 8 14.5C8 14.5 3 11 3 6.5C3 5.17392 3.52678 3.90215 4.46447 2.96447C5.40215 2.02678 6.67392 1.5 8 1.5C9.32608 1.5 10.5979 2.02678 11.5355 2.96447C12.4732 3.90215 13 5.17392 13 6.5Z" stroke="currentColor" stroke-linecap="round" stroke-linejoin="round"/>
                        </svg>
                        <span>{{ block.settings.location }}</span>
                      </div>
                      
                      {% if block.settings.product_used != blank %}
                        <div class="project-product">
                          <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <path d="M13.5 4.5L8 2L2.5 4.5V11.5L8 14L13.5 11.5V4.5Z" stroke="currentColor" stroke-linecap="round" stroke-linejoin="round"/>
                            <path d="M2.5 4.5L8 7L13.5 4.5" stroke="currentColor" stroke-linecap="round" stroke-linejoin="round"/>
                            <path d="M8 14V7" stroke="currentColor" stroke-linecap="round" stroke-linejoin="round"/>
                          </svg>
                          <span>{{ block.settings.product_used }}</span>
                        </div>
                      {% endif %}
                    </div>
                  </div>
                </div>
              {% endfor %}
            </div>
          </div>
        </div>
      </div>
    </div>
    
    <style>
      .testimonials-projects-section {
        padding: 70px 0;
      }
      
      .section-title {
        letter-spacing: 0.05em;
        text-transform: uppercase;
      }
      
      .section-description {
        max-width: 800px;
        margin: 0 auto;
        font-size: 1.1rem;
        line-height: 1.6;
        margin-bottom: 60px;
      }
      
      .testimonials-projects-container {
        display: flex;
        flex-wrap: wrap;
        gap: 50px;
      }
      
      .testimonials-container,
      .projects-container {
        flex: 1 1 400px;
      }
      
      .order-1 {
        order: 1;
      }
      
      .order-2 {
        order: 2;
      }
      
      /* Testimonials */
      .testimonials-slider {
        position: relative;
        overflow: hidden;
      }
      
      .testimonial-slide {
        opacity: 0;
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        transition: opacity 0.5s ease;
      }
      
      .testimonial-slide:first-child {
        position: relative;
        opacity: 1;
      }
      
      .testimonial-card {
        padding: 30px;
        background-color: #ffffff;
        border-radius: 8px;
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
        height: 100%;
        display: flex;
        flex-direction: column;
      }
      
      .testimonial-rating {
        margin-bottom: 15px;
        font-size: 1.2rem;
        display: flex;
      }
      
      .star-icon {
        margin-right: 3px;
      }
      
      .star-icon.empty {
        opacity: 0.3;
      }
      
      .star-icon.half {
        position: relative;
        display: inline-block;
      }
      
      .star-icon.half:before {
        content: '★';
        position: absolute;
        left: 0;
        width: 50%;
        overflow: hidden;
      }
      
      .testimonial-quote {
        flex-grow: 1;
        font-size: 1.1rem;
        line-height: 1.6;
        font-style: italic;
        margin-bottom: 20px;
      }
      
      .testimonial-author {
        display: flex;
        align-items: center;
      }
      
      .testimonial-author-image {
        margin-right: 15px;
      }
      
      .author-avatar {
        border-radius: 50%;
        object-fit: cover;
      }
      
      .testimonial-author-name {
        font-weight: 600;
        font-size: 1.05rem;
        margin-bottom: 5px;
      }
      
      .testimonial-author-title {
        font-size: 0.9rem;
        opacity: 0.7;
      }
      
      .testimonials-navigation {
        display: flex;
        justify-content: center;
        gap: 20px;
        margin-top: 30px;
      }
      
      .testimonial-prev,
      .testimonial-next {
        background: none;
        border: 2px solid currentColor;
        border-radius: 50%;
        width: 44px;
        height: 44px;
        display: flex;
        align-items: center;
        justify-content: center;
        cursor: pointer;
        transition: background-color 0.3s, transform 0.3s;
      }
      
      .testimonial-prev:hover,
      .testimonial-next:hover {
        background-color: rgba(0, 0, 0, 0.05);
        transform: scale(1.05);
      }
      
      /* Projects */
      .projects-grid {
        display: grid;
        grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
        gap: 20px;
      }
      
      .project-item {
        background-color: #ffffff;
        border-radius: 8px;
        overflow: hidden;
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
        transition: transform 0.3s ease, box-shadow 0.3s ease;
      }
      
      .project-item:hover {
        transform: translateY(-5px);
        box-shadow: 0 8px 20px rgba(0, 0, 0, 0.1);
      }
      
      .project-image {
        position: relative;
        padding-top: 75%; /* 4:3 Aspect Ratio */
        overflow: hidden;
      }
      
      .project-img,
      .project-image .placeholder-svg {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        object-fit: cover;
        transition: transform 0.6s ease;
      }
      
      .project-item:hover .project-img {
        transform: scale(1.05);
      }
      
      .project-overlay {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        display: flex;
        flex-direction: column;
        justify-content: space-between;
        padding: 15px;
      }
      
      .project-category {
        align-self: flex-start;
        padding: 5px 10px;
        border-radius: 4px;
        font-size: 0.8rem;
        font-weight: 600;
        color: #ffffff;
        text-transform: uppercase;
      }
      
      .project-link-button {
        align-self: flex-end;
        width: 40px;
        height: 40px;
        border-radius: 50%;
        background-color: #ffffff;
        display: flex;
        align-items: center;
        justify-content: center;
        opacity: 0;
        transform: translateY(10px);
        transition: opacity 0.3s ease, transform 0.3s ease;
        color: currentColor;
      }
      
      .project-item:hover .project-link-button {
        opacity: 1;
        transform: translateY(0);
      }
      
      .project-details {
        padding: 20px;
      }
      
      .project-title {
        margin: 0 0 10px;
        font-size: 1.2rem;
        font-weight: 600;
      }
      
      .project-meta {
        display: flex;
        flex-wrap: wrap;
        gap: 15px;
        font-size: 0.9rem;
      }
      
      .project-location,
      .project-product {
        display: flex;
        align-items: center;
        gap: 5px;
        opacity: 0.7;
      }
      
      /* Responsive */
      @media screen and (max-width: 989px) {
        .testimonials-projects-container {
          flex-direction: column;
        }
        
        .order-1, .order-2 {
          order: inherit;
        }
      }
      
      @media screen and (max-width: 749px) {
        .testimonials-projects-section {
          padding: 50px 0;
        }
        
        .section-title {
          font-size: calc(var(--font-heading-scale) * 1.8rem);
        }
        
        .section-description {
          font-size: 1rem;
        }
        
        .testimonial-quote {
          font-size: 1rem;
        }
        
        .projects-grid {
          grid-template-columns: 1fr;
        }
      }
    </style>
    
    <script>
      document.addEventListener('DOMContentLoaded', function() {
        // Testimonials Slider
        const slides = document.querySelectorAll('.testimonial-slide');
        const prevBtn = document.querySelector('.testimonial-prev');
        const nextBtn = document.querySelector('.testimonial-next');
        let currentSlide = 0;
        
        function showSlide(index) {
          slides.forEach((slide, i) => {
            if (i === index) {
              slide.style.opacity = '1';
              slide.style.position = 'relative';
            } else {
              slide.style.opacity = '0';
              slide.style.position = 'absolute';
            }
          });
        }
        
        function nextSlide() {
          currentSlide = (currentSlide + 1) % slides.length;
          showSlide(currentSlide);
        }
        
        function prevSlide() {
          currentSlide = (currentSlide - 1 + slides.length) % slides.length;
          showSlide(currentSlide);
        }
        
        if (prevBtn && nextBtn && slides.length > 1) {
          prevBtn.addEventListener('click', prevSlide);
          nextBtn.addEventListener('click', nextSlide);
          
          // Auto rotate slides every 5 seconds
          setInterval(nextSlide, 5000);
        }
      });
    </script>