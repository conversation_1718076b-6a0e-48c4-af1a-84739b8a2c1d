{{ 'section-product-additional-content.css' | asset_url | stylesheet_tag }}
{{ 'simple-lightbox.css' | asset_url | stylesheet_tag }}

<div class="page-width">
  <section class="product-additional-content" id="ProductAdditionalContent-{{ section.id }}">
    {% for block in section.blocks %}
      {% case block.type %}
        {% when 'application_technologies' %}
          {% if product.metafields.custom.apptech != blank %}
            <div class="accordion-container {% if block.settings.open_by_default %}is-active{% endif %}" {{ block.shopify_attributes }}>
              <button class="accordion-trigger" aria-expanded="{% if block.settings.open_by_default %}true{% else %}false{% endif %}">
                <div class="accordion-icon">
                  <span class="icon-plus">
                    <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
                      <path d="M8 0V16" stroke="#D1B15A" stroke-width="1.5"/>
                      <path d="M16 8L0 8" stroke="#D1B15A" stroke-width="1.5"/>
                    </svg>
                  </span>
                </div>
                <h2 class="accordion-title">{{ block.settings.title | default: 'Application technologies' }}</h2>
              </button>
              <div class="accordion-content" {% if block.settings.open_by_default %}style="display: block;"{% endif %}>
                <div class="rte">
                  {% assign apptech_content = product.metafields.custom.apptech.value %}
                  {% for para in apptech_content.children %}
                    {% if para.type == "paragraph" %}
                      <p>
                        {% for text in para.children %}
                          {{ text.value }}
                        {% endfor %}
                      </p>
                    {% endif %}
                  {% endfor %}
                </div>

                {% if product.metafields.custom.applicationtech_images != blank %}
                  <div class="application-tech-images">
                    {% for image in product.metafields.custom.applicationtech_images.value %}
                      <div class="application-tech-image">
                        {{ image | image_url: width: 800 | image_tag:
                           loading: 'lazy',
                           class: 'tech-image',
                           widths: '400, 500, 600, 700, 800',
                           sizes: '(min-width: 990px) calc(33.33vw - 50px), (min-width: 750px) calc(50vw - 50px), calc(100vw - 50px)'
                        }}
                      </div>
                    {% endfor %}
                  </div>
                {% endif %}
              </div>
            </div>
          {% endif %}

        {% when 'gallery_textures' %}
          {% if product.metafields.custom.gallery_textures != blank %}
            {% assign texture_count = 0 %}
            {% for texture in product.metafields.custom.gallery_textures.value %}
              {% assign texture_count = texture_count | plus: 1 %}
            {% endfor %}

            <div class="accordion-container {% if block.settings.open_by_default %}is-active{% endif %}" {{ block.shopify_attributes }}>
              <button class="accordion-trigger"
                      aria-expanded="{% if block.settings.open_by_default %}true{% else %}false{% endif %}"
                      data-texture-count="{{ texture_count }}">
                <div class="accordion-icon">
                  <span class="icon-plus">
                    <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
                      <path d="M8 0V16" stroke="#D1B15A" stroke-width="1.5"/>
                      <path d="M16 8L0 8" stroke="#D1B15A" stroke-width="1.5"/>
                    </svg>
                  </span>
                </div>
                <h2 class="accordion-title gallery-title">
                  {{ block.settings.title | default: 'Gallery of textures' }}
                </h2>
              </button>
              <div class="accordion-content" {% if block.settings.open_by_default %}style="display: block;"{% endif %}>
                {% if product.metafields.custom.gallery_textures != blank %}
                  <div class="texture-gallery">
                    {% for texture in product.metafields.custom.gallery_textures.value %}
                      <div class="texture-item">
                        <a href="{{ texture | image_url: width: 2000 }}" class="texture-link" data-gallery-trigger>
                          {{ texture | image_url: width: 400 | image_tag:
                              loading: 'lazy',
                              class: 'texture-image',
                              widths: '400, 500, 600, 700, 800',
                              sizes: '(min-width: 990px) calc(33.33vw - 50px), (min-width: 750px) calc(50vw - 50px), calc(100vw - 50px)'
                          }}
                        </a>
                      </div>
                    {% endfor %}
                  </div>
                {% endif %}
              </div>
            </div>
          {% endif %}
      {% endcase %}
    {% endfor %}
  </section>
</div>

<script>
  (function() {
    var accordionTriggers = document.querySelectorAll('#ProductAdditionalContent-{{ section.id }} .accordion-trigger');

    accordionTriggers.forEach(function(trigger) {
      trigger.addEventListener('click', function() {
        var container = this.closest('.accordion-container');
        var isActive = container.classList.contains('is-active');
        var content = container.querySelector('.accordion-content');

        container.classList.toggle('is-active');
        this.setAttribute('aria-expanded', !isActive);

        if (isActive) {
          content.style.display = 'none';
        } else {
          content.style.display = 'block';
        }
      });

      if (trigger.hasAttribute('data-texture-count') && trigger.getAttribute('data-texture-count') > 0) {
        var title = trigger.querySelector('.gallery-title');
        if (title && title.textContent) {
          title.textContent = title.textContent.trim() + ' (' + trigger.getAttribute('data-texture-count') + ')';
        }
      }
    });

    var galleryTriggers = document.querySelectorAll('#ProductAdditionalContent-{{ section.id }} [data-gallery-trigger]');

    if (galleryTriggers.length > 0) {
      var lightbox = document.createElement('div');
      lightbox.className = 'simple-lightbox';
      lightbox.style.display = 'none';
      lightbox.style.position = 'fixed';
      lightbox.style.top = '0';
      lightbox.style.left = '0';
      lightbox.style.width = '100%';
      lightbox.style.height = '100%';
      lightbox.style.backgroundColor = 'rgba(0,0,0,0.9)';
      lightbox.style.zIndex = '9999';
      lightbox.style.display = 'none';
      lightbox.style.alignItems = 'center';
      lightbox.style.justifyContent = 'center';

      // Create container for the image and navigation
      var lightboxContainer = document.createElement('div');
      lightboxContainer.className = 'lightbox-container';
      lightboxContainer.style.position = 'relative';
      lightboxContainer.style.maxWidth = '95%';
      lightboxContainer.style.maxHeight = '95%';

      // Create navigation buttons
      var prevBtn = document.createElement('button');
      prevBtn.className = 'lightbox-nav lightbox-prev';
      prevBtn.setAttribute('aria-label', 'Previous image');
      prevBtn.innerHTML = '<svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M15 18L9 12L15 6" stroke="white" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/></svg>';
      prevBtn.style.position = 'absolute';
      prevBtn.style.left = '10px';
      prevBtn.style.top = '50%';
      prevBtn.style.transform = 'translateY(-50%)';
      prevBtn.style.background = 'rgba(0,0,0,0.5)';
      prevBtn.style.border = 'none';
      prevBtn.style.borderRadius = '50%';
      prevBtn.style.width = '40px';
      prevBtn.style.height = '40px';
      prevBtn.style.display = 'flex';
      prevBtn.style.alignItems = 'center';
      prevBtn.style.justifyContent = 'center';
      prevBtn.style.cursor = 'pointer';
      prevBtn.style.zIndex = '10';

      var nextBtn = document.createElement('button');
      nextBtn.className = 'lightbox-nav lightbox-next';
      nextBtn.setAttribute('aria-label', 'Next image');
      nextBtn.innerHTML = '<svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M9 6L15 12L9 18" stroke="white" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/></svg>';
      nextBtn.style.position = 'absolute';
      nextBtn.style.right = '10px';
      nextBtn.style.top = '50%';
      nextBtn.style.transform = 'translateY(-50%)';
      nextBtn.style.background = 'rgba(0,0,0,0.5)';
      nextBtn.style.border = 'none';
      nextBtn.style.borderRadius = '50%';
      nextBtn.style.width = '40px';
      nextBtn.style.height = '40px';
      nextBtn.style.display = 'flex';
      nextBtn.style.alignItems = 'center';
      nextBtn.style.justifyContent = 'center';
      nextBtn.style.cursor = 'pointer';
      nextBtn.style.zIndex = '10';

      var lightboxImg = document.createElement('img');
      lightboxImg.className = 'lightbox-img';
      lightboxImg.style.maxWidth = '95%';
      lightboxImg.style.maxHeight = '95%';
      lightboxImg.style.minWidth = '85vw'; // Ensure image is large enough on mobile
      lightboxImg.style.minHeight = '50vh'; // Ensure image has good height on mobile
      lightboxImg.style.objectFit = 'contain';
      lightboxImg.style.border = '2px solid white';
      lightboxImg.style.boxShadow = '0 0 20px rgba(0,0,0,0.5)';

      var closeBtn = document.createElement('button');
      closeBtn.className = 'lightbox-close';
      closeBtn.innerHTML = '&times;';
      closeBtn.style.position = 'absolute';
      closeBtn.style.top = '20px';
      closeBtn.style.right = '20px';
      closeBtn.style.fontSize = '30px';
      closeBtn.style.color = 'white';
      closeBtn.style.background = 'transparent';
      closeBtn.style.border = 'none';
      closeBtn.style.cursor = 'pointer';

      // Add elements to the DOM
      lightboxContainer.appendChild(lightboxImg);
      lightbox.appendChild(lightboxContainer);
      lightbox.appendChild(closeBtn);
      lightbox.appendChild(prevBtn);
      lightbox.appendChild(nextBtn);
      document.body.appendChild(lightbox);

      var preloadedImages = {};

      function preloadImage(url) {
        if (!url || preloadedImages[url]) return Promise.resolve(preloadedImages[url]);

        return new Promise((resolve) => {
          var img = new Image();
          img.onload = function() {
            preloadedImages[url] = img;
            resolve(img);
          };
          img.onerror = function() {
            console.error('Failed to preload image:', url);
            resolve(null);
          };
          img.src = url;
        });
      }

      function preloadAdjacentImages(currentIdx, images) {
        if (!images || images.length <= 1) return;

        var nextIdx = (currentIdx + 1) % images.length;
        var prevIdx = (currentIdx - 1 + images.length) % images.length;

        preloadImage(images[nextIdx]);
        preloadImage(images[prevIdx]);

        if (images.length > 4) {
          var nextIdx2 = (currentIdx + 2) % images.length;
          var prevIdx2 = (currentIdx - 2 + images.length) % images.length;

          setTimeout(() => {
            preloadImage(images[nextIdx2]);
            preloadImage(images[prevIdx2]);
          }, 100);
        }
      }

      var loadingIndicator = document.createElement('div');
      loadingIndicator.className = 'lightbox-loading';
      loadingIndicator.innerHTML = `
        <div class="loading-spinner">
          <div class="spinner-circle"></div>
        </div>
      `;
      loadingIndicator.style.position = 'absolute';
      loadingIndicator.style.top = '50%';
      loadingIndicator.style.left = '50%';
      loadingIndicator.style.transform = 'translate(-50%, -50%)';
      loadingIndicator.style.zIndex = '5';
      loadingIndicator.style.display = 'none';
      lightboxContainer.appendChild(loadingIndicator);

      var style = document.createElement('style');
      style.textContent = `
        .lightbox-loading {
          position: absolute;
          top: 50%;
          left: 50%;
          transform: translate(-50%, -50%);
          z-index: 5;
        }
        .loading-spinner {
          width: 50px;
          height: 50px;
          display: flex;
          justify-content: center;
          align-items: center;
        }
        .spinner-circle {
          width: 40px;
          height: 40px;
          border: 4px solid rgba(255, 255, 255, 0.3);
          border-radius: 50%;
          border-top-color: #fff;
          animation: spin 1s ease-in-out infinite;
        }
        @keyframes spin {
          to {
            transform: rotate(360deg);
          }
        }
      `;
      document.head.appendChild(style);

      var galleryImages = [];
      var currentIndex = 0;

      galleryTriggers.forEach(function(item) {
        galleryImages.push(item.getAttribute('href'));
      });

      function preloadAllImages() {
        for (var i = 0; i < Math.min(5, galleryImages.length); i++) {
          preloadImage(galleryImages[i]);
        }

        if (galleryImages.length > 5) {
          setTimeout(() => {
            for (var i = 5; i < galleryImages.length; i++) {
              preloadImage(galleryImages[i]);
            }
          }, 1000);
        }
      }

      preloadAllImages();

      galleryTriggers.forEach(function(item, index) {
        item.addEventListener('click', function(e) {
          e.preventDefault();

          var imgSrc = this.getAttribute('href');
          currentIndex = index;

          lightboxImg.style.opacity = '0.3';
          loadingIndicator.style.display = 'block';

          if (preloadedImages[imgSrc]) {
            lightboxImg.src = imgSrc;
            lightboxImg.style.opacity = '1';
            loadingIndicator.style.display = 'none';
          } else {
            preloadImage(imgSrc).then(() => {
              lightboxImg.src = imgSrc;
              lightboxImg.style.opacity = '1';
              loadingIndicator.style.display = 'none';
            });
          }

          updateNavButtons();
          lightbox.style.display = 'flex';
          document.body.style.overflow = 'hidden';

          preloadAdjacentImages(currentIndex, galleryImages);
        });
      });

      function updateNavButtons() {
        if (galleryImages.length <= 1) {
          prevBtn.style.display = 'none';
          nextBtn.style.display = 'none';
          return;
        }

        prevBtn.style.display = '';
        nextBtn.style.display = '';
      }

      prevBtn.addEventListener('click', function() {
        currentIndex = (currentIndex - 1 + galleryImages.length) % galleryImages.length;
        var imgSrc = galleryImages[currentIndex];

        lightboxImg.style.opacity = '0.3';
        loadingIndicator.style.display = 'block';

        if (preloadedImages[imgSrc]) {
          lightboxImg.src = imgSrc;
          lightboxImg.style.opacity = '1';
          loadingIndicator.style.display = 'none';
        } else {
          preloadImage(imgSrc).then(() => {
            lightboxImg.src = imgSrc;
            lightboxImg.style.opacity = '1';
            loadingIndicator.style.display = 'none';
          });
        }

        preloadAdjacentImages(currentIndex, galleryImages);
      });

      nextBtn.addEventListener('click', function() {
        currentIndex = (currentIndex + 1) % galleryImages.length;
        var imgSrc = galleryImages[currentIndex];

        lightboxImg.style.opacity = '0.3';
        loadingIndicator.style.display = 'block';

        if (preloadedImages[imgSrc]) {
          lightboxImg.src = imgSrc;
          lightboxImg.style.opacity = '1';
          loadingIndicator.style.display = 'none';
        } else {
          preloadImage(imgSrc).then(() => {
            lightboxImg.src = imgSrc;
            lightboxImg.style.opacity = '1';
            loadingIndicator.style.display = 'none';
          });
        }

        preloadAdjacentImages(currentIndex, galleryImages);
      });

      closeBtn.addEventListener('click', function() {
        lightbox.style.display = 'none';
        document.body.style.overflow = '';
      });

      lightbox.addEventListener('click', function(e) {
        if (e.target === lightbox) {
          lightbox.style.display = 'none';
          document.body.style.overflow = '';
        }
      });

      document.addEventListener('keydown', function(e) {
        if (lightbox.style.display !== 'flex') return;

        switch (e.key) {
          case 'Escape':
            lightbox.style.display = 'none';
            document.body.style.overflow = '';
            break;
          case 'ArrowLeft':
            prevBtn.click();
            break;
          case 'ArrowRight':
            nextBtn.click();
            break;
        }
      });
    }
  })();
</script>

{% if product.metafields.custom.apptech != blank %}
  <script>
    document.addEventListener('DOMContentLoaded', function() {
      var appTechContent = document.querySelector('#ProductAdditionalContent-{{ section.id }} .rte');
      if (appTechContent) {
        var rawContent = {{ product.metafields.custom.apptech.value | json }};
        var formattedContent = '';

        if (rawContent && rawContent.children) {
          rawContent.children.forEach(function(para) {
            if (para.type === 'paragraph') {
              var paraText = '';

              para.children.forEach(function(textNode) {
                if (textNode.type === 'text') {
                  paraText += textNode.value;
                }
              });

              if (paraText.trim()) {
                formattedContent += '<p>' + paraText + '</p>';
              } else {
                formattedContent += '<p><br></p>';
              }
            }
          });
        }

        appTechContent.innerHTML = formattedContent;
      }
    });
  </script>
{% endif %}

{% schema %}
{
  "name": "Product Content",
  "tag": "section",
  "class": "section",
  "settings": [
    {
      "type": "header",
      "content": "Section Settings"
    },
    {
      "type": "paragraph",
      "content": "Display additional product content from metafields"
    },
    {
      "type": "color_scheme",
      "id": "color_scheme",
      "label": "Color scheme",
      "default": "background-1"
    },
    {
      "type": "header",
      "content": "Spacing"
    },
    {
      "type": "range",
      "id": "padding_top",
      "min": 0,
      "max": 100,
      "step": 4,
      "unit": "px",
      "label": "Top padding",
      "default": 36
    },
    {
      "type": "range",
      "id": "padding_bottom",
      "min": 0,
      "max": 100,
      "step": 4,
      "unit": "px",
      "label": "Bottom padding",
      "default": 36
    }
  ],
  "blocks": [
    {
      "type": "application_technologies",
      "name": "Application Technologies",
      "limit": 1,
      "settings": [
        {
          "type": "text",
          "id": "title",
          "label": "Title",
          "default": "Application technologies"
        },
        {
          "type": "checkbox",
          "id": "open_by_default",
          "label": "Open by default",
          "default": true
        }
      ]
    },
    {
      "type": "gallery_textures",
      "name": "Gallery of Textures",
      "limit": 1,
      "settings": [
        {
          "type": "text",
          "id": "title",
          "label": "Title",
          "default": "Gallery of textures"
        },
        {
          "type": "checkbox",
          "id": "show_count",
          "label": "Show count of textures",
          "default": true
        },
        {
          "type": "checkbox",
          "id": "open_by_default",
          "label": "Open by default",
          "default": false
        }
      ]
    }
  ],
  "presets": [
    {
      "name": "Product Content",
      "blocks": [
        {
          "type": "application_technologies"
        },
        {
          "type": "gallery_textures"
        }
      ]
    }
  ]
}
{% endschema %}