{{ 'rcl-color-texture-explorer.css' | asset_url | stylesheet_tag: preload: true }}

<div class="texture-explorer-section"
     style="background-color: {{ section.settings.background_color }};
            color: {{ section.settings.text_color }};"
     data-layout="{{ section.settings.layout }}"
     data-mobile-click="{{ section.settings.mobile_thumbnail_click }}">
  <div class="page-width">
    <div class="section-header text-center">
      <h2 class="section-title" style="color: {{ section.settings.title_color }};">
        {{ section.settings.title }}
      </h2>

      <div class="section-description rte">
        {{ section.settings.description }}
      </div>
    </div>

    <div class="texture-explorer-container" data-layout="{{ section.settings.layout }}">
      <div class="texture-sidebar">
        <div class="category-selector" style="display: none;">
          <select id="category-dropdown" class="category-dropdown" aria-label="Select Texture Category">
            <option value="all">All Textures</option>
          </select>
        </div>

        <div class="sidebar-heading">
          <h3 class="sidebar-title">Texture Views</h3>
          <p class="sidebar-description">Different applications of the selected texture.</p>
        </div>
        <div id="sample-images-list" class="sample-images-list" aria-live="polite">
        </div>
      </div>

      <div class="texture-preview-container">
        <div class="texture-samples-container">
          <div class="samples-heading">
            <h3 class="samples-title">Available Textures</h3>
            <p class="samples-description">Browse our collection of textures. Click on any texture to view details.</p>
          </div>
          <div class="texture-samples" id="texture-samples-grid" role="grid">
          </div>
        </div>

        <div class="preview-room">
          <div class="texture-image-main">
            <div class="image-placeholder">
              <svg viewBox="0 0 1200 800" xmlns="http://www.w3.org/2000/svg">
                <rect width="1200" height="800" fill="#F0F0F0"/>
                <text x="600" y="400" font-family="sans-serif" font-size="40" text-anchor="middle" fill="#555">Loading textures...</text>
              </svg>
            </div>

            <button class="zoom-button" aria-label="View Full Size">
              <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M15 3H21V9" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                <path d="M9 21H3V15" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                <path d="M21 3L14 10" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                <path d="M3 21L10 14" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
              </svg>
            </button>

            <div class="texture-sample-info">
              <div class="sample-name">Select a texture</div>
              <div class="sample-code"></div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <div id="texture-lightbox" class="texture-lightbox" role="dialog" aria-modal="true" aria-label="Texture Lightbox">
      <div class="lightbox-content">
        <button class="close-lightbox" aria-label="Close Lightbox">
          <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M18 6L6 18" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
            <path d="M6 6L18 18" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
          </svg>
        </button>
        <div class="lightbox-container">
          <div class="lightbox-sidebar">
            <div class="lightbox-thumbnails"></div>
          </div>
          <div class="lightbox-main">
            <button class="prev-lightbox" aria-label="Previous Image">
              <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M15 18L9 12L15 6" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
              </svg>
            </button>
            <button class="next-lightbox" aria-label="Next Image">
              <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M9 18L15 12L9 6" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
              </svg>
            </button>
            <img src="" alt="" class="lightbox-image">
            <div class="lightbox-info">
              <div class="lightbox-name"></div>
              <div class="lightbox-code"></div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

<script>
  window.openLightboxDirectly = function(imgSrc, name, code) {
    if (!imgSrc) {
      return;
    }

    const lightbox = document.getElementById('texture-lightbox');
    if (!lightbox) {
      return;
    }

    const lightboxImage = lightbox.querySelector('.lightbox-image');
    const lightboxName = lightbox.querySelector('.lightbox-name');
    const lightboxCode = lightbox.querySelector('.lightbox-code');

    if (!lightboxImage || !lightboxName || !lightboxCode) {
      return;
    }

    let loadingIndicator = lightbox.querySelector('.lightbox-loading');
    const lightboxMain = lightbox.querySelector('.lightbox-main');

    if (lightboxMain && !loadingIndicator) {
      loadingIndicator = document.createElement('div');
      loadingIndicator.className = 'lightbox-loading';
      loadingIndicator.innerHTML = `
        <div class="loading-spinner">
          <div class="spinner-circle"></div>
        </div>
      `;
      loadingIndicator.style.position = 'absolute';
      loadingIndicator.style.top = '50%';
      loadingIndicator.style.left = '50%';
      loadingIndicator.style.transform = 'translate(-50%, -50%)';
      loadingIndicator.style.zIndex = '5';
      lightboxMain.appendChild(loadingIndicator);
    }

    if (loadingIndicator) {
      lightboxImage.style.opacity = '0.3';
      loadingIndicator.style.display = 'block';
    }

    lightboxImage.src = imgSrc;
    lightboxImage.alt = name || 'Texture Full Size';
    lightboxName.textContent = name || '';
    lightboxCode.textContent = code || '';

    const lightboxThumbnails = lightbox.querySelector('.lightbox-thumbnails');
    if (lightboxThumbnails) {
      lightboxThumbnails.innerHTML = '';
    }

    if (window.selectedSample && window.selectedSample.thumbnailUrls && window.selectedSample.thumbnailUrls.length > 0) {

      const prevButton = lightbox.querySelector('.prev-lightbox');
      const nextButton = lightbox.querySelector('.next-lightbox');

      if (window.selectedSample.thumbnailUrls.length <= 1) {
        if (prevButton) prevButton.style.display = 'none';
        if (nextButton) nextButton.style.display = 'none';
      } else {
        if (prevButton) {
          prevButton.style.display = '';
          prevButton.replaceWith(prevButton.cloneNode(true));
          const newPrevButton = lightbox.querySelector('.prev-lightbox');

          newPrevButton.addEventListener('click', function(e) {
            e.stopPropagation();
            const allThumbnails = lightbox.querySelectorAll('.lightbox-thumbnail');
            const currentActive = lightbox.querySelector('.lightbox-thumbnail.active');
            const currentActiveIndex = currentActive ? parseInt(currentActive.getAttribute('data-index')) : -1;

            const prevIndex = (currentActiveIndex - 1 + allThumbnails.length) % allThumbnails.length;

            const prevThumbnail = lightbox.querySelector(`.lightbox-thumbnail[data-index="${prevIndex}"]`);
            if (prevThumbnail) {
              prevThumbnail.scrollIntoView({ behavior: 'smooth', block: 'nearest', inline: 'center' });

              const fullsizeUrl = prevThumbnail.getAttribute('data-fullsize');
              if (fullsizeUrl) {
                lightboxImage.src = fullsizeUrl;

                lightbox.querySelectorAll('.lightbox-thumbnail').forEach(thumb => thumb.classList.remove('active'));
                prevThumbnail.classList.add('active');
              }
            }
          });
        }

        if (nextButton) {
          nextButton.style.display = '';
          nextButton.replaceWith(nextButton.cloneNode(true));
          const newNextButton = lightbox.querySelector('.next-lightbox');

          newNextButton.addEventListener('click', function(e) {
            e.stopPropagation();
            const allThumbnails = lightbox.querySelectorAll('.lightbox-thumbnail');
            const currentActive = lightbox.querySelector('.lightbox-thumbnail.active');
            const currentActiveIndex = currentActive ? parseInt(currentActive.getAttribute('data-index')) : -1;

            const nextIndex = (currentActiveIndex + 1) % allThumbnails.length;

            const nextThumbnail = lightbox.querySelector(`.lightbox-thumbnail[data-index="${nextIndex}"]`);
            if (nextThumbnail) {
              nextThumbnail.scrollIntoView({ behavior: 'smooth', block: 'nearest', inline: 'center' });

              const fullsizeUrl = nextThumbnail.getAttribute('data-fullsize');
              if (fullsizeUrl) {
                lightboxImage.src = fullsizeUrl;

                lightbox.querySelectorAll('.lightbox-thumbnail').forEach(thumb => thumb.classList.remove('active'));
                nextThumbnail.classList.add('active');
              }
            }
          });
        }
      }

      const currentIndex = window.selectedSample.thumbnailUrls.findIndex(url => url === imgSrc);

      window.selectedSample.thumbnailUrls.forEach((thumbUrl, index) => {
        const fullSizeUrl = window.selectedSample.fullSizeUrls && window.selectedSample.fullSizeUrls[index]
          ? window.selectedSample.fullSizeUrls[index] : thumbUrl;

        const thumbnailDiv = document.createElement('div');
        thumbnailDiv.className = 'lightbox-thumbnail';
        if (index === currentIndex) {
          thumbnailDiv.classList.add('active');
        }

        thumbnailDiv.innerHTML = `<img src="${thumbUrl}" alt="${window.selectedSample.name} - View ${index + 1}">`;
        thumbnailDiv.setAttribute('data-index', index);
        thumbnailDiv.setAttribute('data-fullsize', fullSizeUrl);

        thumbnailDiv.onclick = function(e) {
          e.stopPropagation();
          const clickedFullsize = this.getAttribute('data-fullsize');

          lightboxImage.style.opacity = '0.3';
          if (loadingIndicator) loadingIndicator.style.display = 'block';

          lightbox.querySelectorAll('.lightbox-thumbnail').forEach(thumb => thumb.classList.remove('active'));
          this.classList.add('active');

          this.scrollIntoView({ behavior: 'smooth', block: 'nearest', inline: 'center' });

          (async () => {
            await preloadImage(clickedFullsize);
            lightboxImage.src = clickedFullsize;
            lightboxImage.style.opacity = '1';
            if (loadingIndicator) loadingIndicator.style.display = 'none';
          })();
        };

        lightboxThumbnails.appendChild(thumbnailDiv);
      });

      setTimeout(() => {
        const activeThumbnail = lightbox.querySelector('.lightbox-thumbnail.active');
        if (activeThumbnail) {
          activeThumbnail.scrollIntoView({ behavior: 'smooth', block: 'nearest', inline: 'center' });
        }
      }, 100);
    } else {
      const prevButton = lightbox.querySelector('.prev-lightbox');
      const nextButton = lightbox.querySelector('.next-lightbox');
      if (prevButton) prevButton.style.display = 'none';
      if (nextButton) nextButton.style.display = 'none';
    }

    lightbox.classList.add('active');
    document.body.style.overflow = 'hidden';

    (async () => {
      await preloadImage(imgSrc);
      lightboxImage.style.opacity = '1';
      if (loadingIndicator) loadingIndicator.style.display = 'none';
    })();

    document.removeEventListener('keydown', lightboxKeydownHandler);

    function lightboxKeydownHandler(e) {
      if (lightbox.classList.contains('active')) {
        if (e.key === 'Escape') {
          lightbox.classList.remove('active');
          document.body.style.overflow = '';
        } else if (e.key === 'ArrowLeft') {
          e.preventDefault();
          e.stopPropagation();

          const allThumbnails = Array.from(lightbox.querySelectorAll('.lightbox-thumbnail'));
          if (allThumbnails.length <= 1) return;

          const currentActive = lightbox.querySelector('.lightbox-thumbnail.active');
          if (!currentActive) return;

          const currentIndex = allThumbnails.indexOf(currentActive);
          if (currentIndex === -1) return;

          const prevIndex = (currentIndex - 1 + allThumbnails.length) % allThumbnails.length;
          const prevThumbnail = allThumbnails[prevIndex];

          if (prevThumbnail) {
            prevThumbnail.scrollIntoView({ behavior: 'smooth', block: 'nearest', inline: 'center' });
            const fullsizeUrl = prevThumbnail.getAttribute('data-fullsize');
            if (fullsizeUrl) {
              lightboxImage.src = fullsizeUrl;
              allThumbnails.forEach(thumb => thumb.classList.remove('active'));
              prevThumbnail.classList.add('active');
            }
          }
        } else if (e.key === 'ArrowRight') {
          e.preventDefault();
          e.stopPropagation();

          const allThumbnails = Array.from(lightbox.querySelectorAll('.lightbox-thumbnail'));
          if (allThumbnails.length <= 1) return;

          const currentActive = lightbox.querySelector('.lightbox-thumbnail.active');
          if (!currentActive) return;

          const currentIndex = allThumbnails.indexOf(currentActive);
          if (currentIndex === -1) return;

          const nextIndex = (currentIndex + 1) % allThumbnails.length;
          const nextThumbnail = allThumbnails[nextIndex];

          if (nextThumbnail) {
            nextThumbnail.scrollIntoView({ behavior: 'smooth', block: 'nearest', inline: 'center' });
            const fullsizeUrl = nextThumbnail.getAttribute('data-fullsize');
            if (fullsizeUrl) {
              lightboxImage.src = fullsizeUrl;
              allThumbnails.forEach(thumb => thumb.classList.remove('active'));
              nextThumbnail.classList.add('active');
            }
          }
        }
      }
    }

    document.addEventListener('keydown', lightboxKeydownHandler);
  };

  document.addEventListener('DOMContentLoaded', function() {

    const storefrontAccessToken = '6f1c6dc3151eaa1da2486263817f9dd4';
    const shopDomain = window.Shopify && window.Shopify.shop ? window.Shopify.shop : window.location.hostname;

    const accentColor = '{{ section.settings.accent_color }}';
    const root = document.documentElement;
    root.style.setProperty('--accent-color', accentColor);
    root.style.setProperty('--background-color', '{{ section.settings.background_color }}');
    root.style.setProperty('--text-color', '{{ section.settings.text_color }}');

    const section = document.querySelector('.texture-explorer-section');
    if (section) {
      const currentValue = section.getAttribute('data-mobile-click');
      if (!currentValue) {
        section.setAttribute('data-mobile-click', '{{ section.settings.mobile_thumbnail_click }}');
      }
    }

    window.selectedSample = null;
    let selectedSample = null;
    let validSamples = [];
    let categoriesData = [];
    let samplesData = [];
    let imageUrlMap = {};

    const categoryDropdown = document.getElementById('category-dropdown');
    const sampleImagesList = document.getElementById('sample-images-list');
    const samplesGrid = document.getElementById('texture-samples-grid');
    const mainPreviewContainer = document.querySelector('.texture-image-main');
    const imagePlaceholder = mainPreviewContainer?.querySelector('.image-placeholder');
    const sampleNameDisplay = document.querySelector('.texture-sample-info .sample-name');
    const sampleCodeDisplay = document.querySelector('.texture-sample-info .sample-code');
    const lightbox = document.getElementById('texture-lightbox');
    const lightboxImage = document.querySelector('.lightbox-image');
    const lightboxName = document.querySelector('.lightbox-name');
    const lightboxCode = document.querySelector('.lightbox-code');
    const closeButton = document.querySelector('.close-lightbox');
    let zoomButton = document.querySelector('.zoom-button');

    async function fetchGraphQL(queryPayload) {
      try {
        const endpoint = `https://${shopDomain}/api/2024-01/graphql.json`;

        const response = await fetch(endpoint, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'X-Shopify-Storefront-Access-Token': storefrontAccessToken
          },
          body: queryPayload
        });

        if (!response.ok) {
          const errorBody = await response.text();
          throw new Error(`GraphQL query failed: ${response.status} - ${errorBody}`);
        }

        const result = await response.json();
        if (result.errors) {
          return null;
        }

        return result.data;
      } catch (error) {
        return null;
      }
    }

    async function fetchTextureSamples() {
      const query = `
        query GetTextureSamples {
          metaobjects(type: "texture_sample", first: 100) {
            edges {
              node {
                id
                handle
                fields {
                  key
                  value
                  references(first: 10) {
                    edges {
                      node {
                        ... on MediaImage {
                          id
                          image {
                            url(transform: {maxWidth: 800, maxHeight: 800, crop: CENTER})
                            originalSrc
                            width
                            height
                          }
                        }
                        ... on Metaobject {
                          id
                          handle
                          type
                        }
                      }
                    }
                  }
                }
              }
            }
          }
        }
      `;

      const data = await fetchGraphQL(JSON.stringify({ query }));
      return data?.metaobjects?.edges || [];
    }

    async function fetchTextureCategories() {
      const query = `
        query GetTextureCategories {
          metaobjects(type: "texture_category", first: 100) {
            edges {
              node {
                id
                handle
                fields {
                  key
                  value
                  references(first: 10) {
                    edges {
                      node {
                        ... on MediaImage {
                          id
                          image {
                            url
                          }
                        }
                        ... on Metaobject {
                          id
                          handle
                          type
                        }
                      }
                    }
                  }
                }
              }
            }
          }
        }
      `;

      const data = await fetchGraphQL(JSON.stringify({ query }));

      return data?.metaobjects?.edges || [];
    }

    async function convertGidsToImageUrls(gids) {
      if (!gids || gids.length === 0) {
          return {};
      }

      const batchSize = 50;
      const batches = [];
      for (let i = 0; i < gids.length; i += batchSize) {
        batches.push(gids.slice(i, i + batchSize));
      }

      const currentImageUrlMap = {};
      for (const batch of batches) {
        const cleanedGids = batch.map(gid => typeof gid === 'string' ? gid.trim() : null).filter(Boolean);
        if (cleanedGids.length === 0) continue;

        const query = `
          query GetMediaImages($ids: [ID!]!) {
            nodes(ids: $ids) {
              ... on MediaImage {
                id
                image {
                  url(transform: {maxWidth: 1200})
                  originalSrc
                }
              }
            }
          }
        `;

        const variables = { ids: cleanedGids };
        const data = await fetchGraphQL(JSON.stringify({ query, variables }));

        if (data && data.nodes) {
          data.nodes.forEach(node => {
            if (node && node.id && node.image) {
              const imageUrl = node.image.url || node.image.originalSrc;
              if (imageUrl) {
                  currentImageUrlMap[node.id] = imageUrl;
              }
            }
          });
        }
      }
      return currentImageUrlMap;
    }

    function updateSidebarImages(sample) {
      if (!sampleImagesList) {
        return;
      }

      const sidebarElement = sampleImagesList.closest('.texture-sidebar');
      if (!sidebarElement) {
        return;
      }

      const existingHelper = sidebarElement.querySelector('.sample-list-helper');
      if (existingHelper) {
        existingHelper.remove();
      }

      sampleImagesList.innerHTML = '';

      if (sample && sample.thumbnailUrls && sample.thumbnailUrls.length > 0) {

        const helperMessage = document.createElement('div');
        helperMessage.className = 'sample-list-helper';
        helperMessage.innerHTML = `<p>Click on any image below to view different application of <strong>${sample.name}</strong></p>`;

        sidebarElement.insertBefore(helperMessage, sampleImagesList);

        sample.thumbnailUrls.forEach((imageUrl, index) => {
          const fullSizeUrl = sample.fullSizeUrls && sample.fullSizeUrls[index] ? sample.fullSizeUrls[index] : imageUrl;

          const imageItem = document.createElement('div');
          imageItem.className = 'sample-image-item';
          imageItem.setAttribute('data-index', index);
          imageItem.setAttribute('data-fullsize', fullSizeUrl);

          imageItem.innerHTML = `
            <div class="sample-image-wrapper">
              <img src="${imageUrl}" alt="${sample.name}" loading="lazy" class="sample-image"
                   data-fullsize="${fullSizeUrl}"
                   data-name="${sample.name}"
                   data-code="${sample.code}"
                   data-mobile-action="{{ section.settings.mobile_thumbnail_click }}">
              <div class="sample-image-info">
                <span class="texture-name">${sample.name}</span>
              </div>
            </div>
          `;

          sampleImagesList.appendChild(imageItem);

          imageItem.setAttribute('onclick', `
            if (window.innerWidth < 768) {
              let behavior = document.querySelector('.texture-explorer-section').getAttribute('data-mobile-click');

              if (!behavior) {
                behavior = 'normal';
              }

              if (behavior === 'lightbox') {
                const img = this.querySelector('img.sample-image');
                if (img && img.src) {
                  window.openLightboxDirectly(img.src, '${sample.name}', '${sample.code}');
                  event.preventDefault();
                  event.stopPropagation();
                  return false;
                }
              } else if (behavior === 'scroll') {
                document.querySelector('.preview-room')?.scrollIntoView({behavior: 'smooth', block: 'start'});
                event.preventDefault();
                event.stopPropagation();
                return false;
              }
            }
          `);

          imageItem.addEventListener('click', function(event) {
            if (window.innerWidth < 768) {
              let behavior = document.querySelector('.texture-explorer-section').getAttribute('data-mobile-click');

              if (!behavior) {
                behavior = 'normal';
              }

              if (behavior === 'lightbox') {
                const img = this.querySelector('img.sample-image');
                if (img && img.src) {
                  window.openLightboxDirectly(img.src, sample.name, sample.code);
                  event.preventDefault();
                  event.stopPropagation();
                  return;
                }
              } else if (behavior === 'scroll') {
                const previewRoom = document.querySelector('.preview-room');
                if (previewRoom) {
                  previewRoom.scrollIntoView({ behavior: 'smooth', block: 'start' });
                  event.preventDefault();
                  event.stopPropagation();
                  return;
                }
              }
            }

            const fullsizeUrl = this.getAttribute('data-fullsize') || imageUrl;
            updateMainPreview(imageUrl, sample.name, sample.code, fullsizeUrl);
            document.querySelectorAll('.sample-image-item').forEach(item => item.classList.remove('active'));
            this.classList.add('active');
          });

          if (index === 0) {
              imageItem.classList.add('active');
          }
        });
      } else {
          sampleImagesList.innerHTML = '<p>No additional images available for this texture.</p>';
      }
    }

    function updateMainPreview(imageUrl, name, code, fullSizeUrl = null) {
        let mainPreviewImage = mainPreviewContainer?.querySelector('.main-preview-image');
        const currentPlaceholder = mainPreviewContainer?.querySelector('.image-placeholder');

        if (!mainPreviewImage && currentPlaceholder && imageUrl) {
            mainPreviewImage = document.createElement('img');
            mainPreviewImage.className = 'main-preview-image';
            mainPreviewContainer.replaceChild(mainPreviewImage, currentPlaceholder);
            mainPreviewImage.addEventListener('click', handleMainImageClick);
        } else if (!mainPreviewImage && !currentPlaceholder && imageUrl) {
            mainPreviewImage = document.createElement('img');
            mainPreviewImage.className = 'main-preview-image';
            mainPreviewContainer.insertBefore(mainPreviewImage, mainPreviewContainer.firstChild);
            mainPreviewImage.addEventListener('click', handleMainImageClick);
        } else if (mainPreviewImage && !imageUrl && !currentPlaceholder) {
            mainPreviewImage.remove();
            mainPreviewImage = null;
            const newPlaceholder = document.createElement('div');
            newPlaceholder.className = 'image-placeholder';
            newPlaceholder.innerHTML = `
                <svg viewBox="0 0 1200 800" xmlns="http://www.w3.org/2000/svg">
                  <rect width="1200" height="800" fill="#F0F0F0"/>
                  <text x="600" y="400" font-family="sans-serif" font-size="40" text-anchor="middle" fill="#555">Select a texture</text>
                </svg>`;
            mainPreviewContainer.insertBefore(newPlaceholder, mainPreviewContainer.firstChild);
        } else if (mainPreviewImage && !imageUrl && currentPlaceholder) {
              mainPreviewImage.remove();
              mainPreviewImage = null;
        }

        if (mainPreviewImage && imageUrl) {
            mainPreviewImage.src = imageUrl;
            mainPreviewImage.alt = name || 'Texture Preview';
            mainPreviewImage.setAttribute('data-fullsize', fullSizeUrl || imageUrl);
            mainPreviewImage.setAttribute('data-name', name || '');
            mainPreviewImage.setAttribute('data-code', code || '');
        }

        if (sampleNameDisplay) sampleNameDisplay.textContent = name || 'Select a texture';
        if (sampleCodeDisplay) sampleCodeDisplay.textContent = code || '';
    }

    function filterMainGrid(categoryId) {
      const sampleElements = samplesGrid.querySelectorAll('.texture-sample');
      let firstVisibleSample = null;

      sampleElements.forEach(sampleEl => {
          const button = sampleEl.querySelector('.sample-button');
          if (!button) return;

          const sampleCategory = sampleEl.getAttribute('data-category');
          const isVisible = (categoryId === 'all' || sampleCategory === categoryId);
          sampleEl.style.display = isVisible ? '' : 'none';
          if (isVisible && !firstVisibleSample) {
              firstVisibleSample = button;
          }
      });

      if (firstVisibleSample) {
        firstVisibleSample.click();
      } else {
          updateMainPreview(null, 'No textures found', '');
          if (sampleImagesList) sampleImagesList.innerHTML = '';
          document.querySelectorAll('.sample-button').forEach(btn => btn.classList.remove('active'));
      }
    }

    function handleSampleButtonClick() {
      const sampleId = this.getAttribute('data-sample-id');
      selectedSample = validSamples.find(sample => sample.id === sampleId);
      window.selectedSample = selectedSample;

      if (!selectedSample) {
          return;
      }

      updateSidebarImages(selectedSample);

      const firstThumbnailUrl = selectedSample.thumbnailUrls?.[0];
      const firstFullSizeUrl = selectedSample.fullSizeUrls?.[0] || firstThumbnailUrl;
      updateMainPreview(firstThumbnailUrl, selectedSample.name, selectedSample.code, firstFullSizeUrl);

      document.querySelectorAll('.sample-button').forEach(btn => btn.classList.remove('active'));
      this.classList.add('active');
    }

    const preloadedImages = {};

    function preloadImage(url) {
      if (!url) return Promise.resolve(null);
      if (preloadedImages[url]) return Promise.resolve(preloadedImages[url]);

      return new Promise((resolve) => {
        const img = new Image();
        img.onload = () => {
          preloadedImages[url] = img;
          resolve(img);
        };
        img.onerror = () => {
          resolve(null);
        };
        img.src = url;
      });
    }

    async function preloadAdjacentImages(currentIndex, urls) {
      if (!urls || !urls.length) return;

      const nextIndex = (currentIndex + 1) % urls.length;
      const prevIndex = (currentIndex - 1 + urls.length) % urls.length;

      preloadImage(urls[nextIndex]);
      preloadImage(urls[prevIndex]);

      const nextIndex2 = (currentIndex + 2) % urls.length;
      const prevIndex2 = (currentIndex - 2 + urls.length) % urls.length;

      setTimeout(() => {
        preloadImage(urls[nextIndex2]);
        preloadImage(urls[prevIndex2]);
      }, 100);
    }

    function openLightbox(imgSrc, name, code) {
      if (!lightbox) {
        return;
      }

      if (!lightboxImage) {
        return;
      }

      if (!lightboxName) {
        return;
      }

      if (!lightboxCode) {
        return;
      }

      if (!imgSrc) {
        return;
      }

      lightboxImage.style.opacity = '0.3';

      let loadingIndicator = lightbox.querySelector('.lightbox-loading');
      const lightboxMain = lightbox.querySelector('.lightbox-main');

      if (!lightboxMain) {
        return;
      }

      if (!loadingIndicator) {
        loadingIndicator = document.createElement('div');
        loadingIndicator.className = 'lightbox-loading';
        loadingIndicator.innerHTML = `
          <div class="loading-spinner">
            <div class="spinner-circle"></div>
          </div>
        `;
        loadingIndicator.style.position = 'absolute';
        loadingIndicator.style.top = '50%';
        loadingIndicator.style.left = '50%';
        loadingIndicator.style.transform = 'translate(-50%, -50%)';
        loadingIndicator.style.zIndex = '5';
        lightboxMain.appendChild(loadingIndicator);
      }
      loadingIndicator.style.display = 'block';

      const thumbnailItems = Array.from(document.querySelectorAll('.sample-image-item'));
      const activeIndex = thumbnailItems.findIndex(item => item.classList.contains('active'));

      const lightboxThumbnails = lightbox.querySelector('.lightbox-thumbnails');
      if (lightboxThumbnails && selectedSample && selectedSample.thumbnailUrls) {
        lightboxThumbnails.innerHTML = '';

        if (selectedSample.thumbnailUrls.length <= 1) {
          const prevButton = lightbox.querySelector('.prev-lightbox');
          const nextButton = lightbox.querySelector('.next-lightbox');
          if (prevButton) prevButton.style.display = 'none';
          if (nextButton) nextButton.style.display = 'none';
        }

        const allFullSizeUrls = selectedSample.thumbnailUrls.map((thumbUrl, idx) =>
          selectedSample.fullSizeUrls && selectedSample.fullSizeUrls[idx] ?
          selectedSample.fullSizeUrls[idx] : thumbUrl
        );

        allFullSizeUrls.forEach(url => {
          preloadImage(url);
        });

        selectedSample.thumbnailUrls.forEach((thumbUrl, index) => {
          const fullSizeUrl = allFullSizeUrls[index];

          const thumbnailDiv = document.createElement('div');
          thumbnailDiv.className = 'lightbox-thumbnail';
          if (index === activeIndex) {
            thumbnailDiv.classList.add('active');
          }

          thumbnailDiv.innerHTML = `<img src="${thumbUrl}" alt="${selectedSample.name} - View ${index + 1}">`;
          thumbnailDiv.setAttribute('data-index', index);
          thumbnailDiv.setAttribute('data-fullsize', fullSizeUrl);

          thumbnailDiv.addEventListener('click', function() {
            const clickedIndex = parseInt(this.getAttribute('data-index'));
            const clickedFullsize = this.getAttribute('data-fullsize');

            lightboxImage.style.opacity = '0.3';
            loadingIndicator.style.display = 'block';

            lightbox.querySelectorAll('.lightbox-thumbnail').forEach(thumb => thumb.classList.remove('active'));
            this.classList.add('active');

            this.scrollIntoView({ behavior: 'smooth', block: 'nearest', inline: 'center' });

            (async () => {
              await preloadImage(clickedFullsize);
              lightboxImage.src = clickedFullsize;
              lightboxImage.style.opacity = '1';
              loadingIndicator.style.display = 'none';
            })();

            updateLightboxNavButtons(clickedIndex, selectedSample.thumbnailUrls.length);

            preloadAdjacentImages(clickedIndex, allFullSizeUrls);
          });

          lightboxThumbnails.appendChild(thumbnailDiv);
        });
      }

      const prevButton = lightbox.querySelector('.prev-lightbox');
      const nextButton = lightbox.querySelector('.next-lightbox');

      updateLightboxNavButtons(activeIndex, thumbnailItems.length);

      (async () => {
        await preloadImage(imgSrc);
        lightboxImage.src = imgSrc;
        lightboxImage.style.opacity = '1';
        loadingIndicator.style.display = 'none';
      })();

      lightboxImage.alt = name || 'Texture Full Size';
      lightboxName.textContent = name || '';
      lightboxCode.textContent = code || '';

      lightbox.classList.add('active');
      document.body.style.overflow = 'hidden';

      if (selectedSample && selectedSample.thumbnailUrls) {
        const allFullSizeUrls = selectedSample.thumbnailUrls.map((thumbUrl, idx) =>
          selectedSample.fullSizeUrls && selectedSample.fullSizeUrls[idx] ?
          selectedSample.fullSizeUrls[idx] : thumbUrl
        );
        preloadAdjacentImages(activeIndex, allFullSizeUrls);

        setTimeout(() => {
          const activeThumbnail = lightbox.querySelector('.lightbox-thumbnail.active');
          if (activeThumbnail) {
            activeThumbnail.scrollIntoView({ behavior: 'smooth', block: 'nearest', inline: 'center' });
          }
        }, 100);
      }
    }

    function updateLightboxNavButtons(activeIndex, totalItems) {
      const prevButton = lightbox.querySelector('.prev-lightbox');
      const nextButton = lightbox.querySelector('.next-lightbox');

      const numItems = typeof totalItems === 'number' && !isNaN(totalItems) ? totalItems : 0;
      const currentIndex = typeof activeIndex === 'number' && !isNaN(activeIndex) ? activeIndex : 0;

      if (numItems <= 1) {
        if (prevButton) prevButton.style.display = 'none';
        if (nextButton) nextButton.style.display = 'none';
        return;
      }

      if (prevButton) {
        prevButton.style.display = '';
        const prevIndex = (currentIndex - 1 + numItems) % numItems;
        prevButton.setAttribute('data-index', prevIndex);
      }

      if (nextButton) {
        nextButton.style.display = '';
        const nextIndex = (currentIndex + 1) % numItems;
        nextButton.setAttribute('data-index', nextIndex);
      }
    }

    function closeLightbox() {
      if (!lightbox) return;
      lightbox.classList.remove('active');
      document.body.style.overflow = '';

      setupZoomButtonListener();

      const mainPreviewImage = mainPreviewContainer?.querySelector('.main-preview-image');
      if (mainPreviewImage) {
        mainPreviewImage.removeEventListener('click', handleMainImageClick);
        mainPreviewImage.addEventListener('click', handleMainImageClick);
      }
    }

    function setupZoomButtonListener() {
      if (!zoomButton) return;

      const newZoomButton = zoomButton.cloneNode(true);
      if (zoomButton.parentNode) {
        zoomButton.parentNode.replaceChild(newZoomButton, zoomButton);
        zoomButton = newZoomButton;
      }

      zoomButton.addEventListener('click', function() {
        const mainPreviewImage = mainPreviewContainer?.querySelector('.main-preview-image');
        if (mainPreviewImage) {
          const fullsizeUrl = mainPreviewImage.getAttribute('data-fullsize');
          const name = mainPreviewImage.getAttribute('data-name');
          const code = mainPreviewImage.getAttribute('data-code');

          if (!selectedSample) {
            return;
          }

          if (fullsizeUrl) openLightbox(fullsizeUrl, name, code);
        }
      });
    }

    function handleMainImageClick() {
        const fullsizeUrl = this.getAttribute('data-fullsize');
        const name = this.getAttribute('data-name');
        const code = this.getAttribute('data-code');

        if (!selectedSample) {
            return;
        }

        if (fullsizeUrl) openLightbox(fullsizeUrl, name, code);
    }

    function setupEventListeners() {
      categoryDropdown?.addEventListener('change', function() {
        filterMainGrid(this.value);
      });

      samplesGrid?.addEventListener('click', function(event) {
          const button = event.target.closest('.sample-button');
          if (button) {
              handleSampleButtonClick.call(button);
          }
      });

      document.addEventListener('click', function(event) {
        if (window.innerWidth >= 768) {
          return;
        }

        const section = document.querySelector('.texture-explorer-section');
        let mobileClickBehavior = section.getAttribute('data-mobile-click');

        if (!mobileClickBehavior) {
          mobileClickBehavior = 'normal';
        }

        if (mobileClickBehavior === 'normal') {
          return;
        }

        if (event.target.classList.contains('sample-image')) {
          const img = event.target;

          if (mobileClickBehavior === 'lightbox') {
            const imgSrc = img.src;
            const sampleName = img.getAttribute('data-name') || img.alt || '';
            const sampleCode = img.getAttribute('data-code') || '';

            if (imgSrc) {
              window.openLightboxDirectly(imgSrc, sampleName, sampleCode);
              event.preventDefault();
              event.stopPropagation();
              return;
            }
          } else if (mobileClickBehavior === 'scroll') {
            const previewRoom = document.querySelector('.preview-room');
            if (previewRoom) {
              previewRoom.scrollIntoView({ behavior: 'smooth', block: 'start' });
              return;
            }
          }
        }

        const imageItem = event.target.closest('.sample-image-item');
        if (imageItem) {

          if (mobileClickBehavior === 'lightbox') {
            const img = imageItem.querySelector('img.sample-image');
            if (img && img.src) {
              const imgSrc = img.src;
              const sampleName = imageItem.querySelector('.texture-name')?.textContent || '';
              const sampleCode = '';

              window.openLightboxDirectly(imgSrc, sampleName, sampleCode);
              event.preventDefault();
              event.stopPropagation();
            }
          } else if (mobileClickBehavior === 'scroll') {
            const previewRoom = document.querySelector('.preview-room');
            if (previewRoom) {
              previewRoom.scrollIntoView({ behavior: 'smooth', block: 'start' });
            }
          }
        }
      });

      setupZoomButtonListener();

      const initialMainImage = mainPreviewContainer?.querySelector('.main-preview-image');
      initialMainImage?.addEventListener('click', handleMainImageClick);

      closeButton?.addEventListener('click', closeLightbox);

      const prevButton = lightbox?.querySelector('.prev-lightbox');
      const nextButton = lightbox?.querySelector('.next-lightbox');

      prevButton?.addEventListener('click', function(e) {
        e.preventDefault();
        e.stopPropagation();

        const allThumbnails = Array.from(lightbox.querySelectorAll('.lightbox-thumbnail'));
        if (allThumbnails.length <= 1) return;

        const currentActive = lightbox.querySelector('.lightbox-thumbnail.active');
        if (!currentActive) return;

        const currentIndex = allThumbnails.indexOf(currentActive);
        if (currentIndex === -1) return;

        const prevIndex = (currentIndex - 1 + allThumbnails.length) % allThumbnails.length;

        const prevThumbnail = allThumbnails[prevIndex];
        if (prevThumbnail) {
          let loadingIndicator = lightbox.querySelector('.lightbox-loading');
          if (loadingIndicator) {
            lightboxImage.style.opacity = '0.3';
            loadingIndicator.style.display = 'block';
          }

          prevThumbnail.scrollIntoView({ behavior: 'smooth', block: 'nearest', inline: 'center' });

          const fullsizeUrl = prevThumbnail.getAttribute('data-fullsize');
          if (fullsizeUrl) {
            lightboxImage.src = fullsizeUrl;
            allThumbnails.forEach(thumb => thumb.classList.remove('active'));
            prevThumbnail.classList.add('active');

            if (loadingIndicator) {
              lightboxImage.onload = function() {
                lightboxImage.style.opacity = '1';
                loadingIndicator.style.display = 'none';
              };
            }
          }
        }
      });

      nextButton?.addEventListener('click', function(e) {
        e.preventDefault();
        e.stopPropagation();

        const allThumbnails = Array.from(lightbox.querySelectorAll('.lightbox-thumbnail'));
        if (allThumbnails.length <= 1) return;

        const currentActive = lightbox.querySelector('.lightbox-thumbnail.active');
        if (!currentActive) return;

        const currentIndex = allThumbnails.indexOf(currentActive);
        if (currentIndex === -1) return;

        const nextIndex = (currentIndex + 1) % allThumbnails.length;

        const nextThumbnail = allThumbnails[nextIndex];
        if (nextThumbnail) {
          let loadingIndicator = lightbox.querySelector('.lightbox-loading');
          if (loadingIndicator) {
            lightboxImage.style.opacity = '0.3';
            loadingIndicator.style.display = 'block';
          }

          nextThumbnail.scrollIntoView({ behavior: 'smooth', block: 'nearest', inline: 'center' });

          const fullsizeUrl = nextThumbnail.getAttribute('data-fullsize');
          if (fullsizeUrl) {
            lightboxImage.src = fullsizeUrl;
            allThumbnails.forEach(thumb => thumb.classList.remove('active'));
            nextThumbnail.classList.add('active');

            if (loadingIndicator) {
              lightboxImage.onload = function() {
                lightboxImage.style.opacity = '1';
                loadingIndicator.style.display = 'none';
              };
            }
          }
        }
      });

      lightbox?.addEventListener('click', function(e) {
        if (e.target === lightbox) closeLightbox();
      });

      document.removeEventListener('keydown', keydownHandler);

      function keydownHandler(e) {
        if (lightbox?.classList.contains('active')) {
          if (e.key === 'Escape') {
            closeLightbox();
          } else if (e.key === 'ArrowLeft') {
            e.preventDefault();
            e.stopPropagation();

            const allThumbnails = Array.from(lightbox.querySelectorAll('.lightbox-thumbnail'));
            if (allThumbnails.length <= 1) return;

            const currentActive = lightbox.querySelector('.lightbox-thumbnail.active');
            if (!currentActive) return;

            const currentIndex = allThumbnails.indexOf(currentActive);
            if (currentIndex === -1) return;

            const prevIndex = (currentIndex - 1 + allThumbnails.length) % allThumbnails.length;
            const prevThumbnail = allThumbnails[prevIndex];

            if (prevThumbnail) {
              prevThumbnail.scrollIntoView({ behavior: 'smooth', block: 'nearest', inline: 'center' });
              const fullsizeUrl = prevThumbnail.getAttribute('data-fullsize');
              if (fullsizeUrl) {
                lightboxImage.src = fullsizeUrl;
                allThumbnails.forEach(thumb => thumb.classList.remove('active'));
                prevThumbnail.classList.add('active');
              }
            }
          } else if (e.key === 'ArrowRight') {
            e.preventDefault();
            e.stopPropagation();

            const allThumbnails = Array.from(lightbox.querySelectorAll('.lightbox-thumbnail'));
            if (allThumbnails.length <= 1) return;

            const currentActive = lightbox.querySelector('.lightbox-thumbnail.active');
            if (!currentActive) return;

            const currentIndex = allThumbnails.indexOf(currentActive);
            if (currentIndex === -1) return;

            const nextIndex = (currentIndex + 1) % allThumbnails.length;
            const nextThumbnail = allThumbnails[nextIndex];

            if (nextThumbnail) {
              nextThumbnail.scrollIntoView({ behavior: 'smooth', block: 'nearest', inline: 'center' });
              const fullsizeUrl = nextThumbnail.getAttribute('data-fullsize');
              if (fullsizeUrl) {
                lightboxImage.src = fullsizeUrl;
                allThumbnails.forEach(thumb => thumb.classList.remove('active'));
                nextThumbnail.classList.add('active');
              }
            }
          }
        }
      }

      document.addEventListener('keydown', keydownHandler);

      setTimeout(function() {
        if (validSamples.length > 0) {
          filterMainGrid('all');
        }
      }, 100);
    }

    function extractFieldValue(fields, key) {
      const field = fields.find(f => f.key === key);
      if (field) {
        return field.value;
      } else {
        return '';
      }
    }

    function extractFieldReferences(fields, key) {
      const field = fields.find(f => f.key === key);
      if (!field) {
        return [];
      }

      if (!field.references || !field.references.edges) {
        return [];
      }

      const references = field.references.edges.map(edge => edge.node);

      return references;
    }

    async function loadTextures() {
      if (!samplesGrid || !categoryDropdown) {
          return;
      }

      try {
        const liquidCategoriesData = [
          { id: "all", name: "All Textures", description: "View our complete texture collection" },
          {% for category in shop.metaobjects.texture_category.values %}
          {
            id: "{{ category.id }}",
            name: "{{ category.name | escape }}",
            description: "{{ category.description | escape }}",
            link: "{{ category.link | escape }}"
          }{% unless forloop.last %},{% endunless %}
          {% endfor %}
        ];

        const liquidSamplesData = [
          {% for sample in shop.metaobjects.texture_sample.values %}
          {
            id: "{{ sample.id }}",
            name: "{{ sample.name | escape }}",
            code: "{{ sample.code | escape }}",
            link: "{{ sample.link | escape }}",
            category: {{ sample.category | json }}
          }{% unless forloop.last %},{% endunless %}
          {% endfor %}
        ];

      } catch (error) {
      }

      const categoryEdges = await fetchTextureCategories();
      categoriesData = [
        { id: "all", name: "All Textures" }
      ];

      categoryEdges.forEach((edge, index) => {
        const node = edge.node;

        if (!node.fields) {
          return;
        }

        const fields = node.fields;

        const category = {
          id: node.id,
          name: extractFieldValue(fields, 'name'),
          description: extractFieldValue(fields, 'description'),
          link: extractFieldValue(fields, 'link')
        };

        categoriesData.push(category);
      });

      const sampleEdges = await fetchTextureSamples();
            samplesData = [];

      let totalImagesToLoad = 0;
      let loadedImages = 0;

      for (let i = 0; i < sampleEdges.length; i++) {
        const edge = sampleEdges[i];
        const node = edge.node;

        if (!node.fields) {
          continue;
        }

        const fields = node.fields;

        const imageRefs = extractFieldReferences(fields, 'image');

        const categoryRef = extractFieldReferences(fields, 'category')[0] || null;

        const name = extractFieldValue(fields, 'name');
        const code = extractFieldValue(fields, 'code');

        const thumbnailUrls = imageRefs
          .filter(ref => ref && ref.image && ref.image.url)
          .map(ref => {
            const url = ref.image.url;
            totalImagesToLoad++;
            return url;
          });

        const fullSizeUrls = imageRefs
          .filter(ref => ref && ref.image && (ref.image.originalSrc || ref.image.url))
          .map(ref => ref.image.originalSrc || ref.image.url);

        samplesData.push({
          id: node.id,
          name: name,
          code: code,
          link: extractFieldValue(fields, 'link'),
          category: categoryRef ? categoryRef.id : null,
          thumbnailUrls: thumbnailUrls,
          fullSizeUrls: fullSizeUrls || thumbnailUrls,
          imageUrls: thumbnailUrls
        });
      }

      if (samplesData.length > 0 && samplesData[0].thumbnailUrls && samplesData[0].thumbnailUrls.length > 0) {
        const firstImage = new Image();
        firstImage.src = samplesData[0].thumbnailUrls[0];
      }

      validSamples = samplesData.filter(sample => sample.thumbnailUrls && sample.thumbnailUrls.length > 0);

      validSamples.sort((a, b) => {
        if (a.name < b.name) return -1;
        if (a.name > b.name) return 1;
        return 0;
      });

      validSamples.forEach(sample => {
        const img = new Image();
        img.src = sample.thumbnailUrls[0];
      });

      const samplesByCategory = { 'all': validSamples };
      validSamples.forEach(sample => {
        if (sample.category) {
          if (!samplesByCategory[sample.category]) {
            samplesByCategory[sample.category] = [];
          }
          samplesByCategory[sample.category].push(sample);
        }
      });

      categoryDropdown.innerHTML = '<option value="all">All Textures</option>';
      categoriesData.forEach(category => {
        if (category.id !== 'all') {
          const categorySamples = samplesByCategory[category.id] || [];

          if (categorySamples.length > 0) {
            const option = document.createElement('option');
            option.value = category.id;
            option.textContent = category.name;
            categoryDropdown.appendChild(option);
          }
        }
      });

      samplesGrid.innerHTML = '';
      validSamples.forEach((sample) => {
        const sampleDiv = document.createElement('div');
        sampleDiv.className = 'texture-sample';
        sampleDiv.setAttribute('data-category', sample.category || '');
        sampleDiv.setAttribute('data-sample-id', sample.id);

        const previewUrl = sample.thumbnailUrls[0];
        const fullSizeUrl = sample.fullSizeUrls && sample.fullSizeUrls[0] ? sample.fullSizeUrls[0] : previewUrl;

        sampleDiv.innerHTML = `
          <button class="sample-button"
                  data-name="${sample.name}"
                  data-code="${sample.code || ''}"
                  data-sample-id="${sample.id}"
                  data-fullsize="${fullSizeUrl}"
                  aria-label="Select ${sample.name}">
            <img src="${previewUrl}"
                loading="lazy"
                class="sample-image"
                alt="${sample.name}">
          </button>
        `;
        samplesGrid.appendChild(sampleDiv);
      });

      if (validSamples.length > 0) {
        filterMainGrid('all');
      } else {
        const placeholderText = imagePlaceholder?.querySelector('text');
        if(placeholderText) placeholderText.textContent = 'No textures available';
        if (sampleImagesList) sampleImagesList.innerHTML = '';
      }

      setupEventListeners();
    }

    loadTextures();
  });
</script>

{% schema %}
  {
    "name": "Texture Explorer",
    "tag": "section",
    "class": "texture-explorer-section",
    "settings": [
      {
        "type": "header",
        "content": "Content Settings"
      },
      {
        "type": "text",
        "id": "title",
        "label": "Section Title",
        "default": "Explore Colors & Textures"
      },
      {
        "type": "richtext",
        "id": "description",
        "label": "Section Description",
        "default": "<p>Discover our wide range of beautiful textures and color options to transform your spaces into works of art.</p>"
      },
      {
        "type": "header",
        "content": "Style Settings"
      },
      {
        "type": "color",
        "id": "title_color",
        "label": "Title Color",
        "default": "#d1b073"
      },
      {
        "type": "color",
        "id": "text_color",
        "label": "Text Color",
        "default": "#222228"
      },
      {
        "type": "color",
        "id": "background_color",
        "label": "Background Color",
        "default": "#f8f8f8"
      },
      {
        "type": "color",
        "id": "accent_color",
        "label": "Accent Color",
        "default": "#d1b073"
      },
      {
        "type": "select",
        "id": "layout",
        "label": "Desktop Layout",
        "options": [
          {
            "value": "vertical",
            "label": "Vertical (Categories on left)"
          },
          {
            "value": "horizontal",
            "label": "Horizontal (Categories at top)"
          }
        ],
        "default": "vertical"
      },
      {
        "type": "select",
        "id": "mobile_thumbnail_click",
        "label": "Mobile Thumbnail Click Behavior",
        "options": [
          {
            "value": "normal",
            "label": "Normal (Update Main Image)"
          },
          {
            "value": "lightbox",
            "label": "Open in Lightbox"
          },
          {
            "value": "scroll",
            "label": "Scroll to Main Image"
          }
        ],
        "default": "normal",
        "info": "Choose what happens when a thumbnail is clicked on mobile devices"
      }
    ],
    "presets": [
      {
        "name": "Texture Explorer"
      }
    ]
  }
{% endschema %}