{% schema %}
    {
      "name": "Technical Specifications",
      "tag": "section",
      "class": "section",
      "settings": [
        {
          "type": "text",
          "id": "title",
          "label": "Section Title",
          "default": "Technical Specifications"
        },
        {
          "type": "color",
          "id": "title_color",
          "label": "Title Color",
          "default": "#d1b073"
        },
        {
          "type": "color",
          "id": "text_color",
          "label": "Text Color",
          "default": "#222228"
        },
        {
          "type": "color",
          "id": "background_color",
          "label": "Background Color",
          "default": "#f8f8f8"
        },
        {
          "type": "color",
          "id": "accent_color",
          "label": "Accent Color",
          "default": "#d1b073"
        },
        {
          "type": "select",
          "id": "layout",
          "label": "Layout Style",
          "options": [
            {
              "value": "tabs",
              "label": "Tabs"
            },
            {
              "value": "accordion",
              "label": "Accordion"
            }
          ],
          "default": "tabs"
        }
      ],
      "blocks": [
        {
          "type": "spec_group",
          "name": "Specification Group",
          "settings": [
            {
              "type": "text",
              "id": "title",
              "label": "Group Title",
              "default": "Technical Data"
            },
            {
              "type": "color",
              "id": "title_color",
              "label": "Title Color",
              "default": "#222228"
            },
            {
              "type": "image_picker",
              "id": "icon",
              "label": "Group Icon (Optional)"
            }
          ]
        },
        {
          "type": "spec_item",
          "name": "Specification Item",
          "settings": [
            {
              "type": "select",
              "id": "group",
              "label": "Parent Group",
              "default": "1",
              "options": [
                {
                  "value": "1",
                  "label": "Group 1"
                },
                {
                  "value": "2",
                  "label": "Group 2"
                },
                {
                  "value": "3",
                  "label": "Group 3"
                },
                {
                  "value": "4",
                  "label": "Group 4"
                }
              ]
            },
            {
              "type": "text",
              "id": "name",
              "label": "Specification Name",
              "default": "Coverage"
            },
            {
              "type": "html",
              "id": "value",
              "label": "Specification Value",
              "default": "8-10 m²/L per coat, depending on surface porosity"
            }
          ]
        },
        {
          "type": "download",
          "name": "Download File",
          "settings": [
            {
              "type": "select",
              "id": "group",
              "label": "Parent Group",
              "default": "1",
              "options": [
                {
                  "value": "1",
                  "label": "Group 1"
                },
                {
                  "value": "2",
                  "label": "Group 2"
                },
                {
                  "value": "3",
                  "label": "Group 3"
                },
                {
                  "value": "4",
                  "label": "Group 4"
                }
              ]
            },
            {
              "type": "text",
              "id": "title",
              "label": "Download Title",
              "default": "Technical Data Sheet"
            },
            {
              "type": "url",
              "id": "file_url",
              "label": "File URL"
            },
            {
              "type": "text",
              "id": "file_format",
              "label": "File Format",
              "default": "PDF"
            },
            {
              "type": "text",
              "id": "file_size",
              "label": "File Size",
              "default": "2.4 MB"
            }
          ]
        }
      ],
      "presets": [
        {
          "name": "Technical Specifications",
          "blocks": [
            {
              "type": "spec_group"
            },
            {
              "type": "spec_item"
            },
            {
              "type": "spec_item"
            },
            {
              "type": "download"
            }
          ]
        }
      ]
    }
    {% endschema %}
    
    <div class="technical-specs-section" 
         style="background-color: {{ section.settings.background_color }}; 
                color: {{ section.settings.text_color }};"
         data-layout="{{ section.settings.layout }}">
      <div class="page-width">
        <h2 class="section-title" style="color: {{ section.settings.title_color }};">
          {{ section.settings.title }}
        </h2>
    
        {% assign spec_groups = section.blocks | where: "type", "spec_group" %}
        {% assign spec_items = section.blocks | where: "type", "spec_item" %}
        {% assign downloads = section.blocks | where: "type", "download" %}
        
        {% if section.settings.layout == 'tabs' %}
          <div class="spec-tabs">
            <ul class="tabs-nav">
              {% for group in spec_groups %}
                <li class="tab-item {% if forloop.first %}active{% endif %}" 
                    data-group="{{ forloop.index }}" {{ group.shopify_attributes }}>
                  <button class="tab-button" style="color: {{ group.settings.title_color }};">
                    {% if group.settings.icon != blank %}
                      <span class="tab-icon">
                        {{ group.settings.icon | image_url: width: 40 | image_tag:
                          loading: 'lazy',
                          width: 20,
                          height: 20
                        }}
                      </span>
                    {% endif %}
                    {{ group.settings.title }}
                  </button>
                </li>
              {% endfor %}
            </ul>
            
            <div class="tabs-content">
              {% for group in spec_groups %}
                <div class="tab-panel {% if forloop.first %}active{% endif %}" data-group="{{ forloop.index }}">
                  <table class="specs-table">
                    <tbody>
                      {% for item in spec_items %}
                        {% if item.settings.group == forloop.parentloop.index %}
                          <tr {{ item.shopify_attributes }}>
                            <th class="spec-name">{{ item.settings.name }}</th>
                            <td class="spec-value">{{ item.settings.value }}</td>
                          </tr>
                        {% endif %}
                      {% endfor %}
                    </tbody>
                  </table>
                  
                  {% for download in downloads %}
                    {% if download.settings.group == forloop.parentloop.index %}
                      <div class="download-item" {{ download.shopify_attributes }}>
                        <a href="{{ download.settings.file_url }}" class="download-link" target="_blank" rel="noopener">
                          <div class="download-icon" style="color: {{ section.settings.accent_color }};">
                            <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                              <path d="M21 15V19C21 19.5304 20.7893 20.0391 20.4142 20.4142C20.0391 20.7893 19.5304 21 19 21H5C4.46957 21 3.96086 20.7893 3.58579 20.4142C3.21071 20.0391 3 19.5304 3 19V15" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                              <path d="M7 10L12 15L17 10" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                              <path d="M12 15V3" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                            </svg>
                          </div>
                          
                          <div class="download-info">
                            <div class="download-title">{{ download.settings.title }}</div>
                            <div class="download-meta">
                              <span class="download-format">{{ download.settings.file_format }}</span>
                              <span class="download-size">{{ download.settings.file_size }}</span>
                            </div>
                          </div>
                        </a>
                      </div>
                    {% endif %}
                  {% endfor %}
                </div>
              {% endfor %}
            </div>
          </div>
        {% else %}
          <div class="spec-accordion">
            {% for group in spec_groups %}
              <div class="accordion-item" {{ group.shopify_attributes }}>
                <button class="accordion-header" aria-expanded="false">
                  <div class="accordion-title" style="color: {{ group.settings.title_color }};">
                    {% if group.settings.icon != blank %}
                      <span class="accordion-icon">
                        {{ group.settings.icon | image_url: width: 40 | image_tag:
                          loading: 'lazy',
                          width: 20,
                          height: 20
                        }}
                      </span>
                    {% endif %}
                    {{ group.settings.title }}
                  </div>
                  <span class="accordion-arrow" style="border-color: {{ section.settings.accent_color }};">
                    <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
                      <path d="M5 7.5L10 12.5L15 7.5" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                    </svg>
                  </span>
                </button>
                
                <div class="accordion-content">
                  <table class="specs-table">
                    <tbody>
                      {% for item in spec_items %}
                        {% if item.settings.group == forloop.index %}
                          <tr {{ item.shopify_attributes }}>
                            <th class="spec-name">{{ item.settings.name }}</th>
                            <td class="spec-value">{{ item.settings.value }}</td>
                          </tr>
                        {% endif %}
                      {% endfor %}
                    </tbody>
                  </table>
                  
                  {% for download in downloads %}
                    {% if download.settings.group == forloop.index %}
                      <div class="download-item" {{ download.shopify_attributes }}>
                        <a href="{{ download.settings.file_url }}" class="download-link" target="_blank" rel="noopener">
                          <div class="download-icon" style="color: {{ section.settings.accent_color }};">
                            <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                              <path d="M21 15V19C21 19.5304 20.7893 20.0391 20.4142 20.4142C20.0391 20.7893 19.5304 21 19 21H5C4.46957 21 3.96086 20.7893 3.58579 20.4142C3.21071 20.0391 3 19.5304 3 19V15" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                              <path d="M7 10L12 15L17 10" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                              <path d="M12 15V3" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                            </svg>
                          </div>
                          
                          <div class="download-info">
                            <div class="download-title">{{ download.settings.title }}</div>
                            <div class="download-meta">
                              <span class="download-format">{{ download.settings.file_format }}</span>
                              <span class="download-size">{{ download.settings.file_size }}</span>
                            </div>
                          </div>
                        </a>
                      </div>
                    {% endif %}
                  {% endfor %}
                </div>
              </div>
            {% endfor %}
          </div>
        {% endif %}
      </div>
    </div>
    
    <style>
      .technical-specs-section {
        padding: 80px 0;
      }
      
      .section-title {
        margin-bottom: 50px;
        font-size: calc(var(--font-heading-scale) * 2.3rem);
        letter-spacing: 0.05em;
        text-transform: uppercase;
        text-align: center;
        line-height: 1.2;
        position: relative;
        display: inline-block;
        padding-bottom: 20px;
        left: 50%;
        transform: translateX(-50%);
      }
      
      .section-title:after {
        content: '';
        position: absolute;
        bottom: 0;
        left: 50%;
        transform: translateX(-50%);
        width: 80px;
        height: 3px;
        background-color: currentColor;
        border-radius: 2px;
      }
      
      /* Tabs Layout */
      .spec-tabs {
        max-width: 900px;
        margin: 0 auto;
      }
      
      .tabs-nav {
        display: flex;
        list-style: none;
        padding: 0;
        margin: 0 0 30px;
        overflow-x: auto;
        -webkit-overflow-scrolling: touch;
        scrollbar-width: none;
        border-bottom: 1px solid rgba(0, 0, 0, 0.1);
      }
      
      .tabs-nav::-webkit-scrollbar {
        display: none;
      }
      
      .tab-item {
        flex: 0 0 auto;
      }
      
      .tab-button {
        background: none;
        border: none;
        padding: 15px 25px;
        font-size: 1.1rem;
        font-weight: 600;
        cursor: pointer;
        position: relative;
        display: flex;
        align-items: center;
        transition: color 0.3s ease;
      }
      
      .tab-button:after {
        content: '';
        position: absolute;
        bottom: -1px;
        left: 0;
        right: 0;
        height: 3px;
        background-color: {{ section.settings.accent_color }};
        transform: scaleX(0);
        transition: transform 0.3s ease;
      }
      
      .tab-item.active .tab-button:after {
        transform: scaleX(1);
      }
      
      .tab-icon {
        margin-right: 10px;
        display: flex;
        align-items: center;
      }
      
      .tabs-content {
        background-color: #ffffff;
        border-radius: 0 8px 8px 8px;
        box-shadow: 0 5px 20px rgba(0, 0, 0, 0.05);
        overflow: hidden;
      }
      
      .tab-panel {
        display: none;
        padding: 30px;
      }
      
      .tab-panel.active {
        display: block;
      }
      
      /* Accordion Layout */
      .spec-accordion {
        max-width: 900px;
        margin: 0 auto;
      }
      
      .accordion-item {
        background-color: #ffffff;
        border-radius: 8px;
        overflow: hidden;
        margin-bottom: 15px;
        box-shadow: 0 5px 20px rgba(0, 0, 0, 0.05);
      }
      
      .accordion-header {
        width: 100%;
        padding: 20px 25px;
        display: flex;
        justify-content: space-between;
        align-items: center;
        background: none;
        border: none;
        cursor: pointer;
      }
      
      .accordion-title {
        font-size: 1.2rem;
        font-weight: 600;
        display: flex;
        align-items: center;
      }
      
      .accordion-icon {
        margin-right: 15px;
        display: flex;
        align-items: center;
      }
      
      .accordion-arrow {
        transition: transform 0.3s ease;
      }
      
      .accordion-header[aria-expanded="true"] .accordion-arrow {
        transform: rotate(180deg);
      }
      
      .accordion-content {
        padding: 0 25px 25px;
        display: none;
      }
      
      .accordion-header[aria-expanded="true"] + .accordion-content {
        display: block;
      }
      
      /* Specs Table */
      .specs-table {
        width: 100%;
        border-collapse: collapse;
        margin-bottom: 20px;
      }
      
      .specs-table tr {
        border-bottom: 1px solid rgba(0, 0, 0, 0.1);
      }
      
      .specs-table tr:last-child {
        border-bottom: none;
      }
      
      .spec-name {
        width: 40%;
        padding: 12px 10px 12px 0;
        font-weight: 600;
        text-align: left;
        vertical-align: top;
        font-size: 0.95rem;
      }
      
      .spec-value {
        padding: 12px 0;
        text-align: left;
        font-size: 0.95rem;
        line-height: 1.5;
      }
      
      /* Download Item */
      .download-item {
        margin-top: 25px;
        padding-top: 15px;
        border-top: 1px solid rgba(0, 0, 0, 0.1);
      }
      
      .download-link {
        display: flex;
        align-items: center;
        text-decoration: none;
        color: inherit;
        padding: 10px;
        border-radius: 6px;
        transition: background-color 0.3s ease;
      }
      
      .download-link:hover {
        background-color: rgba(0, 0, 0, 0.03);
      }
      
      .download-icon {
        margin-right: 15px;
      }
      
      .download-title {
        font-weight: 600;
        font-size: 1rem;
        margin-bottom: 5px;
      }
      
      .download-meta {
        font-size: 0.85rem;
        opacity: 0.7;
        display: flex;
      }
      
      .download-format {
        margin-right: 10px;
      }
      
      .download-format:after {
        content: '•';
        margin-left: 10px;
      }
      
      /* Responsive */
      @media screen and (max-width: 989px) {
        .section-title {
          font-size: calc(var(--font-heading-scale) * 2rem);
        }
        
        .tab-button {
          padding: 12px 20px;
          font-size: 1rem;
        }
      }
      
      @media screen and (max-width: 749px) {
        .technical-specs-section {
          padding: 60px 0;
        }
        
        .section-title {
          font-size: calc(var(--font-heading-scale) * 1.8rem);
          margin-bottom: 40px;
        }
        
        .tabs-nav {
          flex-wrap: nowrap;
          overflow-x: auto;
        }
        
        .tab-button {
          white-space: nowrap;
          padding: 10px 15px;
          font-size: 0.9rem;
        }
        
        .tab-panel,
        .accordion-content {
          padding: 20px;
        }
        
        .accordion-title {
          font-size: 1.1rem;
        }
        
        .spec-name,
        .spec-value {
          display: block;
          width: 100%;
          padding: 8px 0;
        }
        
        .spec-name {
          border-bottom: none;
        }
        
        .spec-value {
          padding-bottom: 15px;
        }
        
        .specs-table tr {
          display: block;
          margin-bottom: 15px;
          padding-bottom: 15px;
        }
      }
    </style>
    
    <script>
      document.addEventListener('DOMContentLoaded', function() {
        // Tabs functionality
        const tabsNav = document.querySelector('.tabs-nav');
        if (tabsNav) {
          const tabItems = tabsNav.querySelectorAll('.tab-item');
          const tabPanels = document.querySelectorAll('.tab-panel');
          
          tabItems.forEach(tab => {
            tab.addEventListener('click', function() {
              const groupId = this.getAttribute('data-group');
              
              // Update active tab
              tabItems.forEach(item => item.classList.remove('active'));
              this.classList.add('active');
              
              // Show corresponding panel
              tabPanels.forEach(panel => {
                panel.classList.remove('active');
                if (panel.getAttribute('data-group') === groupId) {
                  panel.classList.add('active');
                }
              });
            });
          });
        }
        
        // Accordion functionality
        const accordionHeaders = document.querySelectorAll('.accordion-header');
        if (accordionHeaders.length > 0) {
          accordionHeaders.forEach(header => {
            header.addEventListener('click', function() {
              const expanded = this.getAttribute('aria-expanded') === 'true';
              this.setAttribute('aria-expanded', !expanded);
            });
          });
          
          // Open first accordion by default
          if (accordionHeaders[0]) {
            accordionHeaders[0].setAttribute('aria-expanded', 'true');
          }
        }
      });
    </script>