{% schema %}
    {
      "name": "Showroom Gallery",
      "tag": "section",
      "class": "section",
      "settings": [
        {
          "type": "text",
          "id": "title",
          "label": "Section Title",
          "default": "Our Showroom Gallery"
        },
        {
          "type": "color",
          "id": "title_color",
          "label": "Title Color",
          "default": "#d1b073"
        },
        {
          "type": "richtext",
          "id": "description",
          "label": "Section Description",
          "default": "<p>Discover the beauty and elegance of our premium decorative coatings in these stunning real-world applications.</p>"
        },
        {
          "type": "color",
          "id": "text_color",
          "label": "Text Color",
          "default": "#222228"
        },
        {
          "type": "color",
          "id": "background_color",
          "label": "Background Color",
          "default": "#ffffff"
        },
        {
          "type": "color",
          "id": "accent_color",
          "label": "Accent Color",
          "default": "#d1b073"
        },
        {
          "type": "select",
          "id": "layout",
          "label": "Gallery Layout",
          "options": [
            {
              "value": "grid",
              "label": "Grid"
            },
            {
              "value": "masonry",
              "label": "Masonry"
            }
          ],
          "default": "masonry"
        },
        {
          "type": "select",
          "id": "filter_type",
          "label": "Filter Type",
          "options": [
            {
              "value": "none",
              "label": "No Filters"
            },
            {
              "value": "category",
              "label": "By Category"
            }
          ],
          "default": "category"
        }
      ],
      "blocks": [
        {
          "type": "gallery_item",
          "name": "Gallery Item",
          "settings": [
            {
              "type": "image_picker",
              "id": "image",
              "label": "Gallery Image"
            },
            {
              "type": "text",
              "id": "title",
              "label": "Image Title",
              "default": "Elegant Living Room"
            },
            {
              "type": "text",
              "id": "category",
              "label": "Category",
              "default": "Residential"
            },
            {
              "type": "select",
              "id": "size",
              "label": "Image Size",
              "info": "For Masonry layout only",
              "options": [
                {
                  "value": "small",
                  "label": "Small"
                },
                {
                  "value": "medium",
                  "label": "Medium"
                },
                {
                  "value": "large",
                  "label": "Large"
                }
              ],
              "default": "medium"
            },
            {
              "type": "text",
              "id": "product",
              "label": "Product Used",
              "default": "TRAVERTINO STYLE"
            },
            {
              "type": "url",
              "id": "link",
              "label": "Item Link (Optional)"
            }
          ]
        }
      ],
      "presets": [
        {
          "name": "Showroom Gallery",
          "blocks": [
            {
              "type": "gallery_item"
            },
            {
              "type": "gallery_item"
            },
            {
              "type": "gallery_item"
            },
            {
              "type": "gallery_item"
            },
            {
              "type": "gallery_item"
            },
            {
              "type": "gallery_item"
            }
          ]
        }
      ]
    }
    {% endschema %}
    
    <div class="showroom-gallery-section" 
         style="background-color: {{ section.settings.background_color }}; 
                color: {{ section.settings.text_color }};"
         data-layout="{{ section.settings.layout }}">
      <div class="page-width">
        <div class="section-header text-center">
          <h2 class="section-title" style="color: {{ section.settings.title_color }};">
            {{ section.settings.title }}
          </h2>
          
          <div class="section-description rte">
            {{ section.settings.description }}
          </div>
        </div>
    
        {% if section.settings.filter_type == 'category' %}
          {% assign gallery_items = section.blocks | where: "type", "gallery_item" %}
          {% assign categories = "" | split: "" %}
          
          {% for item in gallery_items %}
            {% assign category = item.settings.category %}
            {% if category != blank %}
              {% unless categories contains category %}
                {% assign categories = categories | concat: category | split: "," %}
              {% endunless %}
            {% endif %}
          {% endfor %}
          
          {% if categories.size > 0 %}
            <div class="gallery-filters">
              <button class="filter-button active" data-filter="all">All</button>
              {% for category in categories %}
                <button class="filter-button" data-filter="{{ category | handle }}">{{ category }}</button>
              {% endfor %}
            </div>
          {% endif %}
        {% endif %}
        
        <div class="gallery-container">
          {% for block in section.blocks %}
            {% if block.type == 'gallery_item' %}
              <div class="gallery-item {% if section.settings.layout == 'masonry' %}{{ block.settings.size }}{% endif %}" 
                   data-category="{{ block.settings.category | handle }}" {{ block.shopify_attributes }}>
                <div class="gallery-item-inner">
                  {% if block.settings.image != blank %}
                    <div class="gallery-image">
                      {{ block.settings.image | image_url: width: 800 | image_tag:
                        loading: 'lazy',
                        class: 'gallery-img',
                        widths: '275, 550, 710, 1420',
                        sizes: '(min-width: 990px) 33vw, (min-width: 750px) 50vw, 100vw'
                      }}
                    </div>
                  {% else %}
                    <div class="gallery-image placeholder">
                      {{ 'image' | placeholder_svg_tag: 'placeholder-svg gallery-img' }}
                    </div>
                  {% endif %}
                  
                  <div class="gallery-overlay">
                    <div class="gallery-content">
                      <h3 class="gallery-title">{{ block.settings.title }}</h3>
                      {% if block.settings.product != blank %}
                        <div class="gallery-product">{{ block.settings.product }}</div>
                      {% endif %}
                    </div>
                    
                    {% if block.settings.link != blank %}
                      <a href="{{ block.settings.link }}" class="gallery-link" style="background-color: {{ section.settings.accent_color }};">
                        <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                          <path d="M15 7H18C18.5304 7 19.0391 7.21071 19.4142 7.58579C19.7893 7.96086 20 8.46957 20 9V18C20 18.5304 19.7893 19.0391 19.4142 19.4142C19.0391 19.7893 18.5304 20 18 20H9C8.46957 20 7.96086 19.7893 7.58579 19.4142C7.21071 19.0391 7 18.5304 7 18V15" stroke="#ffffff" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                          <path d="M13 11L4 20" stroke="#ffffff" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                          <path d="M4 13V20H11" stroke="#ffffff" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                        </svg>
                      </a>
                    {% endif %}
                    
                    <div class="gallery-category-tag" style="background-color: {{ section.settings.accent_color }};">
                      {{ block.settings.category }}
                    </div>
                  </div>
                </div>
              </div>
            {% endif %}
          {% endfor %}
        </div>
      </div>
    </div>
    
    <style>
      .showroom-gallery-section {
        padding: 80px 0;
      }
      
      .section-description {
        max-width: 800px;
        margin: 0 auto;
        font-size: 1.1rem;
        line-height: 1.6;
        margin-bottom: 60px;
      }
      
      /* Gallery Filters */
      .gallery-filters {
        display: flex;
        justify-content: center;
        flex-wrap: wrap;
        gap: 15px;
        margin-bottom: 40px;
      }
      
      .filter-button {
        background: none;
        border: 2px solid transparent;
        padding: 8px 20px;
        font-size: 0.95rem;
        font-weight: 600;
        color: inherit;
        border-radius: 30px;
        cursor: pointer;
        transition: all 0.3s ease;
      }
      
      .filter-button.active {
        border-color: {{ section.settings.accent_color }};
        color: {{ section.settings.accent_color }};
      }
      
      .filter-button:hover:not(.active) {
        background-color: rgba(0, 0, 0, 0.03);
      }
      
      /* Grid Layout */
      [data-layout="grid"] .gallery-container {
        display: grid;
        grid-template-columns: repeat(3, 1fr);
        gap: 20px;
      }
      
      /* Masonry Layout */
      [data-layout="masonry"] .gallery-container {
        column-count: 3;
        column-gap: 20px;
      }
      
      [data-layout="masonry"] .gallery-item {
        break-inside: avoid;
        margin-bottom: 20px;
      }
      
      [data-layout="masonry"] .gallery-item.small .gallery-image {
        padding-bottom: 90%;
      }
      
      [data-layout="masonry"] .gallery-item.medium .gallery-image {
        padding-bottom: 120%;
      }
      
      [data-layout="masonry"] .gallery-item.large .gallery-image {
        padding-bottom: 150%;
      }
      
      /* Gallery Item */
      .gallery-item-inner {
        position: relative;
        border-radius: 8px;
        overflow: hidden;
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
      }
      
      .gallery-image {
        position: relative;
        width: 100%;
        height: 0;
        padding-bottom: 100%;
      }
      
      .gallery-img,
      .gallery-image .placeholder-svg {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        object-fit: cover;
        transition: transform 0.6s ease;
      }
      
      .gallery-item-inner:hover .gallery-img {
        transform: scale(1.05);
      }
      
      .gallery-overlay {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: linear-gradient(to bottom, rgba(0, 0, 0, 0) 60%, rgba(0, 0, 0, 0.7) 100%);
        opacity: 0;
        transition: opacity 0.3s ease;
        display: flex;
        flex-direction: column;
        justify-content: flex-end;
        padding: 20px;
      }
      
      .gallery-item-inner:hover .gallery-overlay {
        opacity: 1;
      }
      
      .gallery-content {
        transform: translateY(20px);
        transition: transform 0.4s ease;
        color: #ffffff;
      }
      
      .gallery-item-inner:hover .gallery-content {
        transform: translateY(0);
      }
      
      .gallery-title {
        margin: 0 0 5px;
        font-size: 1.2rem;
        font-weight: 600;
      }
      
      .gallery-product {
        font-size: 0.9rem;
        opacity: 0.8;
        margin-bottom: 15px;
      }
      
      .gallery-link {
        position: absolute;
        top: 15px;
        right: 15px;
        width: 40px;
        height: 40px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        opacity: 0;
        transform: translateY(-10px);
        transition: opacity 0.4s ease, transform 0.4s ease;
      }
      
      .gallery-item-inner:hover .gallery-link {
        opacity: 1;
        transform: translateY(0);
      }
      
      .gallery-category-tag {
        position: absolute;
        top: 15px;
        left: 15px;
        padding: 5px 10px;
        font-size: 0.8rem;
        font-weight: 600;
        color: #ffffff;
        border-radius: 4px;
        opacity: 0;
        transform: translateY(-10px);
        transition: opacity 0.4s ease, transform 0.4s ease;
      }
      
      .gallery-item-inner:hover .gallery-category-tag {
        opacity: 1;
        transform: translateY(0);
      }
      
      /* Responsive */
      @media screen and (max-width: 989px) {
        .section-title {
          font-size: calc(var(--font-heading-scale) * 2rem);
        }
        
        [data-layout="grid"] .gallery-container {
          grid-template-columns: repeat(2, 1fr);
        }
        
        [data-layout="masonry"] .gallery-container {
          column-count: 2;
        }
      }
      
      @media screen and (max-width: 749px) {
        .showroom-gallery-section {
          padding: 60px 0;
        }
        
        .section-title {
          font-size: calc(var(--font-heading-scale) * 1.8rem);
          margin-bottom: 40px;
        }
        
        .section-description {
          font-size: 1rem;
        }
        
        [data-layout="grid"] .gallery-container {
          grid-template-columns: 1fr;
          gap: 15px;
        }
        
        [data-layout="masonry"] .gallery-container {
          column-count: 1;
          column-gap: 15px;
        }
        
        .gallery-title {
          font-size: 1.1rem;
        }
      }
    </style>
    
    <script>
      document.addEventListener('DOMContentLoaded', function() {
        // Gallery Filtering
        const filterButtons = document.querySelectorAll('.filter-button');
        const galleryItems = document.querySelectorAll('.gallery-item');
        
        if (filterButtons.length > 0) {
          filterButtons.forEach(button => {
            button.addEventListener('click', function() {
              const filter = this.getAttribute('data-filter');
              
              // Update active button
              filterButtons.forEach(btn => btn.classList.remove('active'));
              this.classList.add('active');
              
              // Filter items
              galleryItems.forEach(item => {
                if (filter === 'all' || item.getAttribute('data-category') === filter) {
                  item.style.display = '';
                } else {
                  item.style.display = 'none';
                }
              });
            });
          });
        }
      });
    </script>