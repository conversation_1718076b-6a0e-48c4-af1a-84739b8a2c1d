<style>
  .application-process-section {
    padding: 80px 0;
  }
  
  /* Horizontal Layout */
  [data-layout="horizontal"] .application-steps-container {
    display: flex;
    flex-wrap: nowrap;
    justify-content: space-between;
    position: relative;
    margin-top: 50px;
  }
  
  [data-layout="horizontal"] .application-step-item {
    flex: 1;
    position: relative;
    padding: 0 15px;
    text-align: center;
  }
  
  [data-layout="horizontal"] .step-connector {
    position: absolute;
    top: 30px;
    left: 0;
    width: 100%;
    height: 2px;
    z-index: 0;
  }
  
  [data-layout="horizontal"] .application-step-item:first-child .step-connector {
    left: 50%;
    width: 50%;
  }
  
  [data-layout="horizontal"] .application-step-item:last-child .step-connector {
    width: 50%;
  }
  
  [data-layout="horizontal"] .step-number-container {
    position: relative;
    z-index: 1;
    margin-bottom: 30px;
  }
  
  [data-layout="horizontal"] .step-number {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 700;
    margin: 0 auto;
  }
  
  [data-layout="horizontal"] .step-image {
    margin-bottom: 20px;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
  }
  
  [data-layout="horizontal"] .step-img {
    width: 100%;
    height: auto;
    display: block;
  }
  
  [data-layout="horizontal"] .step-title {
    margin-bottom: 15px;
    font-weight: 600;
  }
  
  [data-layout="horizontal"] .step-description {
    line-height: 1.5;
  }
  
  /* Vertical Layout */
  [data-layout="vertical"] .application-steps-container {
    position: relative;
    padding-left: 40px;
    margin-left: 30px;
  }
  
  [data-layout="vertical"] .step-connector {
    position: absolute;
    left: 0;
    top: 0;
    bottom: 0;
    width: 2px;
    z-index: 0;
  }
  
  [data-layout="vertical"] .application-step-item {
    position: relative;
    margin-bottom: 50px;
  }
  
  [data-layout="vertical"] .application-step-item:last-child {
    margin-bottom: 0;
  }
  
  [data-layout="vertical"] .step-number-container {
    position: absolute;
    left: -40px;
    top: 0;
    z-index: 1;
  }
  
  [data-layout="vertical"] .step-number {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 700;
  }
  
  [data-layout="vertical"] .step-content {
    display: flex;
    flex-wrap: wrap;
    align-items: center;
  }
  
  [data-layout="vertical"] .step-image {
    flex: 0 0 200px;
    margin-right: 30px;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
  }
  
  [data-layout="vertical"] .step-img {
    width: 100%;
    height: auto;
    display: block;
  }
  
  [data-layout="vertical"] .step-title {
    margin-bottom: 15px;
    font-weight: 600;
    flex: 0 0 100%;
  }
  
  [data-layout="vertical"] .step-description {
    line-height: 1.6;
    flex: 1;
  }
  
  /* Responsive */
  @media screen and (max-width: 989px) {
    /* Horizontal Layout - Tablet */
    [data-layout="horizontal"] .application-steps-container {
      flex-wrap: wrap;
      justify-content: center;
    }
    
    [data-layout="horizontal"] .application-step-item {
      flex: 0 0 50%;
      margin-bottom: 40px;
    }
    
    [data-layout="horizontal"] .step-connector {
      display: none;
    }
    
    /* Vertical Layout - Tablet */
    [data-layout="vertical"] .step-image {
      flex: 0 0 150px;
    }
  }
  
  @media screen and (max-width: 749px) {
    .application-process-section {
      padding: 60px 0;
    }
    
    .section-title {
      margin-bottom: 40px;
    }
    
    /* Horizontal Layout - Mobile */
    [data-layout="horizontal"] .application-step-item {
      flex: 0 0 100%;
      margin-bottom: 40px;
    }
    
    /* Vertical Layout - Mobile */
    [data-layout="vertical"] .step-content {
      flex-direction: column;
      align-items: flex-start;
    }
    
    [data-layout="vertical"] .step-image {
      flex: 0 0 auto;
      width: 100%;
      margin-right: 0;
      margin-bottom: 20px;
    }
  }
</style>

<div class="application-process-section" 
      style="background-color: {{ section.settings.background_color }}; 
            color: {{ section.settings.text_color }};"
      data-layout="{{ section.settings.layout }}">
  <div class="page-width">
    <h2 class="section-title" style="color: {{ section.settings.title_color }};">
      {{ section.settings.title }}
    </h2>

    <div class="application-steps-container">
      {% for block in section.blocks %}
        {% if block.type == 'application_step' %}
          <div class="application-step-item" {{ block.shopify_attributes }}>
            <div class="step-connector" style="background-color: {{ section.settings.accent_color }};"></div>
            
            <div class="step-number-container">
              <div class="step-number" style="background-color: {{ section.settings.accent_color }}; color: {{ section.settings.background_color }};">
                {{ block.settings.step_number }}
              </div>
            </div>
            
            <div class="step-content">
              {% if block.settings.image != blank %}
                <div class="step-image">
                  {{ block.settings.image | image_url: width: 600 | image_tag:
                    loading: 'lazy',
                    class: 'step-img',
                    widths: '275, 550, 710',
                    sizes: '(min-width: 750px) 25vw, 100vw'
                  }}
                </div>
              {% endif %}
              
              <h3 class="step-title" style="color: {{ block.settings.title_color }};">
                {{ block.settings.title }}
              </h3>
              
              <div class="step-description">
                {{ block.settings.description }}
              </div>
            </div>
          </div>
        {% endif %}
      {% endfor %}
    </div>
  </div>
</div>

{% schema %}
  {
    "name": "Application Process",
    "tag": "section",
    "class": "section",
    "settings": [
      {
        "type": "text",
        "id": "title",
        "label": "Section Title",
        "default": "Application Process"
      },
      {
        "type": "color",
        "id": "title_color",
        "label": "Title Color",
        "default": "#d1b073"
      },
      {
        "type": "color",
        "id": "text_color",
        "label": "Text Color",
        "default": "#222228"
      },
      {
        "type": "color",
        "id": "background_color",
        "label": "Background Color",
        "default": "#ffffff"
      },
      {
        "type": "color",
        "id": "accent_color",
        "label": "Accent Color",
        "default": "#d1b073"
      },
      {
        "type": "select",
        "id": "layout",
        "label": "Layout Style",
        "options": [
          {
            "value": "horizontal",
            "label": "Horizontal Steps"
          },
          {
            "value": "vertical",
            "label": "Vertical Steps"
          }
        ],
        "default": "horizontal"
      }
    ],
    "blocks": [
      {
        "type": "application_step",
        "name": "Application Step",
        "settings": [
          {
            "type": "text",
            "id": "step_number",
            "label": "Step Number",
            "default": "01"
          },
          {
            "type": "text",
            "id": "title",
            "label": "Step Title",
            "default": "Surface Preparation"
          },
          {
            "type": "color",
            "id": "title_color",
            "label": "Title Color",
            "default": "#222228"
          },
          {
            "type": "textarea",
            "id": "description",
            "label": "Step Description",
            "default": "Prepare the surface by cleaning thoroughly and ensuring it is smooth, dry and free of imperfections."
          },
          {
            "type": "image_picker",
            "id": "image",
            "label": "Step Image"
          }
        ]
      }
    ],
    "presets": [
      {
        "name": "Application Process",
        "blocks": [
          {
            "type": "application_step"
          },
          {
            "type": "application_step"
          },
          {
            "type": "application_step"
          },
          {
            "type": "application_step"
          }
        ]
      }
    ]
  }
{% endschema %}