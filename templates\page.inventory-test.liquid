{% comment %}
  Template for testing inventory validation functionality
  Create a page with handle "inventory-test" to use this template
{% endcomment %}

<div class="page-width">
  <div class="main-page-title">
    <h1 class="h0">{{ page.title | escape }}</h1>
  </div>

  <div class="rte">
    {{ page.content }}
  </div>

  <div class="inventory-test-section">
    <h2>Inventory Validation Test</h2>
    
    <div class="test-controls">
      <button id="run-inventory-tests" class="button button--primary">
        Run Inventory Tests
      </button>
      <button id="clear-console" class="button button--secondary">
        Clear Console
      </button>
    </div>

    <div class="test-results" id="test-results">
      <h3>Test Results</h3>
      <p>Click "Run Inventory Tests" to start testing the inventory validation functionality.</p>
      <p>Open your browser's developer console (F12) to see detailed test output.</p>
    </div>

    <div class="test-info">
      <h3>What This Test Checks</h3>
      <ul>
        <li><strong>Default Location Inventory:</strong> Verifies that quantity inputs use default warehouse inventory instead of total inventory across all locations</li>
        <li><strong>Max Attribute Validation:</strong> Ensures that HTML max attributes match the default location inventory</li>
        <li><strong>JavaScript Validation:</strong> Tests that validation functions properly prevent adding excessive quantities</li>
      </ul>
    </div>

    <div class="current-inventory-info">
      <h3>Current Page Inventory Data</h3>
      <div id="inventory-data-display">
        <!-- Will be populated by JavaScript -->
      </div>
    </div>
  </div>
</div>

<script src="{{ 'inventory-validation-test.js' | asset_url }}" defer></script>

<script>
document.addEventListener('DOMContentLoaded', function() {
  const runTestsBtn = document.getElementById('run-inventory-tests');
  const clearConsoleBtn = document.getElementById('clear-console');
  const testResultsDiv = document.getElementById('test-results');
  const inventoryDataDiv = document.getElementById('inventory-data-display');

  // Display current inventory data on page
  function displayInventoryData() {
    const quantityInputs = document.querySelectorAll('[data-inventory-quantity]');
    let html = '';

    if (quantityInputs.length === 0) {
      html = '<p>No quantity inputs found on this page. Navigate to a product page to test inventory validation.</p>';
    } else {
      html = '<table style="width: 100%; border-collapse: collapse; margin-top: 10px;">';
      html += '<tr style="background: #f5f5f5;"><th style="border: 1px solid #ddd; padding: 8px;">Input</th><th style="border: 1px solid #ddd; padding: 8px;">Variant ID</th><th style="border: 1px solid #ddd; padding: 8px;">Default Location Inventory</th><th style="border: 1px solid #ddd; padding: 8px;">Total Inventory</th><th style="border: 1px solid #ddd; padding: 8px;">Max Attribute</th></tr>';
      
      quantityInputs.forEach((input, index) => {
        const variantId = input.dataset.quantityVariantId || 'N/A';
        const defaultInventory = input.dataset.inventoryQuantity || 'N/A';
        const totalInventory = input.dataset.totalInventoryQuantity || 'N/A';
        const maxAttr = input.getAttribute('max') || 'N/A';
        
        html += `<tr>
          <td style="border: 1px solid #ddd; padding: 8px;">${index + 1}</td>
          <td style="border: 1px solid #ddd; padding: 8px;">${variantId}</td>
          <td style="border: 1px solid #ddd; padding: 8px;">${defaultInventory}</td>
          <td style="border: 1px solid #ddd; padding: 8px;">${totalInventory}</td>
          <td style="border: 1px solid #ddd; padding: 8px;">${maxAttr}</td>
        </tr>`;
      });
      
      html += '</table>';
    }

    inventoryDataDiv.innerHTML = html;
  }

  // Run tests
  runTestsBtn.addEventListener('click', function() {
    console.clear();
    
    if (typeof InventoryValidationTest !== 'undefined') {
      const tester = new InventoryValidationTest();
      tester.runAllTests();
      
      testResultsDiv.innerHTML = `
        <h3>Test Results</h3>
        <p>✅ Tests completed! Check the browser console for detailed results.</p>
        <p>If you see any failed tests, the inventory validation may need further adjustments.</p>
      `;
    } else {
      testResultsDiv.innerHTML = `
        <h3>Test Results</h3>
        <p>❌ Test script not loaded. Please ensure inventory-validation-test.js is properly included.</p>
      `;
    }
  });

  // Clear console
  clearConsoleBtn.addEventListener('click', function() {
    console.clear();
    testResultsDiv.innerHTML = `
      <h3>Test Results</h3>
      <p>Console cleared. Click "Run Inventory Tests" to start testing.</p>
    `;
  });

  // Display inventory data on page load
  displayInventoryData();
});
</script>

<style>
.inventory-test-section {
  margin-top: 2rem;
  padding: 2rem;
  border: 1px solid #ddd;
  border-radius: 8px;
  background: #f9f9f9;
}

.test-controls {
  margin: 1rem 0;
}

.test-controls button {
  margin-right: 1rem;
  margin-bottom: 1rem;
}

.test-results {
  margin: 2rem 0;
  padding: 1rem;
  background: white;
  border-radius: 4px;
  border: 1px solid #ddd;
}

.test-info {
  margin: 2rem 0;
}

.test-info ul {
  margin-left: 1rem;
}

.current-inventory-info {
  margin: 2rem 0;
}

.current-inventory-info table {
  font-size: 0.9rem;
}
</style>
