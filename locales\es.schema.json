{"settings_schema": {"colors": {"name": "Colores", "settings": {"background": {"label": "Fondo"}, "background_gradient": {"label": "Degradado de fondo", "info": "El degradado de fondo reemplaza el fondo donde es posible."}, "text": {"label": "Texto"}, "button_background": {"label": "Fondo de botón sólido"}, "button_label": {"label": "Etiqueta de botón sólido"}, "secondary_button_label": {"label": "Botón de contorno"}, "shadow": {"label": "Sombra"}}}, "typography": {"name": "Tipografía", "settings": {"type_header_font": {"label": "Fuente", "info": "Seleccionar una fuente diferente puede afectar la velocidad de tu tienda online. [Más información sobre fuentes de sistema.](https://help.shopify.com/manual/online-store/os/store-speed/improving-speed#fonts)"}, "header__1": {"content": "<PERSON><PERSON><PERSON><PERSON>"}, "header__2": {"content": "<PERSON><PERSON><PERSON>"}, "type_body_font": {"label": "Fuente", "info": "Seleccionar una fuente diferente puede afectar la velocidad de tu tienda online. [Más información sobre fuentes de sistema.](https://help.shopify.com/manual/online-store/os/store-speed/improving-speed#fonts)"}, "heading_scale": {"label": "Escala de tamaño de fuente"}, "body_scale": {"label": "Escala de tamaño de fuente"}}}, "social-media": {"name": "Redes sociales", "settings": {"social_twitter_link": {"label": "Twitter", "info": "https://twitter.com/shopify"}, "social_facebook_link": {"label": "Facebook", "info": "https://www.facebook.com/ShopifyES/"}, "social_pinterest_link": {"label": "Pinterest", "info": "https://pinterest.com/shopify"}, "social_instagram_link": {"label": "Instagram", "info": "http://instagram.com/shopify"}, "social_tiktok_link": {"label": "TikTok", "info": "https://tiktok.com/@shopify"}, "social_tumblr_link": {"label": "Tumblr", "info": "https://shopify.tumblr.com"}, "social_snapchat_link": {"label": "Snapchat", "info": "https://www.snapchat.com/add/shopify"}, "social_youtube_link": {"label": "YouTube", "info": "https://www.youtube.com/shopify"}, "social_vimeo_link": {"label": "Vimeo", "info": "https://vimeo.com/shopify"}, "header": {"content": "Cuentas de redes sociales"}}}, "currency_format": {"name": "Formato de moneda", "settings": {"content": "Códigos de moneda", "currency_code_enabled": {"label": "Mostrar códigos de moneda"}, "paragraph": "Los precios en el carrito y la pantalla de pago siempre muestran códigos de moneda. Ejemplo: USD 1,00."}}, "layout": {"name": "Diseño", "settings": {"page_width": {"label": "<PERSON><PERSON>"}, "spacing_sections": {"label": "Espacio entre las secciones de la plantilla"}, "header__grid": {"content": "Cuadrícula"}, "paragraph__grid": {"content": "Afecta a áreas con varias columnas o filas."}, "spacing_grid_horizontal": {"label": "Espacio horizontal"}, "spacing_grid_vertical": {"label": "Espacio vertical"}}}, "search_input": {"name": "Comportamiento de búsqueda", "settings": {"header": {"content": "Sugerencias de búsqueda"}, "predictive_search_enabled": {"label": "Activar sugerencias de búsqueda"}, "predictive_search_show_vendor": {"label": "Mostrar proveedor del producto", "info": "Visible cuando las sugerencias de búsqueda están activadas."}, "predictive_search_show_price": {"label": "Mostrar el precio del producto", "info": "Visible cuando las sugerencias de búsqueda están activadas."}}}, "global": {"settings": {"header__border": {"content": "<PERSON>rde"}, "header__shadow": {"content": "Sombra"}, "blur": {"label": "Difuminar"}, "corner_radius": {"label": "Radio de esquina"}, "horizontal_offset": {"label": "Desalineación horizontal"}, "vertical_offset": {"label": "Desalineación vertical"}, "thickness": {"label": "Grosor"}, "opacity": {"label": "Opacidad"}, "image_padding": {"label": "<PERSON><PERSON><PERSON>"}, "text_alignment": {"options__1": {"label": "Iz<PERSON>erda"}, "options__2": {"label": "Centrado"}, "options__3": {"label": "Derecha"}, "label": "Alineación de texto"}}}, "badges": {"name": "<PERSON><PERSON><PERSON>", "settings": {"position": {"options__1": {"label": "Abajo a la izquierda"}, "options__2": {"label": "Abajo a la derecha"}, "options__3": {"label": "Arriba a la izquierda"}, "options__4": {"label": "Arriba a la derecha"}, "label": "Posición de las tarjetas"}, "sale_badge_color_scheme": {"label": "Esquema de color de distintivo de oferta"}, "sold_out_badge_color_scheme": {"label": "Esquema de color de emblema de agotado"}}}, "buttons": {"name": "Botones"}, "variant_pills": {"name": "Botones de variantes", "paragraph": "Los botones de variantes son una forma de mostrar las variantes de producto. [Más información](https://help.shopify.com/en/manual/online-store/themes/theme-structure/page-types#variant-picker-block)"}, "inputs": {"name": "Entradas"}, "content_containers": {"name": "Contenedores de contenido"}, "popups": {"name": "Menús desplegables y ventanas emergentes", "paragraph": "Afecta áreas como los menús desplegables de navegación, las ventanas emergentes y los carritos emergentes."}, "media": {"name": "Multimedia"}, "drawers": {"name": "<PERSON><PERSON><PERSON>"}, "cart": {"name": "<PERSON><PERSON>", "settings": {"cart_type": {"label": "<PERSON><PERSON><PERSON> de <PERSON>", "drawer": {"label": "Lateral"}, "page": {"label": "<PERSON><PERSON><PERSON><PERSON>"}, "notification": {"label": "Notificación emergente"}}, "show_vendor": {"label": "<PERSON><PERSON> proveedor"}, "show_cart_note": {"label": "Habilitar nota del carrito"}, "cart_drawer": {"header": "Carrito lateral", "collection": {"label": "Colección", "info": "Visible cuando el carrito lateral está vacío."}}}}, "cards": {"name": "Tarjetas de producto", "settings": {"style": {"options__1": {"label": "<PERSON><PERSON><PERSON><PERSON>"}, "options__2": {"label": "Tarjeta"}, "label": "<PERSON><PERSON><PERSON>"}}}, "collection_cards": {"name": "Tarjetas de colección", "settings": {"style": {"options__1": {"label": "<PERSON><PERSON><PERSON><PERSON>"}, "options__2": {"label": "Tarjeta"}, "label": "<PERSON><PERSON><PERSON>"}}}, "blog_cards": {"name": "Tarjetas de blogs", "settings": {"style": {"options__1": {"label": "<PERSON><PERSON><PERSON><PERSON>"}, "options__2": {"label": "Tarjeta"}, "label": "<PERSON><PERSON><PERSON>"}}}, "logo": {"name": "Logo", "settings": {"logo_image": {"label": "Logo"}, "logo_width": {"label": "Ancho del logo de escritorio", "info": "El ancho del logo se optimiza automáticamente para dispositivos móviles."}, "favicon": {"label": "Imagen de favicon", "info": "Se reducirá a 32 × 32 px"}}}, "brand_information": {"name": "Información de marca", "settings": {"brand_headline": {"label": "Titular"}, "brand_description": {"label": "Descripción"}, "brand_image": {"label": "Imagen"}, "brand_image_width": {"label": "<PERSON><PERSON>"}, "paragraph": {"content": "Agrega una descripción de marca al pie de página de la tienda."}}}, "animations": {"name": "Animaciones", "settings": {"animations_reveal_on_scroll": {"label": "Revelar secciones al desplazarse"}, "animations_hover_elements": {"options__1": {"label": "Ninguna"}, "options__2": {"label": "Elevación vertical"}, "label": "Efecto hover", "info": "Afecta a tarjetas y botones.", "options__3": {"label": "3D Lift"}}}}}, "sections": {"all": {"padding": {"section_padding_heading": "<PERSON><PERSON><PERSON>", "padding_top": "Relleno superior", "padding_bottom": "Relleno inferior"}, "spacing": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "colors": {"label": "Esquema de colores", "has_cards_info": "Para cambiar el esquema de color de la tarjeta, actualiza la configuración del tema."}, "heading_size": {"label": "Tamaño del título", "options__1": {"label": "Pequeño"}, "options__2": {"label": "Mediano"}, "options__3": {"label": "Grande"}, "options__4": {"label": "Extra grande"}}, "image_shape": {"options__1": {"label": "Predeterminado"}, "options__2": {"label": "Arco"}, "options__3": {"label": "<PERSON><PERSON>"}, "options__4": {"label": "Comillas angulares izquierda"}, "options__5": {"label": "Comillas angulares derecha"}, "options__6": {"label": "Diamante"}, "options__7": {"label": "Paralelogramo"}, "options__8": {"label": "Redonda"}, "label": "Forma de la imagen", "info": "Las tarjetas de estilo estándar no tienen bordes cuando la forma de una imagen está activa."}, "animation": {"content": "Animaciones", "image_behavior": {"options__1": {"label": "Ninguna"}, "options__2": {"label": "Movimiento de ambiente"}, "label": "Comportamiento de la imagen", "options__3": {"label": "Posición del fondo fija"}, "options__4": {"label": "Amp<PERSON><PERSON> al desplazarse"}}}}, "announcement-bar": {"name": "Barra de anuncios", "blocks": {"announcement": {"name": "<PERSON><PERSON><PERSON>", "settings": {"text": {"label": "Texto"}, "text_alignment": {"label": "Alineación de texto", "options__1": {"label": "Iz<PERSON>erda"}, "options__2": {"label": "Centrado"}, "options__3": {"label": "Derecha"}}, "link": {"label": "Enlace"}}}}, "settings": {"auto_rotate": {"label": "Rotar anuncios automáticamente"}, "change_slides_speed": {"label": "Cambiar cada"}, "header__1": {"content": "Íconos de redes sociales", "info": "Para mostrar tus cuentas de redes sociales, vincúlalas en la [configuración del tema](/editor?context=theme&category=social%20media)."}, "header__2": {"content": "<PERSON><PERSON><PERSON><PERSON>"}, "show_social": {"label": "Mostrar íconos en el escritorio"}, "header__3": {"content": "Selector de país o región", "info": "Para agregar un país o una región, ve a la [configuración del mercado.](/admin/settings/markets)"}, "enable_country_selector": {"label": "Activar selector de país o región"}, "header__4": {"content": "Selector de idioma", "info": "Para agregar un idioma, ve a la [configuración de idiomas.](/admin/settings/languages)"}, "enable_language_selector": {"label": "Activar selector de idioma"}}, "presets": {"name": "Barra de anuncios"}}, "collage": {"name": "Collage", "settings": {"heading": {"label": "<PERSON><PERSON><PERSON><PERSON>"}, "desktop_layout": {"label": "Diseño para computadora", "options__1": {"label": "Bloque grande izquierdo"}, "options__2": {"label": "Bloque grande derecho"}}, "mobile_layout": {"label": "Diseño para móviles", "options__1": {"label": "Collage"}, "options__2": {"label": "Columna"}}, "card_styles": {"label": "Estilo de <PERSON>rjet<PERSON>", "info": "La configuración del tema permite actualizar los estilos de tarjeta de blog, producto y colección.", "options__1": {"label": "Usar estilos de tarjeta individuales"}, "options__2": {"label": "Definir todos los estilos como tarjetas de producto"}}}, "blocks": {"image": {"name": "Imagen", "settings": {"image": {"label": "Imagen"}}}, "product": {"name": "Producto", "settings": {"product": {"label": "Producto"}, "secondary_background": {"label": "Mostrar fondo secundario"}, "second_image": {"label": "Mostrar segunda imagen al pasar el cursor"}}}, "collection": {"name": "Colección", "settings": {"collection": {"label": "Colección"}}}, "video": {"name": "Video", "settings": {"cover_image": {"label": "Imagen de portada"}, "video_url": {"label": "URL", "info": "El video se reproduce en una ventana emergente si la sección contiene otros bloques.", "placeholder": "Utiliza una URL de YouTube o Vimeo"}, "description": {"label": "Texto alternativo del video", "info": "Describe el video para los clientes que usan lectores de pantalla. [Más información](https://help.shopify.com/manual/online-store/themes/theme-structure/theme-features#video-block)"}}}}, "presets": {"name": "Collage"}}, "collection-list": {"name": "Lista de colecciones", "settings": {"title": {"label": "<PERSON><PERSON><PERSON><PERSON>"}, "image_ratio": {"label": "Relación de aspecto de imagen", "options__1": {"label": "Adaptar a la imagen"}, "options__2": {"label": "Retrato"}, "options__3": {"label": "Cuadrado"}, "info": "Agregar imágenes editando tus colecciones. [Más información](https://help.shopify.com/manual/products/collections)"}, "swipe_on_mobile": {"label": "Activar uso de banda magnética en el móvil"}, "show_view_all": {"label": "Habilitar el botón \"Ver todos\" si la lista incluye más colecciones de las que se muestran"}, "columns_desktop": {"label": "Número de columnas en el escritorio"}, "header_mobile": {"content": "Diseño para móviles"}, "columns_mobile": {"label": "Número de columnas en la versión para móviles", "options__1": {"label": "1 columna"}, "options__2": {"label": "2 columnas"}}}, "blocks": {"featured_collection": {"name": "Colección", "settings": {"collection": {"label": "Colección"}}}}, "presets": {"name": "Lista de colecciones"}}, "contact-form": {"name": "Formulario de contacto", "presets": {"name": "Formulario de contacto"}}, "custom-liquid": {"name": "Liquid personalizado", "settings": {"custom_liquid": {"label": "Código de Liquid", "info": "Agrega fragmentos de la aplicación u otros códigos para crear personalizaciones avanzadas. [Más información](https://shopify.dev/docs/api/liquid)"}}, "presets": {"name": "Liquid personalizado"}}, "featured-blog": {"name": "Artículos de blog", "settings": {"heading": {"label": "<PERSON><PERSON><PERSON><PERSON>"}, "blog": {"label": "Blog"}, "post_limit": {"label": "Número de artículos del blog que mostrar"}, "show_view_all": {"label": "Habilitar el botón \"Ver todos\" si el blog tiene más artículos de los que se muestran"}, "show_image": {"label": "Mostrar imagen destacada", "info": "Para resultados ópt<PERSON>, utiliza una imagen con una relación de aspecto 3:2. [Más información](https://help.shopify.com/manual/shopify-admin/productivity-tools/image-editor#understanding-image-aspect-ratio)"}, "show_date": {"label": "<PERSON>rar fecha"}, "show_author": {"label": "Mostrar autor"}, "columns_desktop": {"label": "Número de columnas en el escritorio"}}, "presets": {"name": "Artículos de blog"}}, "featured-collection": {"name": "Colección destacada", "settings": {"title": {"label": "<PERSON><PERSON><PERSON><PERSON>"}, "collection": {"label": "Colección"}, "products_to_show": {"label": "Máximo de productos para mostrar"}, "show_view_all": {"label": "Habilitar \"Ver todos\" si la colección tiene más productos de los que se muestran"}, "header": {"content": "Tarjeta de producto"}, "image_ratio": {"label": "Relación de aspecto de imagen", "options__1": {"label": "Adaptar a la imagen"}, "options__2": {"label": "Retrato"}, "options__3": {"label": "Cuadrado"}}, "show_secondary_image": {"label": "Mostrar segunda imagen al pasar el cursor"}, "show_vendor": {"label": "<PERSON><PERSON> proveedor"}, "show_rating": {"label": "Mostrar calificación de productos", "info": "Para mostrar una calificación, agrega una aplicación de calificación de productos [Más información](https://help.shopify.com/manual/online-store/themes/theme-structure/theme-features#featured-collection-show-product-rating)"}, "columns_desktop": {"label": "Número de columnas en el escritorio"}, "description": {"label": "Descripción"}, "show_description": {"label": "Mostrar descripción de la colección desde el panel de control"}, "description_style": {"label": "Estilo de descripción", "options__1": {"label": "<PERSON><PERSON><PERSON>"}, "options__2": {"label": "Subtítulo"}, "options__3": {"label": "<PERSON><PERSON><PERSON>"}}, "view_all_style": {"label": "<PERSON><PERSON><PERSON> \"Ver todos\"", "options__1": {"label": "Enlace"}, "options__2": {"label": "Botón con contorno"}, "options__3": {"label": "Botón sólido"}}, "enable_desktop_slider": {"label": "Activar carrusel en computadora"}, "full_width": {"label": "Definir ancho completo de productos"}, "header_mobile": {"content": "Diseño para móviles"}, "columns_mobile": {"label": "Número de columnas en la versión para móviles", "options__1": {"label": "1 columna"}, "options__2": {"label": "2 columnas"}}, "swipe_on_mobile": {"label": "Activar uso de banda magnética en el móvil"}, "enable_quick_buy": {"label": "Activar botón de agregado rápido", "info": "Funciona de manera óptima con ventanas emergentes o carritos laterales."}}, "presets": {"name": "Colección destacada"}}, "footer": {"name": "Pie de página", "blocks": {"link_list": {"name": "Menú", "settings": {"heading": {"label": "Encabezado"}, "menu": {"label": "Menú", "info": "Muestra solo los elementos del menú de nivel superior."}}}, "text": {"name": "Texto", "settings": {"heading": {"label": "Encabezado"}, "subtext": {"label": "Subtexto"}}}, "brand_information": {"name": "Información de marca", "settings": {"paragraph": {"content": "Este bloque mostrará la información de tu marca. [Editar información de marca.](/editor?context=theme&category=brand%20information)"}, "header__1": {"content": "Íconos de redes sociales"}, "show_social": {"label": "Mostrar íconos de redes sociales", "info": "Para mostrar tus cuentas de redes sociales, vincúlalas en la [configuración de tu tema](/editor?context=theme&category=social%20media)."}}}}, "settings": {"newsletter_enable": {"label": "Mostrar suscriptor de correo electrónico"}, "newsletter_heading": {"label": "Encabezado"}, "header__1": {"content": "Suscriptor de correo electrónico", "info": "Suscriptores agregados automáticamente a tu lista de clientes \"marketing aceptado\". [Más información](https://help.shopify.com/manual/customers/manage-customers)"}, "header__2": {"content": "Íconos de redes sociales", "info": "Para mostrar tus cuentas de redes sociales, vincúlalas en la [configuración de tu tema](/editor?context=theme&category=social%20media)."}, "show_social": {"label": "Mostrar íconos de redes sociales"}, "header__3": {"content": "Selector de país/región"}, "header__4": {"info": "Para agregar un país o una región, ve a la [configuración del mercado.](/admin/settings/markets)"}, "enable_country_selector": {"label": "Activar selector de país/región"}, "header__5": {"content": "Selector de idioma"}, "header__6": {"info": "Para agregar un idioma, ve a tu [configuración de idiomas.](/admin/settings/languages)"}, "enable_language_selector": {"label": "Activar selector de idioma"}, "header__7": {"content": "Formas de pago"}, "payment_enable": {"label": "Mostrar íconos de pago"}, "margin_top": {"label": "Margen superior"}, "header__8": {"content": "Enlaces a las políticas", "info": "Para agregar las políticas de la tienda, ve a tu [configuración de políticas](/admin/settings/legal)."}, "show_policy": {"label": "Mostrar enlaces a las políticas"}, "header__9": {"content": "<PERSON><PERSON><PERSON> en <PERSON>", "info": "Para que los clientes puedan seguir tu tienda en la aplicación Shop desde la tienda, Shop Pay debe estar activado. [Más información](https://help.shopify.com/manual/online-store/themes/customizing-themes/follow-on-shop)"}, "enable_follow_on_shop": {"label": "Activar Seguir en <PERSON>"}}}, "header": {"name": "Encabezado", "settings": {"logo_position": {"label": "Posición de logo en computadora", "options__1": {"label": "Centrado a la izquierda"}, "options__2": {"label": "Arriba a la izquierda"}, "options__3": {"label": "Superior centrada"}, "options__4": {"label": "Centrado en el medio"}}, "menu": {"label": "Menú"}, "show_line_separator": {"label": "Mostrar línea separadora"}, "margin_bottom": {"label": "Margen inferior"}, "menu_type_desktop": {"label": "Tipo de menú de escritorio", "info": "El tipo de menú se optimiza automáticamente para celular.", "options__1": {"label": "Desplegable"}, "options__2": {"label": "Mega menú"}, "options__3": {"label": "Menú lateral"}}, "mobile_layout": {"content": "Diseño para dispositivo móvil"}, "mobile_logo_position": {"label": "Posición del logo en dispositivo móvil", "options__1": {"label": "Centro"}, "options__2": {"label": "Iz<PERSON>erda"}}, "logo_help": {"content": "Edita tu logotipo en [configuración del tema](/editor?context=theme&category=logo)."}, "sticky_header_type": {"label": "Encabezado fijo", "options__1": {"label": "<PERSON><PERSON><PERSON>"}, "options__2": {"label": "Al desplazarse hacia arriba"}, "options__3": {"label": "Siempre"}, "options__4": {"label": "Reducir el tamaño del logo siempre"}}, "header__3": {"content": "Selector de país o región"}, "header__4": {"info": "Para agregar un país o una región, ve a la [configuración del mercado.](/admin/settings/markets)"}, "enable_country_selector": {"label": "Activar selector de país o región"}, "header__5": {"content": "Selector de idioma"}, "header__6": {"info": "Para agregar un idioma, ve a tu [configuración de idiomas.](/admin/settings/languages)"}, "enable_language_selector": {"label": "Activar selector de idioma"}, "header__1": {"content": "Color"}, "menu_color_scheme": {"label": "Esquema de colores del menú"}}}, "image-banner": {"name": "<PERSON> de imagen", "settings": {"image": {"label": "Primera imagen"}, "image_2": {"label": "Segunda imagen"}, "stack_images_on_mobile": {"label": "Apilar imágenes en móviles"}, "show_text_box": {"label": "Mostrar contenedor en la computadora"}, "image_overlay_opacity": {"label": "Opacidad de la sobreposición de imagen"}, "show_text_below": {"label": "Mostrar contenedor en el móvil"}, "image_height": {"label": "Altura del banner", "options__1": {"label": "Adaptar a la primera imagen"}, "options__2": {"label": "Pequeño"}, "options__3": {"label": "Mediano"}, "info": "Para resultados ópt<PERSON>, utiliza una imagen con una relación de aspecto 3:2. [Más información](https://help.shopify.com/manual/shopify-admin/productivity-tools/image-editor#understanding-image-aspect-ratio)", "options__4": {"label": "Grande"}}, "desktop_content_position": {"options__1": {"label": "Arriba a la izquierda"}, "options__2": {"label": "Arriba en el centro"}, "options__3": {"label": "Arriba a la derecha"}, "options__4": {"label": "Centrado a la izquierda"}, "options__5": {"label": "Centrado en el medio"}, "options__6": {"label": "Centrado a la derecha"}, "options__7": {"label": "Abajo a la izquierda"}, "options__8": {"label": "Abajo en el centro"}, "options__9": {"label": "Abajo a la derecha"}, "label": "Posición del contenido en la computadora"}, "desktop_content_alignment": {"options__1": {"label": "Iz<PERSON>erda"}, "options__2": {"label": "Centro"}, "options__3": {"label": "Derecha"}, "label": "Alineación del contenido en la computadora"}, "mobile_content_alignment": {"options__1": {"label": "Iz<PERSON>erda"}, "options__2": {"label": "Centro"}, "options__3": {"label": "Derecha"}, "label": "Alineación del contenido en el móvil"}, "mobile": {"content": "Diseño para móviles"}}, "blocks": {"heading": {"name": "<PERSON><PERSON><PERSON><PERSON>", "settings": {"heading": {"label": "<PERSON><PERSON><PERSON><PERSON>"}}}, "text": {"name": "Texto", "settings": {"text": {"label": "Descripción"}, "text_style": {"options__1": {"label": "<PERSON><PERSON><PERSON>"}, "options__2": {"label": "Subtítulo"}, "options__3": {"label": "<PERSON><PERSON><PERSON>"}, "label": "Estilo de texto"}}}, "buttons": {"name": "Botones", "settings": {"button_label_1": {"label": "Primera etiqueta de botón", "info": "Deja la etiqueta en blanco para ocultar el botón."}, "button_link_1": {"label": "Primer en<PERSON> de bot<PERSON>"}, "button_style_secondary_1": {"label": "Usar estilo de botón con contorno"}, "button_label_2": {"label": "Primera etiqueta de botón", "info": "Deja la etiqueta en blanco para ocultar el botón."}, "button_link_2": {"label": "<PERSON><PERSON><PERSON> en<PERSON> de bot<PERSON>"}, "button_style_secondary_2": {"label": "Usar estilo de botón con contorno"}}}}, "presets": {"name": "<PERSON> de imagen"}}, "image-with-text": {"name": "Imagen con texto", "settings": {"image": {"label": "Imagen"}, "height": {"options__1": {"label": "Adaptar a la imagen"}, "options__2": {"label": "Pequeño"}, "options__3": {"label": "Mediano"}, "label": "Altura de imagen", "options__4": {"label": "Grande"}}, "layout": {"options__1": {"label": "Imagen primero"}, "options__2": {"label": "<PERSON>n segunda"}, "label": "Ubicación de la imagen en computadoras", "info": "La imagen primero es el diseño predeterminado para móviles."}, "desktop_image_width": {"options__1": {"label": "Pequeña"}, "options__2": {"label": "Mediana"}, "options__3": {"label": "Grande"}, "label": "<PERSON><PERSON> de <PERSON>n en computadoras", "info": "La imagen se optimiza automáticamente para dispositivos móviles."}, "desktop_content_alignment": {"options__1": {"label": "Iz<PERSON>erda"}, "options__2": {"label": "Centrado"}, "options__3": {"label": "Derecha"}, "label": "Alineación del contenido en computadoras"}, "desktop_content_position": {"options__1": {"label": "Arriba"}, "options__2": {"label": "Centrada"}, "options__3": {"label": "Abajo"}, "label": "Posición del contenido en computadoras"}, "content_layout": {"options__1": {"label": "<PERSON> solapamiento"}, "options__2": {"label": "Solapamiento"}, "label": "Diseño de contenido"}, "mobile_content_alignment": {"options__1": {"label": "Iz<PERSON>erda"}, "options__2": {"label": "Centrado"}, "options__3": {"label": "Derecha"}, "label": "Alineación del contenido en dispositivos móviles"}}, "blocks": {"heading": {"name": "<PERSON><PERSON><PERSON><PERSON>", "settings": {"heading": {"label": "<PERSON><PERSON><PERSON><PERSON>"}}}, "text": {"name": "Texto", "settings": {"text": {"label": "Contenido"}, "text_style": {"label": "Estilo de texto", "options__1": {"label": "<PERSON><PERSON><PERSON>"}, "options__2": {"label": "Subtítulo"}}}}, "button": {"name": "Botón", "settings": {"button_label": {"label": "Etiqueta de botón", "info": "Deja la etiqueta en blanco para ocultar el botón."}, "button_link": {"label": "<PERSON>lace de botón"}, "outline_button": {"label": "Usar estilo de botón con contorno"}}}, "caption": {"name": "Leyenda", "settings": {"text": {"label": "Texto"}, "text_style": {"label": "Estilo de texto", "options__1": {"label": "Subtítulo"}, "options__2": {"label": "<PERSON><PERSON><PERSON>"}}, "caption_size": {"label": "Tamaño del texto", "options__1": {"label": "Pequeño"}, "options__2": {"label": "Mediano"}, "options__3": {"label": "Grande"}}}}}, "presets": {"name": "Imagen con texto"}}, "main-article": {"name": "Artí<PERSON>lo de <PERSON>", "blocks": {"featured_image": {"name": "Imagen destacada", "settings": {"image_height": {"label": "Altura de imagen destacada", "options__1": {"label": "Adaptar a la imagen"}, "options__2": {"label": "Pequeña"}, "options__3": {"label": "Mediana"}, "info": "Para mejores resultados, utiliza una imagen con una relación de aspecto 16:9. [Más información](https://help.shopify.com/manual/shopify-admin/productivity-tools/image-editor#understanding-image-aspect-ratio)", "options__4": {"label": "Grande"}}}}, "title": {"name": "<PERSON><PERSON><PERSON><PERSON>", "settings": {"blog_show_date": {"label": "<PERSON>rar fecha"}, "blog_show_author": {"label": "Mostrar autor"}}}, "content": {"name": "Contenido"}, "share": {"name": "Compartir", "settings": {"featured_image_info": {"content": "Si incluyes un enlace en publicaciones de redes sociales, la imagen destacada de la página se mostrará como la imagen de vista previa. [Más información](https://help.shopify.com/manual/online-store/images/showing-social-media-thumbnail-images)."}, "title_info": {"content": "Con la imagen de vista previa se incluye un nombre y descripción de la tienda. [Más información](https://help.shopify.com/manual/promoting-marketing/seo/adding-keywords#set-a-title-and-description-for-your-online-store)"}, "text": {"label": "texto"}}}}}, "main-blog": {"name": "Artículos de blog", "settings": {"header": {"content": "Tarjeta de artículo de blog"}, "show_image": {"label": "Mostrar imagen destacada"}, "paragraph": {"content": "Cambiar extractos editando tus artículo del blog. [Más información](https://help.shopify.com/manual/online-store/blogs/writing-blogs#display-an-excerpt-from-a-blog-post)"}, "show_date": {"label": "<PERSON>rar fecha"}, "show_author": {"label": "Mostrar autor"}, "layout": {"label": "Diseño para computadora", "options__1": {"label": "Cuadrícula"}, "options__2": {"label": "Collage"}, "info": "Las publicaciones se apilaron en el dispositivo móvil."}, "image_height": {"label": "Altura de imagen destacada", "options__1": {"label": "Adaptar a la imagen"}, "options__2": {"label": "Pequeña"}, "options__3": {"label": "Mediana"}, "options__4": {"label": "Grande"}, "info": "Para resultados ópt<PERSON>, utiliza una imagen con una relación de aspecto 3:2. [Más información](https://help.shopify.com/manual/shopify-admin/productivity-tools/image-editor#understanding-image-aspect-ratio)"}}}, "main-cart-footer": {"name": "Subtotal", "blocks": {"subtotal": {"name": "Precio subtotal"}, "buttons": {"name": "Botón de pago"}}}, "main-cart-items": {"name": "<PERSON><PERSON><PERSON><PERSON>"}, "main-collection-banner": {"name": "Banner de colección", "settings": {"paragraph": {"content": "agregar una descripción o imagen editando tu colección. [Más información](https://help.shopify.com/manual/products/collections/collection-layout)"}, "show_collection_description": {"label": "Mostrar descripción de la colección"}, "show_collection_image": {"label": "Mostrar imagen de la colección", "info": "Para mejores resultados, utiliza una imagen con una relación de aspecto 16:9. [Más información](https://help.shopify.com/manual/shopify-admin/productivity-tools/image-editor#understanding-image-aspect-ratio)"}}}, "main-collection-product-grid": {"name": "Cuadrícula de productos", "settings": {"products_per_page": {"label": "Productos por página"}, "image_ratio": {"label": "Relación de aspecto de imagen", "options__1": {"label": "Adaptar a la imagen"}, "options__2": {"label": "Retrato"}, "options__3": {"label": "Cuadrado"}}, "show_secondary_image": {"label": "Mostrar segunda imagen al pasar el cursor"}, "show_vendor": {"label": "<PERSON><PERSON> proveedor"}, "enable_tags": {"label": "Habilitar filtrado", "info": "Personaliza los filtros con la aplicación Search & Discovery. [Más información](https://help.shopify.com/manual/online-store/search-and-discovery/filters)"}, "enable_filtering": {"label": "Habilitar filtrado", "info": "Personaliza los filtros con la aplicación Search & Discovery. [Más información](https://help.shopify.com/manual/online-store/search-and-discovery/filters)"}, "enable_sorting": {"label": "Habilitar ordenado"}, "header__1": {"content": "Filtrado y ordenado"}, "header__3": {"content": "Tarjeta de producto"}, "show_rating": {"label": "Mostrar calificación de productos", "info": "Para mostrar una calificación, agrega una aplicación de calificación de productos [Más información](https://help.shopify.com/manual/online-store/themes/theme-structure/page-types#product-grid-section-settings)"}, "columns_desktop": {"label": "Número de columnas en el escritorio"}, "header_mobile": {"content": "Diseño para móviles"}, "columns_mobile": {"label": "Número de columnas en la versión para móviles", "options__1": {"label": "1 columna"}, "options__2": {"label": "2 columnas"}}, "enable_quick_buy": {"label": "Activar botón de agregado rápido", "info": "Funciona de manera óptima con ventanas emergentes o carritos laterales."}, "filter_type": {"label": "Filtro de diseño para computadora", "options__1": {"label": "Horizontal"}, "options__2": {"label": "Vertical"}, "options__3": {"label": "Cajón"}, "info": "El cajón es el diseño para móviles predeterminado."}}}, "main-list-collections": {"name": "Página de lista de colecciones", "settings": {"title": {"label": "<PERSON><PERSON><PERSON><PERSON>"}, "sort": {"label": "Ordenar colecciones por:", "options__1": {"label": "Alfabéticamente, A-Z"}, "options__2": {"label": "Alfabéticamente, Z-A"}, "options__3": {"label": "Fecha: reciente a antigua"}, "options__4": {"label": "Fecha: antigua a reciente"}, "options__5": {"label": "<PERSON><PERSON><PERSON> de productos, de mayor a menor"}, "options__6": {"label": "<PERSON><PERSON><PERSON> de productos, de menor a mayor"}}, "image_ratio": {"label": "Relación de aspecto de imagen", "options__1": {"label": "Adaptar a la imagen"}, "options__2": {"label": "Retrato"}, "options__3": {"label": "Cuadrado"}, "info": "Agregar imágenes editando tus colecciones. [Más información](https://help.shopify.com/manual/products/collections)"}, "columns_desktop": {"label": "Número de columnas en la versión para computadora"}, "header_mobile": {"content": "Diseño para móviles"}, "columns_mobile": {"label": "Número de columnas en la versión para móviles", "options__1": {"label": "1 columna"}, "options__2": {"label": "2 columnas"}}}}, "main-page": {"name": "<PERSON><PERSON><PERSON><PERSON>"}, "main-password-footer": {"name": "Pie de página de contraseña"}, "main-password-header": {"name": "Encabezado de contraseña", "settings": {"logo_header": {"content": "Logo"}, "logo_help": {"content": "Edita tu logo en la configuración del tema."}}}, "main-product": {"name": "Información de producto", "blocks": {"text": {"name": "Texto", "settings": {"text": {"label": "Texto"}, "text_style": {"label": "Text style", "options__1": {"label": "<PERSON><PERSON><PERSON>"}, "options__2": {"label": "Subtítulo"}, "options__3": {"label": "<PERSON><PERSON><PERSON>"}}}}, "title": {"name": "<PERSON><PERSON><PERSON><PERSON>"}, "price": {"name": "Precio"}, "quantity_selector": {"name": "Selector de cantidad"}, "variant_picker": {"name": "Selector de variante", "settings": {"picker_type": {"label": "<PERSON><PERSON><PERSON>", "options__1": {"label": "Desplegable"}, "options__2": {"label": "Miniaturas"}}, "swatch_shape": {"label": "Muestra", "info": "Habilita [muestras](https://help.shopify.com/en/manual/online-store/themes/theme-structure/theme-settings#options-with-swatches) en las opciones de producto.", "options__1": {"label": "<PERSON><PERSON><PERSON><PERSON>"}, "options__2": {"label": "Cuadrado"}, "options__3": {"label": "Ninguna"}}}}, "buy_buttons": {"name": "Botones de compras", "settings": {"show_dynamic_checkout": {"label": "Mostrar botones de pago dinámico", "info": "Utilizando las formas de pago disponibles en tu tienda, los clientes ven la opción de su preferencia, como PayPal o Apple Pay. [Más información](https://help.shopify.com/manual/using-themes/change-the-layout/dynamic-checkout)"}, "show_gift_card_recipient": {"label": "Mostrar el formulario de información de la persona destinataria para las tarjetas de regalo", "info": "Permite que los compradores envíen tarjetas de regalo en una fecha programada junto con un mensaje personal. [Más información](https://help.shopify.com/manual/online-store/themes/customizing-themes/add-gift-card-recipient-fields)"}}}, "pickup_availability": {"name": "Disponibilidad de retiro"}, "description": {"name": "Descripción"}, "share": {"name": "Compartir", "settings": {"featured_image_info": {"content": "Si incluyes un enlace en publicaciones de redes sociales, la imagen destacada de la página se mostrará como la imagen de vista previa. [Más información](https://help.shopify.com/manual/online-store/images/showing-social-media-thumbnail-images)."}, "title_info": {"content": "Con la imagen de vista previa se incluye un nombre y descripción de la tienda. [Más información](https://help.shopify.com/manual/promoting-marketing/seo/adding-keywords#set-a-title-and-description-for-your-online-store)"}, "text": {"label": "texto"}}}, "collapsible_tab": {"name": "Fila desplegable", "settings": {"heading": {"info": "Incluye un título que explique el contenido.", "label": "<PERSON><PERSON><PERSON><PERSON>"}, "content": {"label": "Contenido de fila"}, "page": {"label": "Contenido de fila de la página"}, "icon": {"label": "Ícono", "options__1": {"label": "<PERSON><PERSON><PERSON>"}, "options__2": {"label": "Man<PERSON><PERSON>"}, "options__3": {"label": "<PERSON><PERSON><PERSON><PERSON>"}, "options__4": {"label": "Biberón"}, "options__5": {"label": "Apartado postal"}, "options__6": {"label": "Zanahoria"}, "options__7": {"label": "Globo de chat"}, "options__8": {"label": "Marca de verificación"}, "options__9": {"label": "Portapapeles"}, "options__10": {"label": "Lácteos"}, "options__11": {"label": "Libre de lácteos"}, "options__12": {"label": "Secador"}, "options__13": {"label": "<PERSON><PERSON>"}, "options__14": {"label": "Fuego"}, "options__15": {"label": "Libre de gluten"}, "options__16": {"label": "Corazón"}, "options__17": {"label": "Plancha"}, "options__18": {"label": "Hoja"}, "options__19": {"label": "<PERSON><PERSON><PERSON>"}, "options__20": {"label": "Relámpago"}, "options__21": {"label": "<PERSON><PERSON><PERSON><PERSON> labial"}, "options__22": {"label": "Candado"}, "options__23": {"label": "Alfiler en mapa"}, "options__24": {"label": "Libre de nueces"}, "options__25": {"label": "<PERSON><PERSON><PERSON>"}, "options__26": {"label": "<PERSON><PERSON> de <PERSON>a"}, "options__27": {"label": "Pimienta"}, "options__28": {"label": "Perfume"}, "options__29": {"label": "Avión"}, "options__30": {"label": "Planta"}, "options__31": {"label": "Etiqueta de precio"}, "options__32": {"label": "Signo de interrogación"}, "options__33": {"label": "Reciclar"}, "options__34": {"label": "Devolución"}, "options__35": {"label": "Regla"}, "options__36": {"label": "<PERSON> de <PERSON>rvir"}, "options__37": {"label": "<PERSON><PERSON>"}, "options__38": {"label": "Zapato"}, "options__39": {"label": "<PERSON><PERSON><PERSON>"}, "options__40": {"label": "<PERSON><PERSON> de <PERSON>eve"}, "options__41": {"label": "Estrella"}, "options__42": {"label": "Cronómetro"}, "options__43": {"label": "Camión"}, "options__44": {"label": "<PERSON><PERSON><PERSON>"}}}}, "popup": {"name": "Ventana emergente", "settings": {"link_label": {"label": "Vincular etiqueta"}, "page": {"label": "<PERSON><PERSON><PERSON><PERSON>"}}}, "rating": {"name": "Calificación de los productos", "settings": {"paragraph": {"content": "Para mostrar una calificación, agrega una aplicación de calificación de productos [Más información](https://help.shopify.com/manual/online-store/themes/theme-structure/page-types#product-rating-block)"}}}, "complementary_products": {"name": "Productos complementarios", "settings": {"paragraph": {"content": "Para seleccionar productos complementarios, agrega la aplicación Search & Discovery. [Más información](https://help.shopify.com/manual/online-store/search-and-discovery/product-recommendations)"}, "heading": {"label": "Encabezado"}, "make_collapsible_row": {"label": "Mostrar como una fila plegable"}, "icon": {"info": "Visible cuando se muestra la fila plegable."}, "product_list_limit": {"label": "Número máximo de productos para mostrar"}, "products_per_page": {"label": "Cantidad de productos por página"}, "pagination_style": {"label": "Estilo de paginación", "options": {"option_1": "Punt<PERSON>", "option_2": "<PERSON><PERSON><PERSON>", "option_3": "Números"}}, "product_card": {"heading": "Tarjeta de producto"}, "image_ratio": {"label": "Relación de aspecto de imagen", "options": {"option_1": "Retrato", "option_2": "Cuadrado"}}, "enable_quick_add": {"label": "Activar botón de agregado rápido"}}}, "icon_with_text": {"name": "Ícono con texto", "settings": {"layout": {"label": "Diseño", "options__1": {"label": "Horizontal"}, "options__2": {"label": "Vertical"}}, "content": {"label": "Contenido", "info": "Elige un ícono o agrega una imagen para cada columna o fila."}, "heading": {"info": "Deja la etiqueta de encabezado en blanco para ocultar la columna del ícono."}, "icon_1": {"label": "Primer <PERSON><PERSON><PERSON>"}, "image_1": {"label": "Primera imagen"}, "heading_1": {"label": "Primer <PERSON><PERSON><PERSON><PERSON><PERSON>"}, "icon_2": {"label": "<PERSON><PERSON><PERSON>"}, "image_2": {"label": "Segunda imagen"}, "heading_2": {"label": "<PERSON><PERSON><PERSON>"}, "icon_3": {"label": "<PERSON><PERSON><PERSON>"}, "image_3": {"label": "Tercera imagen"}, "heading_3": {"label": "<PERSON><PERSON><PERSON> <PERSON>"}}}, "sku": {"name": "SKU", "settings": {"text_style": {"label": "Estilo de texto", "options__1": {"label": "<PERSON><PERSON><PERSON>"}, "options__2": {"label": "Subtítulo"}, "options__3": {"label": "<PERSON><PERSON><PERSON>"}}}}, "inventory": {"name": "Estado del inventario", "settings": {"text_style": {"label": "Estilo de texto", "options__1": {"label": "<PERSON><PERSON><PERSON>"}, "options__2": {"label": "Subtítulo"}, "options__3": {"label": "<PERSON><PERSON><PERSON>"}}, "inventory_threshold": {"label": "Umbral de inventario bajo", "info": "Elige 0 para que siempre se muestre en existencias si está disponible."}, "show_inventory_quantity": {"label": "Mostrar recuento de inventario"}}}}, "settings": {"header": {"content": "Multimedia", "info": "Obtén más información sobre [tipos de elementos multimedia.](https://help.shopify.com/manual/products/product-media)"}, "enable_video_looping": {"label": "Activar la reproducción de video en bucle"}, "enable_sticky_info": {"label": "Activar contenido fijo en computadoras"}, "hide_variants": {"label": "Ocultar los elementos multimedia de las demás variantes tras seleccionar una de ellas"}, "gallery_layout": {"label": "Diseño para computadora", "options__1": {"label": "<PERSON><PERSON><PERSON><PERSON>"}, "options__2": {"label": "<PERSON><PERSON> columnas"}, "options__3": {"label": "Miniaturas"}, "options__4": {"label": "Carrusel de miniaturas"}}, "media_size": {"label": "Ancho del elemento multimedia en computadora", "info": "Los elementos multimedia se optimizan automáticamente para dispositivos móviles.", "options__1": {"label": "Pequeño"}, "options__2": {"label": "Mediano"}, "options__3": {"label": "Grande"}}, "mobile_thumbnails": {"label": "Diseño para móviles", "options__1": {"label": "<PERSON><PERSON> columnas"}, "options__2": {"label": "Mostrar miniaturas"}, "options__3": {"label": "Ocultar miniaturas"}}, "media_position": {"label": "Posición de contenido multimedia en el escritorio", "info": "La posición se optimiza automáticamente para dispositivos móviles.", "options__1": {"label": "Iz<PERSON>erda"}, "options__2": {"label": "Derecha"}}, "image_zoom": {"label": "Ampliar imagen", "info": "En dispositivos móviles, la opción Hacer clic y pasar sobre el elemento cambia a Abrir Lightbox de forma predeterminada.", "options__1": {"label": "Abrir Lightbox"}, "options__2": {"label": "<PERSON><PERSON> clic y pasar sobre el elemento"}, "options__3": {"label": "Sin zoom"}}, "constrain_to_viewport": {"label": "Ajustar el elemento multimedia a la altura de la pantalla"}, "media_fit": {"label": "Ajuste del elemento multimedia", "options__1": {"label": "Original"}, "options__2": {"label": "<PERSON><PERSON><PERSON>"}}}}, "main-search": {"name": "Resultados de búsqueda", "settings": {"image_ratio": {"label": "Relación de aspecto de imagen", "options__1": {"label": "Adaptar a la imagen"}, "options__2": {"label": "Retrato"}, "options__3": {"label": "Cuadrado"}}, "show_secondary_image": {"label": "Mostrar segunda imagen al pasar el cursor"}, "show_vendor": {"label": "<PERSON><PERSON> proveedor"}, "header__1": {"content": "Tarjeta de producto"}, "header__2": {"content": "Tarjeta de blog", "info": "Los estilos de tarjeta de blog también se aplican a las tarjetas de página en los resultados de búsqueda. Para cambiar los estilos de tarjeta, modifica la configuración del tema."}, "article_show_date": {"label": "<PERSON>rar fecha"}, "article_show_author": {"label": "Mostrar autor"}, "show_rating": {"label": "Mostrar calificación de productos", "info": "Para mostrar una calificación, agrega una aplicación de calificación de productos [Más información](https://help.shopify.com/manual/online-store/themes/theme-structure/page-types#search-results-section-settings)"}, "columns_desktop": {"label": "Número de columnas en el escritorio"}, "header_mobile": {"content": "Diseño para móviles"}, "columns_mobile": {"label": "Número de columnas en la versión para móviles", "options__1": {"label": "1 columna"}, "options__2": {"label": "2 columnas"}}}}, "multicolumn": {"name": "Multicolumna", "settings": {"title": {"label": "<PERSON><PERSON><PERSON><PERSON>"}, "image_width": {"label": "<PERSON><PERSON>", "options__1": {"label": "Ancho de un tercio de columna"}, "options__2": {"label": "<PERSON><PERSON> de mitad de columna"}, "options__3": {"label": "<PERSON><PERSON> completo de columna"}}, "image_ratio": {"label": "Relación de aspecto de imagen", "options__1": {"label": "Adaptar a la imagen"}, "options__2": {"label": "Retrato"}, "options__3": {"label": "Cuadrado"}, "options__4": {"label": "<PERSON><PERSON><PERSON><PERSON>"}}, "column_alignment": {"label": "Alineación de columna", "options__1": {"label": "Iz<PERSON>erda"}, "options__2": {"label": "Centrado"}}, "background_style": {"label": "Fondo secundario", "options__1": {"label": "<PERSON><PERSON><PERSON>"}, "options__2": {"label": "Mostrar como fondo de columna"}}, "button_label": {"label": "Etiqueta de botón"}, "button_link": {"label": "<PERSON>lace de botón"}, "swipe_on_mobile": {"label": "Activar uso de banda magnética en el móvil"}, "columns_desktop": {"label": "Número de columnas en el escritorio"}, "header_mobile": {"content": "Diseño para móviles"}, "columns_mobile": {"label": "Número de columnas en la versión para móviles", "options__1": {"label": "1 columna"}, "options__2": {"label": "2 columnas"}}}, "blocks": {"column": {"name": "Columna", "settings": {"image": {"label": "Imagen"}, "title": {"label": "<PERSON><PERSON><PERSON><PERSON>"}, "text": {"label": "Descripción"}, "link_label": {"label": "Vincular etiqueta"}, "link": {"label": "Enlace"}}}}, "presets": {"name": "Multicolumna"}}, "newsletter": {"name": "Suscriptor de correo electrónico", "settings": {"full_width": {"label": "Definir ancho completo en sección"}, "paragraph": {"content": "Con cada suscripción a correos electrónicos se crean cuentas de cliente. [Más información](https://help.shopify.com/manual/customers)"}}, "blocks": {"heading": {"name": "<PERSON><PERSON><PERSON><PERSON>", "settings": {"heading": {"label": "<PERSON><PERSON><PERSON><PERSON>"}}}, "paragraph": {"name": "Subtítulo", "settings": {"paragraph": {"label": "Descripción"}}}, "email_form": {"name": "Formulario de correo electrónico"}}, "presets": {"name": "Suscriptor de correo electrónico"}}, "page": {"name": "<PERSON><PERSON><PERSON><PERSON>", "settings": {"page": {"label": "<PERSON><PERSON><PERSON><PERSON>"}}, "presets": {"name": "<PERSON><PERSON><PERSON><PERSON>"}}, "rich-text": {"name": "Texto enriquecido", "settings": {"full_width": {"label": "Definir ancho completo en sección"}, "desktop_content_position": {"options__1": {"label": "Iz<PERSON>erda"}, "options__2": {"label": "Centrado"}, "options__3": {"label": "Derecha"}, "label": "Posición del contenido en el escritorio", "info": "La posición se optimizó automáticamente para dispositivos móviles."}, "content_alignment": {"options__1": {"label": "Iz<PERSON>erda"}, "options__2": {"label": "Centrado"}, "options__3": {"label": "Derecha"}, "label": "Alineación de contenido"}}, "blocks": {"heading": {"name": "<PERSON><PERSON><PERSON><PERSON>", "settings": {"heading": {"label": "<PERSON><PERSON><PERSON><PERSON>"}}}, "text": {"name": "Texto", "settings": {"text": {"label": "Descripción"}}}, "buttons": {"name": "Botones", "settings": {"button_label_1": {"label": "Primera etiqueta de botón", "info": "Deja la etiqueta en blanco para ocultar el botón."}, "button_link_1": {"label": "Primer en<PERSON> de bot<PERSON>"}, "button_style_secondary_1": {"label": "Usar estilo de botón con contorno"}, "button_label_2": {"label": "Segunda etiqueta de botón", "info": "Deja la etiqueta en blanco para ocultar el botón."}, "button_link_2": {"label": "<PERSON><PERSON><PERSON> en<PERSON> de bot<PERSON>"}, "button_style_secondary_2": {"label": "Usar estilo de botón con contorno"}}}, "caption": {"name": "Leyenda", "settings": {"text": {"label": "Texto"}, "text_style": {"label": "Estilo de texto", "options__1": {"label": "Subtítulo"}, "options__2": {"label": "<PERSON><PERSON><PERSON>"}}, "caption_size": {"label": "Tamaño del texto", "options__1": {"label": "Pequeño"}, "options__2": {"label": "Mediano"}, "options__3": {"label": "Grande"}}}}}, "presets": {"name": "Texto enriquecido"}}, "apps": {"name": "Aplicaciones", "settings": {"include_margins": {"label": "Hacer que los márgenes de sección sean iguales al tema"}}, "presets": {"name": "Aplicaciones"}}, "video": {"name": "Video", "settings": {"heading": {"label": "Encabezado"}, "cover_image": {"label": "Imagen de portada"}, "video_url": {"label": "URL", "info": "Usar URL de YouTube o Vimeo"}, "description": {"label": "Texto alternativo del video", "info": "Describe el video para los clientes que usan lectores de pantalla. [Más información](https://help.shopify.com/manual/online-store/themes/theme-structure/theme-features#video)"}, "image_padding": {"label": "Agregar re<PERSON>o de <PERSON>n", "info": "Selecciona relleno de imagen si no deseas que se corte tu imagen de portada."}, "full_width": {"label": "Definir ancho completo en sección"}, "video": {"label": "Video"}, "enable_video_looping": {"label": "Reproducir video en bucle"}, "header__1": {"content": "Video alojado en Shopify"}, "header__2": {"content": "O incrustar video a partir de una URL"}, "header__3": {"content": "<PERSON><PERSON><PERSON>"}, "paragraph": {"content": "Se muestra cuando no se seleccionó ningún video alojado en Shopify."}}, "presets": {"name": "Video"}}, "featured-product": {"name": "Producto destacado", "blocks": {"text": {"name": "Texto", "settings": {"text": {"label": "Texto"}, "text_style": {"label": "Estilo de texto", "options__1": {"label": "<PERSON><PERSON><PERSON>"}, "options__2": {"label": "Subtítulo"}, "options__3": {"label": "<PERSON><PERSON><PERSON>"}}}}, "title": {"name": "<PERSON><PERSON><PERSON><PERSON>"}, "price": {"name": "Precio"}, "quantity_selector": {"name": "Selector de cantidad"}, "variant_picker": {"name": "Selector de variante", "settings": {"picker_type": {"label": "<PERSON><PERSON><PERSON>", "options__1": {"label": "Desplegable"}, "options__2": {"label": "Botones"}}, "swatch_shape": {"label": "Muestra", "info": "Habilita [muestras](https://help.shopify.com/en/manual/online-store/themes/theme-structure/theme-settings#options-with-swatches) en las opciones de producto.", "options__1": {"label": "<PERSON><PERSON><PERSON><PERSON>"}, "options__2": {"label": "Cuadrado"}, "options__3": {"label": "Ninguna"}}}}, "buy_buttons": {"name": "Botones de compras", "settings": {"show_dynamic_checkout": {"label": "Mostrar botones de pago dinámico", "info": "Utilizando las formas de pago disponibles en tu tienda, los clientes ven la opción de su preferencia, como PayPal o Apple Pay. [Más información](https://help.shopify.com/manual/using-themes/change-the-layout/dynamic-checkout)"}}}, "description": {"name": "Descripción"}, "share": {"name": "Compartir", "settings": {"featured_image_info": {"content": "Si incluyes un enlace en publicaciones de redes sociales, la imagen destacada de la página se mostrará como la imagen de vista previa. [Más información](https://help.shopify.com/manual/online-store/images/showing-social-media-thumbnail-images)"}, "title_info": {"content": "Con la imagen de vista previa se incluye un nombre y descripción de la tienda. [Más información](https://help.shopify.com/manual/promoting-marketing/seo/adding-keywords#set-a-title-and-description-for-your-online-store)"}, "text": {"label": "Texto"}}}, "rating": {"name": "Calificación de productos", "settings": {"paragraph": {"content": "Agrega una aplicación para mostrar las calificaciones de los productos. [Más información](https://help.shopify.com/manual/online-store/themes/theme-structure/theme-features#featured-product-rating)"}}}, "sku": {"name": "SKU", "settings": {"text_style": {"label": "Estilo de texto", "options__1": {"label": "<PERSON><PERSON><PERSON>"}, "options__2": {"label": "Subtítulo"}, "options__3": {"label": "<PERSON><PERSON><PERSON>"}}}}}, "settings": {"product": {"label": "Producto"}, "secondary_background": {"label": "Mostrar fondo secundario"}, "header": {"content": "Multimedia", "info": "Más información sobre [tipos de elementos multimedia](https://help.shopify.com/manual/products/product-media)"}, "enable_video_looping": {"label": "Activar la reproducción de video en bucle"}, "hide_variants": {"label": "Ocultar elementos multimedia de variantes no seleccionadas en el escritorio"}, "media_position": {"label": "Posición del contenido multimedia en computadoras de escritorio", "info": "La posición se optimiza automáticamente para dispositivos móviles.", "options__1": {"label": "Iz<PERSON>erda"}, "options__2": {"label": "Derecha"}}}, "presets": {"name": "Producto destacado"}}, "email-signup-banner": {"name": "Banner de suscripción de correo electrónico", "settings": {"paragraph": {"content": "Con cada suscripción a correos electrónicos se crea una cuenta de cliente. [Más información](https://help.shopify.com/manual/customers)"}, "image": {"label": "Imagen de fondo"}, "show_background_image": {"label": "Mostrar imagen de fondo"}, "show_text_box": {"label": "Mostrar contenedor en la computadora"}, "image_overlay_opacity": {"label": "Opacidad de la sobreposición de imagen"}, "show_text_below": {"label": "Mostrar el contenido debajo de la imagen en el móvil", "info": "Para mejores resultados, utiliza una imagen con una relación de aspecto 16:9. [Más información](https://help.shopify.com/manual/shopify-admin/productivity-tools/image-editor#understanding-image-aspect-ratio)"}, "image_height": {"label": "Altura del banner", "options__1": {"label": "Adaptar a la imagen"}, "options__2": {"label": "Pequeña"}, "options__3": {"label": "Mediana"}, "options__4": {"label": "Grande"}, "info": "Para mejores resultados, utiliza una imagen con una relación de aspecto 16:9. [Más información](https://help.shopify.com/manual/shopify-admin/productivity-tools/image-editor#understanding-image-aspect-ratio)"}, "desktop_content_position": {"options__1": {"label": "Arriba a la izquierda"}, "options__2": {"label": "Arriba en el centro"}, "options__3": {"label": "Arriba a la derecha"}, "options__4": {"label": "Centrado a la izquierda"}, "options__5": {"label": "Centrado en el medio"}, "options__6": {"label": "Centrado a la derecha"}, "options__7": {"label": "Abajo a la izquierda"}, "options__8": {"label": "Abajo en el centro"}, "options__9": {"label": "Abajo a la derecha"}, "label": "Posición del contenido en la computadora"}, "desktop_content_alignment": {"options__1": {"label": "Iz<PERSON>erda"}, "options__2": {"label": "Centro"}, "options__3": {"label": "Derecha"}, "label": "Alineación del contenido en la computadora"}, "header": {"content": "Diseño para móviles"}, "mobile_content_alignment": {"options__1": {"label": "Iz<PERSON>erda"}, "options__2": {"label": "Centro"}, "options__3": {"label": "Derecha"}, "label": "Alineación del contenido en el móvil"}, "color_scheme": {"info": "Visible cuando se muestre el contenedor."}}, "blocks": {"heading": {"name": "<PERSON><PERSON><PERSON><PERSON>", "settings": {"heading": {"label": "<PERSON><PERSON><PERSON><PERSON>"}}}, "paragraph": {"name": "<PERSON><PERSON><PERSON><PERSON>", "settings": {"paragraph": {"label": "Descripción"}, "text_style": {"options__1": {"label": "<PERSON><PERSON><PERSON>"}, "options__2": {"label": "Subtítulo"}, "label": "Estilo de texto"}}}, "email_form": {"name": "Formulario de correo electrónico"}}, "presets": {"name": "Banner de suscripción de correo electrónico"}}, "slideshow": {"name": "Presentación de diapositivas", "settings": {"layout": {"label": "Diseño", "options__1": {"label": "<PERSON><PERSON> completo"}, "options__2": {"label": "Cuadrícula"}}, "slide_height": {"label": "Altura de diapositiva", "options__1": {"label": "Adaptar a la primera imagen"}, "options__2": {"label": "Pequeña"}, "options__3": {"label": "Mediana"}, "options__4": {"label": "Grande"}}, "slider_visual": {"label": "Estilo de paginación", "options__1": {"label": "<PERSON><PERSON><PERSON>"}, "options__2": {"label": "Punt<PERSON>"}, "options__3": {"label": "Números"}}, "auto_rotate": {"label": "Rotar las diapositivas automáticamente"}, "change_slides_speed": {"label": "Cambiar diapositivas cada"}, "mobile": {"content": "Diseño para móviles"}, "show_text_below": {"label": "Mostrar el contenido debajo de las imágenes en el móvil"}, "accessibility": {"content": "Accesibilidad", "label": "Descripción de la presentación de diapositivas", "info": "Describe la presentación de diapositivas para los clientes utilizando lectores de pantallas."}}, "blocks": {"slide": {"name": "Diapositiva", "settings": {"image": {"label": "Imagen"}, "heading": {"label": "<PERSON><PERSON><PERSON><PERSON>"}, "subheading": {"label": "Subtítulo"}, "button_label": {"label": "Etiqueta de botón", "info": "Deja la etiqueta en blanco para ocultar el botón."}, "link": {"label": "<PERSON>lace de botón"}, "secondary_style": {"label": "Usar estilo de botón con contorno"}, "box_align": {"label": "Posición del contenido en computadoras", "options__1": {"label": "Arriba a la izquierda"}, "options__2": {"label": "Arriba en el centro"}, "options__3": {"label": "Arriba a la derecha"}, "options__4": {"label": "Centrado a la izquierda"}, "options__5": {"label": "Centrado en el medio"}, "options__6": {"label": "Centrado a la derecha"}, "options__7": {"label": "Abajo a la izquierda"}, "options__8": {"label": "Abajo en el centro"}, "options__9": {"label": "Abajo a la derecha"}, "info": "La posición se optimizó automáticamente para dispositivos móviles."}, "show_text_box": {"label": "Mostrar contenedor en computadoras"}, "text_alignment": {"label": "Alineación del contenido en computadoras", "option_1": {"label": "Iz<PERSON>erda"}, "option_2": {"label": "Centrado"}, "option_3": {"label": "Derecha"}}, "image_overlay_opacity": {"label": "Opacidad de la sobreposición de imagen"}, "text_alignment_mobile": {"label": "Alineación del contenido en dispositivos móviles", "options__1": {"label": "Iz<PERSON>erda"}, "options__2": {"label": "Centrado"}, "options__3": {"label": "Derecha"}}}}}, "presets": {"name": "Presentación de diapositivas"}}, "collapsible_content": {"name": "Contenido desplegable", "settings": {"caption": {"label": "Leyenda"}, "heading": {"label": "Encabezado"}, "heading_alignment": {"label": "Alineación del encabezado", "options__1": {"label": "Iz<PERSON>erda"}, "options__2": {"label": "Centrado"}, "options__3": {"label": "Derecha"}}, "layout": {"label": "Diseño", "options__1": {"label": "<PERSON> contenedor"}, "options__2": {"label": "<PERSON>ten<PERSON><PERSON> de <PERSON>la"}, "options__3": {"label": "Contenedor de sección"}}, "container_color_scheme": {"label": "Esquema de color del contenedor", "info": "Visible cuando el diseño está configurado como fila o contenedor de sección."}, "open_first_collapsible_row": {"label": "Abrir primera fila desplegable"}, "header": {"content": "Diseño de la imagen"}, "image": {"label": "Imagen"}, "image_ratio": {"label": "Relación de aspecto de imagen", "options__1": {"label": "Adaptar a la imagen"}, "options__2": {"label": "Pequeña"}, "options__3": {"label": "Grande"}}, "desktop_layout": {"label": "Diseño para computadora", "options__1": {"label": "Imagen primero"}, "options__2": {"label": "Imagen de segundo"}, "info": "La imagen siempre primero en dispositivos móviles."}}, "blocks": {"collapsible_row": {"name": "Fila desplegable", "settings": {"heading": {"info": "Incluye un título que explique el contenido.", "label": "Encabezado"}, "row_content": {"label": "Contenido de fila"}, "page": {"label": "Contenido de fila de la página"}, "icon": {"label": "Ícono", "options__1": {"label": "<PERSON><PERSON><PERSON>"}, "options__2": {"label": "Man<PERSON><PERSON>"}, "options__3": {"label": "<PERSON><PERSON><PERSON><PERSON>"}, "options__4": {"label": "Biberón"}, "options__5": {"label": "Apartado postal"}, "options__6": {"label": "Zanahoria"}, "options__7": {"label": "Globo de chat"}, "options__8": {"label": "Marca de verificación"}, "options__9": {"label": "Portapapeles"}, "options__10": {"label": "Lácteos"}, "options__11": {"label": "Libre de lácteos"}, "options__12": {"label": "Secador"}, "options__13": {"label": "<PERSON><PERSON>"}, "options__14": {"label": "Fuego"}, "options__15": {"label": "Libre de gluten"}, "options__16": {"label": "Corazón"}, "options__17": {"label": "Plancha"}, "options__18": {"label": "Hoja"}, "options__19": {"label": "<PERSON><PERSON><PERSON>"}, "options__20": {"label": "Relámpago"}, "options__21": {"label": "<PERSON><PERSON><PERSON><PERSON> labial"}, "options__22": {"label": "Candado"}, "options__23": {"label": "Alfiler en mapa"}, "options__24": {"label": "Libre de nueces"}, "options__25": {"label": "<PERSON><PERSON><PERSON>"}, "options__26": {"label": "<PERSON><PERSON> de <PERSON>a"}, "options__27": {"label": "Pimienta"}, "options__28": {"label": "Perfume"}, "options__29": {"label": "Avión"}, "options__30": {"label": "Planta"}, "options__31": {"label": "Etiqueta de precio"}, "options__32": {"label": "Signo de interrogación"}, "options__33": {"label": "Reciclar"}, "options__34": {"label": "Devolución"}, "options__35": {"label": "Regla"}, "options__36": {"label": "<PERSON> de <PERSON>rvir"}, "options__37": {"label": "<PERSON><PERSON>"}, "options__38": {"label": "Zapato"}, "options__39": {"label": "<PERSON><PERSON><PERSON>"}, "options__40": {"label": "<PERSON><PERSON> de <PERSON>eve"}, "options__41": {"label": "Estrella"}, "options__42": {"label": "Cronómetro"}, "options__43": {"label": "Camión"}, "options__44": {"label": "<PERSON><PERSON><PERSON>"}}}}}, "presets": {"name": "Contenido desplegable"}}, "main-account": {"name": "C<PERSON><PERSON>"}, "main-activate-account": {"name": "Activación de cuenta"}, "main-addresses": {"name": "Direcciones"}, "main-login": {"name": "Inicio de sesión"}, "main-order": {"name": "Pedido"}, "main-register": {"name": "Registro"}, "main-reset-password": {"name": "Restablecimiento de contraseña"}, "related-products": {"name": "Productos relacionados", "settings": {"heading": {"label": "Encabezado"}, "products_to_show": {"label": "Número máximo de productos para mostrar"}, "columns_desktop": {"label": "Número de columnas en la versión para computadora"}, "paragraph__1": {"content": "Las recomendaciones dinámicas usan la información de pedidos y productos para cambiar y mejorar con el tiempo. [Más información](https://help.shopify.com/themes/development/recommended-products)"}, "header__2": {"content": "Tarjeta de producto"}, "image_ratio": {"label": "Relación de aspecto de imagen", "options__1": {"label": "Adaptar a la imagen"}, "options__2": {"label": "Vertical"}, "options__3": {"label": "Cuadrada"}}, "show_secondary_image": {"label": "Mostrar segunda imagen al pasar el cursor"}, "show_vendor": {"label": "<PERSON><PERSON> proveedor"}, "show_rating": {"label": "Mostrar calificación de productos", "info": "Agrega una aplicación para mostrar las calificaciones de los productos. [Más información](https://help.shopify.com/manual/online-store/themes/theme-structure/page-types#product-recommendations-section-settings)"}, "header_mobile": {"content": "Diseño para móviles"}, "columns_mobile": {"label": "Número de columnas en la versión para móviles", "options__1": {"label": "<PERSON> columna"}, "options__2": {"label": "<PERSON><PERSON> columnas"}}}}, "multirow": {"name": "<PERSON><PERSON><PERSON>", "settings": {"image": {"label": "Imagen"}, "image_height": {"options__1": {"label": "Adaptar a la imagen"}, "options__2": {"label": "Pequeña"}, "options__3": {"label": "Mediana"}, "options__4": {"label": "Grande"}, "label": "Altura de imagen"}, "desktop_image_width": {"options__1": {"label": "Pequeña"}, "options__2": {"label": "Mediana"}, "options__3": {"label": "Grande"}, "label": "<PERSON><PERSON> de la <PERSON>n en la computadora", "info": "La imagen se optimiza automáticamente para el celular."}, "heading_size": {"options__1": {"label": "Pequeña"}, "options__2": {"label": "Mediana"}, "options__3": {"label": "Grande"}, "label": "Tamaño del título"}, "text_style": {"options__1": {"label": "<PERSON><PERSON><PERSON>"}, "options__2": {"label": "Subtítulo"}, "label": "Estilo de texto"}, "button_style": {"options__1": {"label": "Botón sólido"}, "options__2": {"label": "Botón con contorno"}, "label": "Estilo del botón"}, "desktop_content_alignment": {"options__1": {"label": "Iz<PERSON>erda"}, "options__2": {"label": "Centro"}, "options__3": {"label": "Derecha"}, "label": "Alineación del contenido en computadoras"}, "desktop_content_position": {"options__1": {"label": "Arriba"}, "options__2": {"label": "Centrado"}, "options__3": {"label": "Abajo"}, "label": "Posición del contenido en computadoras", "info": "La posición se optimiza automáticamente para dispositivos móviles."}, "image_layout": {"options__1": {"label": "Alternar desde la izquierda"}, "options__2": {"label": "Alternar desde la derecha"}, "options__3": {"label": "Alineada a la izquierda"}, "options__4": {"label": "Alineada a la derecha"}, "label": "Ubicación de la imagen en computadoras", "info": "La colocación se optimiza automáticamente para dispositivos móviles."}, "container_color_scheme": {"label": "Esquema de color del contenedor"}, "mobile_content_alignment": {"options__1": {"label": "Iz<PERSON>erda"}, "options__2": {"label": "Centro"}, "options__3": {"label": "Derecha"}, "label": "Alineación del contenido en el celular"}, "header_mobile": {"content": "Diseño para móviles"}}, "blocks": {"row": {"name": "<PERSON><PERSON>", "settings": {"image": {"label": "Imagen"}, "caption": {"label": "Leyenda"}, "heading": {"label": "Encabezado"}, "text": {"label": "Texto"}, "button_label": {"label": "Etiqueta de botón"}, "button_link": {"label": "<PERSON>lace de botón"}}}}, "presets": {"name": "<PERSON><PERSON><PERSON>"}}, "quick-order-list": {"name": "Lista de pedidos rápidos", "settings": {"show_image": {"label": "<PERSON>rar imágenes"}, "show_sku": {"label": "Mostrar SKU"}}, "presets": {"name": "Lista de pedidos rápidos"}}}}