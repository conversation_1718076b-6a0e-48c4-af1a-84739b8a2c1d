{%- assign icon_id = icon_id | default: 'icon-m2' -%}
{%- assign icon_class = icon_class | default: 'icon icon-m2' -%}
{%- assign icon_width = icon_width | default: nil -%}
{%- assign icon_height = icon_height | default: nil -%}
{%- assign icon_color = icon_color | default: 'currentColor' -%}

<svg xmlns="http://www.w3.org/2000/svg"
     class="{{ icon_class }}"
     {% if icon_width %}width="{{ icon_width }}"{% endif %}
     {% if icon_height %}height="{{ icon_height }}"{% endif %}
     viewBox="0 0 28 28"
     aria-hidden="true"
     focusable="false"
     role="presentation">
  <style>.ascls-1{fill:none;stroke:{{ icon_color }}}</style>
  <g id="Слой_2" data-name="Слой 2">
    <g id="Layer_1" data-name="Layer 1">
      <path d="M13.4 9.9a3.58 3.58 0 0 1 1.9.4 3.18 3.18 0 0 1 1.2 1.1 4.42 4.42 0 0 1 .6 1.6 11 11 0 0 1 .2 1.8v8.9h-3.9V15a1.59 1.59 0 0 0-.4-1.2 1.78 1.78 0 0 0-1-.4 1.28 1.28 0 0 0-1 .4 1.37 1.37 0 0 0-.4 1.1v8.7H6.7v-8.7a1.59 1.59 0 0 0-.4-1.2 1.78 1.78 0 0 0-1-.4 1.28 1.28 0 0 0-1 .4 1.59 1.59 0 0 0-.4 1.2v8.6H0V10h3.9v1.2H4a4 4 0 0 1 1.2-1 2.73 2.73 0 0 1 1.4-.4 4.23 4.23 0 0 1 2 .5 3.25 3.25 0 0 1 1.3 1.3 6.39 6.39 0 0 1 1.5-1.3 7.87 7.87 0 0 1 2-.4z" fill="{{ icon_color }}"/>
      <path class="ascls-1" d="M16.9 4.7a4.78 4.78 0 0 1 .4-1.8 3 3 0 0 1 .9-1.3 5.67 5.67 0 0 1 1.3-.8A4 4 0 0 1 21 .5a4 4 0 0 1 3 1 3.84 3.84 0 0 1 1.1 2.9 7.77 7.77 0 0 1-.2 1.6 10.09 10.09 0 0 1-.7 1.5l-3.4 4.9h4.3v2.8h-8v-2.6l4.7-6.7a1.08 1.08 0 0 0 .3-.7 2 2 0 0 0 .1-.8 1.14 1.14 0 0 0-.3-.8.81.81 0 0 0-.8-.4 1 1 0 0 0-.8.4 1.81 1.81 0 0 0-.4 1.4z"/>
    </g>
  </g>
</svg>