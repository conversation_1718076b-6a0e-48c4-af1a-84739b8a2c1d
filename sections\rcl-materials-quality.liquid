{{ 'rcl-materials-quality.css' | asset_url | stylesheet_tag: preload: true }}

{%- liquid
  assign widths = '375, 550, 750, 1100, 1500, 1780, 2000, 3000'
-%}

<div class="materials-quality-section" 
      style="background-color: {{ section.settings.background_color }}; 
            color: {{ section.settings.text_color }};"
      data-layout="{{ section.settings.layout }}">
  <div class="page-width">
    <div class="section-header text-center">
      <h2 class="section-title" style="color: {{ section.settings.title_color }};">
        {{ section.settings.title }}
      </h2>
      
      <div class="section-description rte">
        {{ section.settings.description }}
      </div>
    </div>

    <div class="materials-quality-container">
      {% for block in section.blocks %}
        {% assign is_odd = forloop.index | modulo: 2 %}
        
        <div class="materials-quality-item {% if section.settings.layout == 'alternating' and is_odd == 0 %}reversed{% endif %}" {{ block.shopify_attributes }}>
          {% case block.type %}
            {% when 'material' %}
              <div class="materials-quality-image-container">
                {% if block.settings.image != blank %}
                  {%- liquid
                    assign image_height = block.settings.image.width | divided_by: block.settings.image.aspect_ratio
                    
                    if block.settings.image_height == 'adapt'
                      # Do nothing, aspect ratio is preserved
                    elsif block.settings.image_height == 'small'
                      assign image_class = 'materials-quality-image--small'
                    elsif block.settings.image_height == 'medium'
                      assign image_class = 'materials-quality-image--medium'
                    elsif block.settings.image_height == 'large'
                      assign image_class = 'materials-quality-image--large'
                    endif
                  -%}
                  
                  <div class="materials-quality-image {{ image_class }}">
                    {{
                      block.settings.image
                      | image_url: width: 2000
                      | image_tag:
                        loading: 'lazy',
                        width: block.settings.image.width,
                        height: image_height,
                        widths: widths,
                        sizes: '(min-width: 750px) 50vw, 100vw'
                    }}
                    
                    {% if block.settings.origin != blank %}
                      <div class="materials-origin" style="background-color: {{ section.settings.accent_color }};">
                        <span>{{ block.settings.origin }}</span>
                      </div>
                    {% endif %}
                  </div>
                  
                  {% if block.settings.image_height == 'adapt' %}
                    <style>
                      @media screen and (max-width: 749px) {
                        #MaterialImage-{{ block.id }}::before {
                          padding-bottom: {{ 1 | divided_by: block.settings.image.aspect_ratio | times: 100 }}%;
                          content: '';
                          display: block;
                        }
                      }
                      
                      @media screen and (min-width: 750px) {
                        #MaterialImage-{{ block.id }}::before {
                          padding-bottom: {{ 1 | divided_by: block.settings.image.aspect_ratio | times: 100 }}%;
                          content: '';
                          display: block;
                        }
                      }
                    </style>
                  {% endif %}
                {% else %}
                  <div class="materials-quality-image placeholder">
                    {{ 'image' | placeholder_svg_tag: 'placeholder-svg' }}
                    
                    {% if block.settings.origin != blank %}
                      <div class="materials-origin" style="background-color: {{ section.settings.accent_color }};">
                        <span>{{ block.settings.origin }}</span>
                      </div>
                    {% endif %}
                  </div>
                {% endif %}
              </div>
              
              <div class="materials-quality-content">
                <div class="materials-quality-content-inner">
                  <h3 class="materials-quality-title">
                    {{ block.settings.title }}
                  </h3>
                  
                  <div class="materials-quality-detail">
                    <span class="detail-accent" style="background-color: {{ section.settings.accent_color }};"></span>
                  </div>
                  
                  <div class="materials-quality-text rte">
                    {{ block.settings.description }}
                  </div>

                  {% if block.settings.link != blank %}
                    <a href="{{ block.settings.link }}" class="rcl-button materials-quality-button" style="background-color: {{ section.settings.button_background_color }}; color: {{ section.settings.button_text_color }};">
                      {% if block.settings.button_text != blank %}
                        {{ block.settings.button_text }}
                      {% else %}
                        Learn More
                      {% endif %}
                    </a>
                  {% endif %}
                </div>
              </div>
            
            
            {% when 'product' %}
              <div class="materials-quality-image-container">
                {% if block.settings.image != blank %}
                  {%- liquid
                    assign image_height = block.settings.image.width | divided_by: block.settings.image.aspect_ratio
                    
                    if block.settings.image_height == 'adapt'
                      # Do nothing, aspect ratio is preserved
                    elsif block.settings.image_height == 'small'
                      assign image_class = 'materials-quality-image--small'
                    elsif block.settings.image_height == 'medium'
                      assign image_class = 'materials-quality-image--medium'
                    elsif block.settings.image_height == 'large'
                      assign image_class = 'materials-quality-image--large'
                    endif
                  -%}
                  
                  <div id="MaterialImage-{{ block.id }}" class="materials-quality-image product-image {{ image_class }}">
                    {{
                      block.settings.image
                      | image_url: width: 2000
                      | image_tag:
                        loading: 'lazy',
                        width: block.settings.image.width,
                        height: image_height,
                        widths: widths,
                        sizes: '(min-width: 750px) 50vw, 100vw'
                    }}
                  </div>
                  
                  {% if block.settings.image_height == 'adapt' %}
                    <style>
                      @media screen and (max-width: 749px) {
                        #MaterialImage-{{ block.id }}::before {
                          padding-bottom: {{ 1 | divided_by: block.settings.image.aspect_ratio | times: 100 }}%;
                          content: '';
                          display: block;
                        }
                      }
                      
                      @media screen and (min-width: 750px) {
                        #MaterialImage-{{ block.id }}::before {
                          padding-bottom: {{ 1 | divided_by: block.settings.image.aspect_ratio | times: 100 }}%;
                          content: '';
                          display: block;
                        }
                      }
                    </style>
                  {% endif %}
                {% else %}
                  <div class="materials-quality-image product-image placeholder">
                    {{ 'image' | placeholder_svg_tag: 'placeholder-svg' }}
                  </div>
                {% endif %}
              </div>
              
              <div class="materials-quality-content">
                <div class="materials-quality-content-inner">
                  <div class="product-badge" style="color: {{ section.settings.accent_color }};">
                    Featured Product
                  </div>
                  
                  <h3 class="materials-quality-title">
                    {{ block.settings.title }}
                  </h3>
                  
                  <div class="materials-quality-detail">
                    <span class="detail-accent" style="background-color: {{ section.settings.accent_color }};"></span>
                  </div>
                  
                  <div class="materials-quality-text rte">
                    {{ block.settings.description }}
                  </div>

                  {% if block.settings.link != blank %}
                    <a href="{{ block.settings.link }}" class="rcl-button materials-quality-button" style="background-color: {{ section.settings.button_background_color }}; color: {{ section.settings.button_text_color }};">
                      {% if block.settings.button_text != blank %}
                        {{ block.settings.button_text }}
                      {% else %}
                        Discover Product
                      {% endif %}
                    </a>
                  {% endif %}
                </div>
              </div>
          {% endcase %}
        </div>
      {% endfor %}
    </div>
  </div>
</div>

<script>
  document.addEventListener('DOMContentLoaded', function() {
    document.documentElement.classList.remove('no-js');
    
    if ('IntersectionObserver' in window) {
      const observer = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
          if (entry.isIntersecting) {
            entry.target.classList.add('animate');
            observer.unobserve(entry.target);
          }
        });
      }, {
        root: null, 
        threshold: 0.1, 
        rootMargin: '0px 0px -100px 0px' 
      });
      
      document.querySelectorAll('.materials-quality-item').forEach(item => {
        observer.observe(item);
      });
    } else {
      document.querySelectorAll('.materials-quality-item').forEach(item => {
        item.classList.add('animate');
      });
    }
  });
</script>

{% schema %}
{
  "name": "Materials & Quality",
  "tag": "section",
  "class": "section",
  "settings": [
    {
      "type": "text",
      "id": "title",
      "label": "Section Title",
      "default": "Premium Materials & Quality"
    },
    {
      "type": "color",
      "id": "title_color",
      "label": "Title Color",
      "default": "#222228"
    },
    {
      "type": "richtext",
      "id": "description",
      "label": "Section Description",
      "default": "<p>All our materials are made from the best, environmentally friendly premium-class raw materials. The main components of decorative coatings are imported to our production base from Italy, Spain, Germany and France.</p>"
    },
    {
      "type": "color",
      "id": "text_color",
      "label": "Text Color",
      "default": "#222228"
    },
    {
      "type": "color",
      "id": "background_color",
      "label": "Background Color",
      "default": "#ffffff"
    },
    {
      "type": "color",
      "id": "accent_color",
      "label": "Accent Color",
      "default": "#d1b073"
    },
    {
      "type": "color",
      "id": "button_background_color",
      "label": "Button Background Color",
      "default": "#d1b073"
    },
    {
      "type": "color",
      "id": "button_text_color",
      "label": "Button Text Color",
      "default": "#ffffff"
    },
    {
      "type": "select",
      "id": "layout",
      "label": "Layout Style",
      "options": [
        {
          "value": "standard",
          "label": "Standard"
        },
        {
          "value": "alternating",
          "label": "Alternating"
        }
      ],
      "default": "standard"
    }
  ],
  "blocks": [
    {
      "type": "material",
      "name": "Material Feature",
      "settings": [
        {
          "type": "image_picker",
          "id": "image",
          "label": "Material Image"
        },
        {
          "type": "select",
          "id": "image_height",
          "options": [
            {
              "value": "adapt",
              "label": "Adapt to image"
            },
            {
              "value": "small",
              "label": "Small"
            },
            {
              "value": "medium",
              "label": "Medium"
            },
            {
              "value": "large",
              "label": "Large"
            }
          ],
          "default": "medium",
          "label": "Image height"
        },
        {
          "type": "text",
          "id": "title",
          "label": "Material Title",
          "default": "Premium Lime Base"
        },
        {
          "type": "richtext",
          "id": "description",
          "label": "Material Description",
          "default": "<p>We are proud to be the first in Ukraine to master the production of decorative coatings based on high-quality lime. The composition includes only natural ingredients following ancient recipes.</p>"
        },
        {
          "type": "text",
          "id": "origin",
          "label": "Origin Country",
          "default": "Italy"
        },
        {
          "type": "text",
          "id": "button_text",
          "label": "Button Text",
          "default": "Learn More"
        },
        {
          "type": "url",
          "id": "link",
          "label": "Link"
        }
      ]
    },
    {
      "type": "product",
      "name": "Featured Product",
      "settings": [
        {
          "type": "image_picker",
          "id": "image",
          "label": "Product Image"
        },
        {
          "type": "select",
          "id": "image_height",
          "options": [
            {
              "value": "adapt",
              "label": "Adapt to image"
            },
            {
              "value": "small",
              "label": "Small"
            },
            {
              "value": "medium",
              "label": "Medium"
            },
            {
              "value": "large",
              "label": "Large"
            }
          ],
          "default": "medium",
          "label": "Image height"
        },
        {
          "type": "text",
          "id": "title",
          "label": "Product Title",
          "default": "TRAVERTINO STYLE"
        },
        {
          "type": "richtext",
          "id": "description",
          "label": "Product Description",
          "default": "<p>A premium decorative coating that recreates the elegant look of natural travertine stone, offering a luxurious finish for walls and ceilings.</p>"
        },
        {
          "type": "url",
          "id": "link",
          "label": "Product Link"
        },
        {
          "type": "text",
          "id": "button_text",
          "label": "Button Text",
          "default": "Discover Product"
        }
      ]
    }
  ],
  "presets": [
    {
      "name": "Materials & Quality",
      "blocks": [
        {
          "type": "material"
        },
        {
          "type": "material"
        },
        {
          "type": "product"
        }
      ]
    }
  ]
}
{% endschema %}
