{% schema %}
    {
      "name": "Product Categories",
      "tag": "section",
      "class": "section",
      "settings": [
        {
          "type": "text",
          "id": "title",
          "label": "Section Title",
          "default": "Our Premium Collections"
        },
        {
          "type": "color",
          "id": "title_color",
          "label": "Title Color",
          "default": "#222228"
        },
        {
          "type": "richtext",
          "id": "description",
          "label": "Section Description",
          "default": "<p>Discover our range of premium decorative coatings made from the finest environmentally friendly materials imported from Italy, Spain, Germany, and France.</p>"
        },
        {
          "type": "color",
          "id": "text_color",
          "label": "Text Color",
          "default": "#222228"
        },
        {
          "type": "color",
          "id": "background_color",
          "label": "Background Color",
          "default": "#f8f8f8"
        },
        {
          "type": "color",
          "id": "accent_color",
          "label": "Accent Color",
          "default": "#d1b073"
        },
        {
          "type": "select",
          "id": "layout",
          "label": "Layout Style",
          "options": [
            {
              "value": "grid",
              "label": "Grid"
            },
            {
              "value": "carousel",
              "label": "Carousel"
            }
          ],
          "default": "grid"
        },
        {
          "type": "range",
          "id": "columns_desktop",
          "min": 1,
          "max": 4,
          "step": 1,
          "default": 3,
          "label": "Columns on Desktop"
        },
        {
          "type": "range",
          "id": "columns_mobile",
          "min": 1,
          "max": 3,
          "step": 1,
          "default": 1,
          "label": "Columns on Mobile"
        }
      ],
      "blocks": [
        {
          "type": "category",
          "name": "Product Category",
          "settings": [
            {
              "type": "image_picker",
              "id": "image",
              "label": "Category Image"
            },
            {
              "type": "text",
              "id": "title",
              "label": "Category Title",
              "default": "GROTTO"
            },
            {
              "type": "richtext",
              "id": "description",
              "label": "Category Description",
              "default": "<p>Premium lime-based decorative coating with natural ingredients for unique textures and finishes.</p>"
            },
            {
              "type": "url",
              "id": "link",
              "label": "Category Link"
            },
            {
              "type": "text",
              "id": "button_text",
              "label": "Button Text",
              "default": "Explore Collection"
            }
          ]
        }
      ],
      "presets": [
        {
          "name": "Product Categories",
          "blocks": [
            {
              "type": "category"
            },
            {
              "type": "category"
            },
            {
              "type": "category"
            }
          ]
        }
      ]
    }
    {% endschema %}
    
    <div class="product-categories-section" 
         style="background-color: {{ section.settings.background_color }}; 
                color: {{ section.settings.text_color }};"
         data-layout="{{ section.settings.layout }}">
      <div class="page-width">
        <div class="section-header text-center">
          <h2 class="section-title" style="color: {{ section.settings.title_color }};">
            {{ section.settings.title }}
          </h2>
          <div class="section-description rte">
            {{ section.settings.description }}
          </div>
        </div>
    
        <div class="product-categories-container"
             data-columns-desktop="{{ section.settings.columns_desktop }}"
             data-columns-mobile="{{ section.settings.columns_mobile }}">
          {% for block in section.blocks %}
            {% if block.type == 'category' %}
              <div class="product-category-item" {{ block.shopify_attributes }}>
                <div class="product-category-card">
                  {% if block.settings.image != blank %}
                    <div class="product-category-image">
                      {{ block.settings.image | image_url: width: 800 | image_tag:
                        loading: 'lazy',
                        widths: '275, 550, 710, 1420',
                        sizes: '(min-width: 750px) 33vw, 100vw'
                      }}
                      <div class="product-category-image-overlay"></div>
                    </div>
                  {% else %}
                    <div class="product-category-image placeholder">
                      {{ 'image' | placeholder_svg_tag: 'placeholder-svg' }}
                      <div class="product-category-image-overlay"></div>
                    </div>
                  {% endif %}
                  
                  <div class="product-category-content">
                    <h3 class="product-category-title">
                      {{ block.settings.title }}
                    </h3>
                    
                    <div class="product-category-description rte">
                      {{ block.settings.description }}
                    </div>
                    
                    {% if block.settings.link != blank and block.settings.button_text != blank %}
                      <a href="{{ block.settings.link }}" class="product-category-button" 
                         style="color: {{ section.settings.background_color }}; 
                                background-color: {{ section.settings.accent_color }};">
                        {{ block.settings.button_text }}
                      </a>
                    {% endif %}
                  </div>
                </div>
              </div>
            {% endif %}
          {% endfor %}
        </div>
        
        {% if section.settings.layout == 'carousel' %}
          <div class="carousel-navigation">
            <button class="carousel-prev" aria-label="Previous slide">
              <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M15 18L9 12L15 6" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
              </svg>
            </button>
            <button class="carousel-next" aria-label="Next slide">
              <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M9 18L15 12L9 6" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
              </svg>
            </button>
          </div>
        {% endif %}
      </div>
    </div>
    
    <style>
      .product-categories-section {
        padding: 70px 0;
      }
      
      .section-description {
        max-width: 800px;
        margin: 0 auto;
        line-height: 1.6;
        margin-bottom: 60px;
      }
      
      /* Grid Layout */
      [data-layout="grid"] .product-categories-container {
        display: grid;
        grid-template-columns: repeat(var(--columns-desktop, 3), 1fr);
        gap: 30px;
      }
      
      /* Carousel Layout */
      [data-layout="carousel"] .product-categories-container {
        display: flex;
        overflow-x: auto;
        scroll-snap-type: x mandatory;
        scroll-behavior: smooth;
        -webkit-overflow-scrolling: touch;
        scrollbar-width: none; /* Firefox */
        -ms-overflow-style: none; /* IE and Edge */
        gap: 30px;
        padding-bottom: 20px;
      }
      
      [data-layout="carousel"] .product-categories-container::-webkit-scrollbar {
        display: none; /* Chrome, Safari, Opera */
      }
      
      [data-layout="carousel"] .product-category-item {
        flex: 0 0 calc((100% / var(--columns-desktop, 3)) - 30px);
        scroll-snap-align: start;
      }
      
      /* Category Item */
      .product-category-card {
        display: flex;
        flex-direction: column;
        height: 100%;
        background-color: #ffffff;
        border-radius: 8px;
        overflow: hidden;
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
        transition: transform 0.3s ease, box-shadow 0.3s ease;
      }
      
      .product-category-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 8px 20px rgba(0, 0, 0, 0.1);
      }
      
      .product-category-image {
        position: relative;
        overflow: hidden;
        padding-bottom: 75%; /* Aspect ratio of 4:3 */
      }
      
      .product-category-image img,
      .product-category-image .placeholder-svg {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        object-fit: cover;
        transition: transform 0.6s ease;
      }
      
      .product-category-card:hover .product-category-image img {
        transform: scale(1.05);
      }
      
      .product-category-image-overlay {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: linear-gradient(to bottom, rgba(0,0,0,0) 50%, rgba(0,0,0,0.3) 100%);
      }
      
      .product-category-content {
        flex-grow: 1;
        padding: 25px;
        display: flex;
        flex-direction: column;
      }
      
      .product-category-title {
        margin: 0 0 15px;
        font-weight: 600;
        letter-spacing: 0.05em;
      }
      
      .product-category-description {
        margin-bottom: 20px;
        line-height: 1.5;
        flex-grow: 1;
      }
      
      .product-category-button {
        display: inline-block;
        padding: 0.7rem 1.5rem;
        text-align: center;
        text-decoration: none;
        border-radius: 4px;
        transition: opacity 0.3s;
        align-self: flex-start;
      }
      
      .product-category-button:hover {
        opacity: 0.9;
      }
      
      /* Carousel Navigation */
      .carousel-navigation {
        display: flex;
        justify-content: center;
        gap: 20px;
        margin-top: 40px;
      }
      
      .carousel-prev,
      .carousel-next {
        background: none;
        border: 2px solid currentColor;
        border-radius: 50%;
        width: 44px;
        height: 44px;
        display: flex;
        align-items: center;
        justify-content: center;
        cursor: pointer;
        transition: background-color 0.3s, transform 0.3s;
      }
      
      .carousel-prev:hover,
      .carousel-next:hover {
        background-color: rgba(0, 0, 0, 0.05);
        transform: scale(1.05);
      }
      
      /* Responsive */
      @media screen and (max-width: 989px) {
        [data-layout="grid"] .product-categories-container {
          grid-template-columns: repeat(2, 1fr);
        }
        
        [data-layout="carousel"] .product-category-item {
          flex: 0 0 calc(50% - 15px);
        }
      }
      
      @media screen and (max-width: 749px) {
        .product-categories-section {
          padding: 50px 0;
        }
        
        [data-layout="grid"] .product-categories-container {
          grid-template-columns: repeat(var(--columns-mobile, 1), 1fr);
        }
        
        [data-layout="carousel"] .product-category-item {
          flex: 0 0 calc((100% / var(--columns-mobile, 1)) - 15px);
        }
      }
    </style>
    
    <script>
      document.addEventListener('DOMContentLoaded', function() {
        if (document.querySelector('[data-layout="carousel"]')) {
          const container = document.querySelector('.product-categories-container');
          const prevButton = document.querySelector('.carousel-prev');
          const nextButton = document.querySelector('.carousel-next');
          
          if (container && prevButton && nextButton) {
            const itemWidth = container.querySelector('.product-category-item').offsetWidth + 30; // Item width + gap
            
            prevButton.addEventListener('click', function() {
              container.scrollBy({ left: -itemWidth, behavior: 'smooth' });
            });
            
            nextButton.addEventListener('click', function() {
              container.scrollBy({ left: itemWidth, behavior: 'smooth' });
            });
          }
        }
      });
    </script>