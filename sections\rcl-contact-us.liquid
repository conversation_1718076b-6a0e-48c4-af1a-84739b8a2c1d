{%- style -%}
  .section-{{ section.id }}-padding {
    {% if section.settings.padding_top == 'small' %}
      padding-top: 2.5rem;
    {% elsif section.settings.padding_top == 'medium' %}
      padding-top: 5rem;
    {% else %}
      padding-top: 7.5rem;
    {% endif %}
    
    {% if section.settings.padding_bottom == 'small' %}
      padding-bottom: 2.5rem;
    {% elsif section.settings.padding_bottom == 'medium' %}
      padding-bottom: 5rem;
    {% else %}
      padding-bottom: 7.5rem;
    {% endif %}
  }
  
  #ContactSection-{{ section.id }} {
    background-color: {{ section.settings.background_color }};
    color: {{ section.settings.text_color }};
  }
  
  #ContactSection-{{ section.id }} .contact-header {
    text-align: center;
    margin-bottom: 3.75rem;
  }
  
  #ContactSection-{{ section.id }} .contact-title {
    font-weight: 600;
    margin-bottom: 0.9375rem;
    color: {{ section.settings.heading_color }};
  }
  
  #ContactSection-{{ section.id }} .contact-divider {
    width: 5rem;
    height: 0.1875rem;
    background-color: {{ section.settings.accent_color }};
    margin: 1.5625rem auto;
  }
  
  #ContactSection-{{ section.id }} .contact-subtitle {
    max-width: 43.75rem;
    margin: 0 auto;
    color: {{ section.settings.text_color }};
  }
  
  #ContactSection-{{ section.id }} .contact-content {
    display: flex;
    flex-wrap: wrap;
    justify-content: space-between;
    gap: 1.875rem;
  }
  
  #ContactSection-{{ section.id }} .contact-info {
    flex: 1;
    min-width: 18.75rem;
  }
  
  #ContactSection-{{ section.id }} .contact-item {
    margin-bottom: 2.1875rem;
  }
  
  #ContactSection-{{ section.id }} .contact-item-header {
    display: flex;
    align-items: center;
    margin-bottom: 0.9375rem;
  }
  
  #ContactSection-{{ section.id }} .contact-icon {
    width: 2.8125rem;
    height: 2.8125rem;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: {{ section.settings.accent_color }};
    color: white;
    border-radius: 50%;
    margin-right: 0.9375rem;
  }
  
  #ContactSection-{{ section.id }} .contact-item-title {
    font-weight: 600;
    color: {{ section.settings.heading_color }};
  }
  
  #ContactSection-{{ section.id }} .contact-item-content {
    padding-left: 3.75rem;
    line-height: 1.6;
  }
  
  #ContactSection-{{ section.id }} .contact-form {
    flex: 1;
    min-width: 18.75rem;
    padding: 2.5rem;
    background-color: #f9f9f9;
    border-radius: 0.5rem;
    box-shadow: 0 0.3125rem 1.25rem rgba(0,0,0,0.05);
  }
  
  #ContactSection-{{ section.id }} .form-group {
    margin-bottom: 1.25rem;
  }
  
  #ContactSection-{{ section.id }} .form-group label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: 500;
    color: {{ section.settings.heading_color }};
  }
  
  #ContactSection-{{ section.id }} .form-control {
    width: 100%;
    padding: 0.75rem 0.9375rem;
    border: 1px solid #e0e0e0;
    border-radius: 0.25rem;
    background-color: white;
    transition: border-color 0.3s;
  }
  
  #ContactSection-{{ section.id }} .form-control:focus {
    outline: none;
    border-color: {{ section.settings.accent_color }};
  }
  
  #ContactSection-{{ section.id }} textarea.form-control {
    min-height: 7.5rem;
    resize: vertical;
  }
  
  #ContactSection-{{ section.id }} .submit-btn {
    display: inline-block;
    padding: 0.875rem 1.875rem;
    background-color: {{ section.settings.accent_color }};
    color: white;
    font-weight: 600;
    border: none;
    border-radius: 0.25rem;
    cursor: pointer;
    transition: background-color 0.3s;
  }
  
  #ContactSection-{{ section.id }} .submit-btn:hover {
    background-color: {{ section.settings.accent_color | color_darken: 10 }};
  }
  
  #ContactSection-{{ section.id }} .social-links {
    display: flex;
    gap: 0.9375rem;
    margin-top: 0.625rem;
  }
  
  #ContactSection-{{ section.id }} .social-link {
    width: 2.375rem;
    height: 2.375rem;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: {{ section.settings.heading_color }};
    color: white;
    border-radius: 50%;
    transition: background-color 0.3s;
  }
  
  #ContactSection-{{ section.id }} .social-link:hover {
    background-color: {{ section.settings.accent_color }};
  }
  
  #ContactSection-{{ section.id }} .form-status-success {
    background-color: #f1f8e9;
    border: 1px solid #c5e1a5;
    border-radius: 0.25rem;
    padding: 0.9375rem;
    margin-bottom: 1.25rem;
  }
  
  #ContactSection-{{ section.id }} .form-status-error {
    background-color: #fbe9e7;
    border: 1px solid #ffccbc;
    border-radius: 0.25rem;
    padding: 0.9375rem;
    margin-bottom: 1.25rem;
  }
  
  @media screen and (max-width: 749px) {
    #ContactSection-{{ section.id }} .contact-form {
      padding: 1.875rem 1.25rem;
    }
    
    #ContactSection-{{ section.id }} .contact-item-content {
      padding-left: 0;
    }
  }
{%- endstyle -%}

<div id="ContactSection-{{ section.id }}" class="contact-section section-{{ section.id }}-padding">
  <div class="page-width">
    <div class="contact-header">
      <h2 class="contact-title section-title">{{ section.settings.title }}</h2>
      <div class="contact-divider"></div>
      <div class="contact-subtitle">{{ section.settings.subtitle }}</div>
    </div>
    
    <div class="contact-content">
      <div class="contact-info">
        {% for block in section.blocks %}
          {% case block.type %}
            {% when 'contact_address' %}
              <div class="contact-item" {{ block.shopify_attributes }}>
                <div class="contact-item-header">
                  <div class="contact-icon">
                    <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                      <path d="M21 10c0 7-9 13-9 13s-9-6-9-13a9 9 0 0 1 18 0z"></path>
                      <circle cx="12" cy="10" r="3"></circle>
                    </svg>
                  </div>
                  <h3 class="contact-item-title">{{ block.settings.title }}</h3>
                </div>
                <div class="contact-item-content">
                  {{ block.settings.address }}
                </div>
              </div>
              
            {% when 'contact_phone' %}
              <div class="contact-item" {{ block.shopify_attributes }}>
                <div class="contact-item-header">
                  <div class="contact-icon">
                    <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                      <path d="M22 16.92v3a2 2 0 0 1-2.18 2 19.79 19.79 0 0 1-8.63-3.07 19.5 19.5 0 0 1-6-6 19.79 19.79 0 0 1-3.07-8.67A2 2 0 0 1 4.11 2h3a2 2 0 0 1 2 1.72 12.84 12.84 0 0 0 .7 2.81 2 2 0 0 1-.45 2.11L8.09 9.91a16 16 0 0 0 6 6l1.27-1.27a2 2 0 0 1 2.11-.45 12.84 12.84 0 0 0 2.81.7A2 2 0 0 1 22 16.92z"></path>
                    </svg>
                  </div>
                  <h3 class="contact-item-title">{{ block.settings.title }}</h3>
                </div>
                <div class="contact-item-content">
                  <p><strong>{{ block.settings.phone_label }}</strong><br>
                  <a href="tel:{{ block.settings.phone | remove: ' ' }}" style="color: {{ section.settings.text_color }}; text-decoration: none;">{{ block.settings.phone }}</a></p>
                </div>
              </div>
              
            {% when 'contact_email' %}
              <div class="contact-item" {{ block.shopify_attributes }}>
                <div class="contact-item-header">
                  <div class="contact-icon">
                    <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                      <path d="M4 4h16c1.1 0 2 .9 2 2v12c0 1.1-.9 2-2 2H4c-1.1 0-2-.9-2-2V6c0-1.1.9-2 2-2z"></path>
                      <polyline points="22,6 12,13 2,6"></polyline>
                    </svg>
                  </div>
                  <h3 class="contact-item-title">{{ block.settings.title }}</h3>
                </div>
                <div class="contact-item-content">
                  <p><a href="mailto:{{ block.settings.email }}" style="color: {{ section.settings.text_color }}; text-decoration: none;">{{ block.settings.email }}</a></p>
                </div>
              </div>
              
            {% when 'follow_us' %}
              {%- if settings.social_facebook_link != blank
                or settings.social_instagram_link != blank
                or settings.social_youtube_link != blank
                or settings.social_tiktok_link != blank
                or settings.social_twitter_link != blank
                or settings.social_pinterest_link != blank
                or settings.social_snapchat_link != blank
                or settings.social_tumblr_link != blank
                or settings.social_vimeo_link != blank
              -%}
                <div class="contact-item" {{ block.shopify_attributes }}>
                  <div class="contact-item-header">
                    <div class="contact-icon">
                      <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                        <circle cx="12" cy="12" r="10"></circle>
                        <line x1="12" y1="8" x2="12" y2="12"></line>
                        <line x1="12" y1="16" x2="12.01" y2="16"></line>
                      </svg>
                    </div>
                    <h3 class="contact-item-title">{{ block.settings.title }}</h3>
                  </div>
                  <div class="contact-item-content">
                    <div class="social-links">
                      {%- render 'social-icons' -%}
                    </div>
                  </div>
                </div>
              {%- endif -%}
          {% endcase %}
        {% endfor %}
      </div>
      
      <div class="contact-form">
        {%- form 'contact', id: 'ContactForm', class: 'contact-form-class' -%}
          {%- if form.posted_successfully? -%}
            <div class="form-status form-status-list form-status-success" role="status">
              <h3 class="contact-item-title">{% render 'icon-success' %} {{ 'templates.contact.form.post_success' | t }}</h3>
            </div>
          {%- elsif form.errors -%}
            <div class="form-status form-status-list form-status-error" role="status">
              <h3 class="contact-item-title">{% render 'icon-error' %} {{ 'templates.contact.form.error_heading' | t }}</h3>
              <ul>
                {%- for field in form.errors -%}
                  <li>
                    <a href="#ContactForm-{{ field }}" class="link">
                      {%- if form.errors.translated_fields[field] contains 'author' -%}
                        {{ 'templates.contact.form.name' | t }}
                      {%- elsif form.errors.translated_fields[field] contains 'body' -%}
                        {{ 'templates.contact.form.message' | t }}
                      {%- else -%}
                        {{ form.errors.translated_fields[field] }}
                      {%- endif -%}
                      {{ form.errors.messages[field] }}
                    </a>
                  </li>
                {%- endfor -%}
              </ul>
            </div>
          {%- endif -%}
          
          <div class="form-group">
            <label for="ContactForm-name">{{ 'templates.contact.form.name' | t }}</label>
            <input type="text" id="ContactForm-name" class="form-control" name="contact[name]" value="{% if form.name %}{{ form.name }}{% endif %}" required>
          </div>
          
          <div class="form-group">
            <label for="ContactForm-email">{{ 'templates.contact.form.email' | t }}</label>
            <input type="email" id="ContactForm-email" class="form-control" name="contact[email]" value="{% if form.email %}{{ form.email }}{% endif %}" required autocorrect="off" autocapitalize="off" autocomplete="email">
          </div>
          
          <div class="form-group">
            <label for="ContactForm-phone">{{ 'templates.contact.form.phone' | t }}</label>
            <input type="tel" id="ContactForm-phone" class="form-control" name="contact[phone]" value="{% if form.phone %}{{ form.phone }}{% endif %}" pattern="[0-9\-]*">
          </div>
          
          {%- comment -%}Adding a hidden field to ensure the store admin email is sent{%- endcomment -%}
          <input type="hidden" name="contact[email_to]" value="{{ shop.email }}">
          
          <div class="form-group">
            <label for="ContactForm-body">{{ 'templates.contact.form.comment' | t }}</label>
            <textarea id="ContactForm-body" class="form-control" name="contact[body]" required>{% if form.body %}{{ form.body }}{% endif %}</textarea>
          </div>
          
          <button type="submit" class="submit-btn">
            {{ 'templates.contact.form.send' | t }}
          </button>
        {%- endform -%}
      </div>
    </div>
  </div>
</div>

{% schema %}
  {
    "name": "Contact Section",
    "settings": [
      {
        "type": "text",
        "id": "title",
        "label": "Heading",
        "default": "Contact Us"
      },
      {
        "type": "richtext",
        "id": "subtitle",
        "label": "Subheading",
        "default": "<p>We're here to help with any questions about our premium decorative coatings. Get in touch with our team today.</p>"
      },
      {
        "type": "color",
        "id": "heading_color",
        "label": "Heading Color",
        "default": "#222228"
      },
      {
        "type": "color",
        "id": "text_color",
        "label": "Text Color",
        "default": "#222228"
      },
      {
        "type": "color",
        "id": "accent_color",
        "label": "Accent Color",
        "default": "#d1b073"
      },
      {
        "type": "color",
        "id": "background_color",
        "label": "Background Color",
        "default": "#ffffff"
      },
      {
        "type": "select",
        "id": "padding_top",
        "label": "Top Padding",
        "options": [
          {
            "value": "small",
            "label": "Small"
          },
          {
            "value": "medium",
            "label": "Medium"
          },
          {
            "value": "large",
            "label": "Large"
          }
        ],
        "default": "medium"
      },
      {
        "type": "select",
        "id": "padding_bottom",
        "label": "Bottom Padding",
        "options": [
          {
            "value": "small",
            "label": "Small"
          },
          {
            "value": "medium",
            "label": "Medium"
          },
          {
            "value": "large",
            "label": "Large"
          }
        ],
        "default": "medium"
      }
    ],
    "blocks": [
      {
        "type": "contact_address",
        "name": "Address",
        "limit": 1,
        "settings": [
          {
            "type": "text",
            "id": "title",
            "label": "Title",
            "default": "Visit Us"
          },
          {
            "type": "richtext",
            "id": "address",
            "label": "Address",
            "default": "<p>17, Highfield Road, Edgbaston,<br> Birmingham, West Midlands,<br> B15 3DU, UNITED KINGDOM</p>"
          }
        ]
      },
      {
        "type": "contact_phone",
        "name": "Phone",
        "limit": 1,
        "settings": [
          {
            "type": "text",
            "id": "title",
            "label": "Title",
            "default": "Call Us"
          },
          {
            "type": "text",
            "id": "phone_label",
            "label": "Phone Label",
            "default": "Wholesale and Retail:"
          },
          {
            "type": "text",
            "id": "phone",
            "label": "Phone Number",
            "default": "+447947736776"
          }
        ]
      },
      {
        "type": "contact_email",
        "name": "Email",
        "limit": 1,
        "settings": [
          {
            "type": "text",
            "id": "title",
            "label": "Title",
            "default": "Email Us"
          },
          {
            "type": "text",
            "id": "email",
            "label": "Email Address",
            "default": "<EMAIL>"
          }
        ]
      },
      {
        "type": "follow_us",
        "name": "Follow Us",
        "limit": 1,
        "settings": [
          {
            "type": "text",
            "id": "title",
            "label": "Title",
            "default": "Follow Us"
          }
        ]
      }
    ],
    "presets": [
      {
        "name": "Contact Section",
        "blocks": [
          {
            "type": "contact_address"
          },
          {
            "type": "contact_phone"
          },
          {
            "type": "contact_email"
          },
          {
            "type": "follow_us"
          }
        ]
      }
    ]
  }
  {% endschema %}