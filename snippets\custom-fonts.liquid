{% comment %}
  Custom Fonts - InputMono and Grtsk Exa
  Upload all font files to your theme's assets folder before using this snippet
{% endcomment %}

<style>
  /* InputMono Font Family */
  @import url('{{ "InputMono-Thin.woff2" | asset_url }}') format('woff2');
  @import url('{{ "InputMono-Thin.woff" | asset_url }}') format('woff');
  @import url('{{ "InputMono-ThinItalic.woff2" | asset_url }}') format('woff2');
  @import url('{{ "InputMono-ThinItalic.woff" | asset_url }}') format('woff');
  @import url('{{ "InputMono-ExtraLight.woff2" | asset_url }}') format('woff2');
  @import url('{{ "InputMono-ExtraLight.woff" | asset_url }}') format('woff');
  @import url('{{ "InputMono-ExtraLightItalic.woff2" | asset_url }}') format('woff2');
  @import url('{{ "InputMono-ExtraLightItalic.woff" | asset_url }}') format('woff');
  @import url('{{ "InputMono-Light.woff2" | asset_url }}') format('woff2');
  @import url('{{ "InputMono-Light.woff" | asset_url }}') format('woff');
  @import url('{{ "InputMono-LightItalic.woff2" | asset_url }}') format('woff2');
  @import url('{{ "InputMono-LightItalic.woff" | asset_url }}') format('woff');
  @import url('{{ "InputMono-Regular.woff2" | asset_url }}') format('woff2');
  @import url('{{ "InputMono-Regular.woff" | asset_url }}') format('woff');
  @import url('{{ "InputMono-Italic.woff2" | asset_url }}') format('woff2');
  @import url('{{ "InputMono-Italic.woff" | asset_url }}') format('woff');
  @import url('{{ "InputMono-MediumItalic.woff2" | asset_url }}') format('woff2');
  @import url('{{ "InputMono-MediumItalic.woff" | asset_url }}') format('woff');
  @import url('{{ "InputMono-Bold.woff2" | asset_url }}') format('woff2');
  @import url('{{ "InputMono-Bold.woff" | asset_url }}') format('woff');
  @import url('{{ "InputMono-BoldItalic.woff2" | asset_url }}') format('woff2');
  @import url('{{ "InputMono-BoldItalic.woff" | asset_url }}') format('woff');
  @import url('{{ "InputMono-Black.woff2" | asset_url }}') format('woff2');
  @import url('{{ "InputMono-Black.woff" | asset_url }}') format('woff');
  @import url('{{ "InputMono-BlackItalic.woff2" | asset_url }}') format('woff2');
  @import url('{{ "InputMono-BlackItalic.woff" | asset_url }}') format('woff');

  /* Grtsk Exa Font Family */
  @import url('{{ "GrtskExa-Thin.woff2" | asset_url }}') format('woff2');
  @import url('{{ "GrtskExa-Thin.woff" | asset_url }}') format('woff');
  @import url('{{ "GrtskExa-ThinItalic.woff2" | asset_url }}') format('woff2');
  @import url('{{ "GrtskExa-ThinItalic.woff" | asset_url }}') format('woff');
  @import url('{{ "GrtskExa-Extralight.woff2" | asset_url }}') format('woff2');
  @import url('{{ "GrtskExa-Extralight.woff" | asset_url }}') format('woff');
  @import url('{{ "GrtskExa-ExtralightItalic.woff2" | asset_url }}') format('woff2');
  @import url('{{ "GrtskExa-ExtralightItalic.woff" | asset_url }}') format('woff');
  @import url('{{ "GrtskExa-Light.woff2" | asset_url }}') format('woff2');
  @import url('{{ "GrtskExa-Light.woff" | asset_url }}') format('woff');
  @import url('{{ "GrtskExa-LightItalic.woff2" | asset_url }}') format('woff2');
  @import url('{{ "GrtskExa-LightItalic.woff" | asset_url }}') format('woff');
  @import url('{{ "GrtskExa-Regular.woff2" | asset_url }}') format('woff2');
  @import url('{{ "GrtskExa-Regular.woff" | asset_url }}') format('woff');
  @import url('{{ "GrtskExa-Italic.woff2" | asset_url }}') format('woff2');
  @import url('{{ "GrtskExa-Italic.woff" | asset_url }}') format('woff');
  @import url('{{ "GrtskExa-MediumItalic.woff2" | asset_url }}') format('woff2');
  @import url('{{ "GrtskExa-MediumItalic.woff" | asset_url }}') format('woff');
  @import url('{{ "GrtskExa-Semibold.woff2" | asset_url }}') format('woff2');
  @import url('{{ "GrtskExa-Semibold.woff" | asset_url }}') format('woff');
  @import url('{{ "GrtskExa-SemiboldItalic.woff2" | asset_url }}') format('woff2');
  @import url('{{ "GrtskExa-SemiboldItalic.woff" | asset_url }}') format('woff');
  @import url('{{ "GrtskExa-Bold.woff2" | asset_url }}') format('woff2');
  @import url('{{ "GrtskExa-Bold.woff" | asset_url }}') format('woff');
  @import url('{{ "GrtskExa-BoldItalic.woff2" | asset_url }}') format('woff2');
  @import url('{{ "GrtskExa-BoldItalic.woff" | asset_url }}') format('woff');
  
  /* -------------------- */
  /* InputMono Font Family */
  /* -------------------- */
  
  /* Thin (100) */
  @font-face {
    font-family: 'InputMono';
    src: url('{{ "InputMono-Thin.woff2" | asset_url }}') format('woff2'),
         url('{{ "InputMono-Thin.woff" | asset_url }}') format('woff');
    font-weight: 100;
    font-style: normal;
    font-display: swap;
  }
  
  @font-face {
    font-family: 'InputMono';
    src: url('{{ "InputMono-ThinItalic.woff2" | asset_url }}') format('woff2'),
         url('{{ "InputMono-ThinItalic.woff" | asset_url }}') format('woff');
    font-weight: 100;
    font-style: italic;
    font-display: swap;
  }
  
  /* ExtraLight (200) */
  @font-face {
    font-family: 'InputMono';
    src: url('{{ "InputMono-ExtraLight.woff2" | asset_url }}') format('woff2'),
         url('{{ "InputMono-ExtraLight.woff" | asset_url }}') format('woff');
    font-weight: 200;
    font-style: normal;
    font-display: swap;
  }
  
  @font-face {
    font-family: 'InputMono';
    src: url('{{ "InputMono-ExtraLightItalic.woff2" | asset_url }}') format('woff2'),
         url('{{ "InputMono-ExtraLightItalic.woff" | asset_url }}') format('woff');
    font-weight: 200;
    font-style: italic;
    font-display: swap;
  }
  
  /* Light (300) */
  @font-face {
    font-family: 'InputMono';
    src: url('{{ "InputMono-Light.woff2" | asset_url }}') format('woff2'),
         url('{{ "InputMono-Light.woff" | asset_url }}') format('woff');
    font-weight: 300;
    font-style: normal;
    font-display: swap;
  }
  
  @font-face {
    font-family: 'InputMono';
    src: url('{{ "InputMono-LightItalic.woff2" | asset_url }}') format('woff2'),
         url('{{ "InputMono-LightItalic.woff" | asset_url }}') format('woff');
    font-weight: 300;
    font-style: italic;
    font-display: swap;
  }
  
  /* Regular (400) */
  @font-face {
    font-family: 'InputMono';
    src: url('{{ "InputMono-Regular.woff2" | asset_url }}') format('woff2'),
         url('{{ "InputMono-Regular.woff" | asset_url }}') format('woff');
    font-weight: normal;
    font-style: normal;
    font-display: swap;
  }
  
  @font-face {
    font-family: 'InputMono';
    src: url('{{ "InputMono-Italic.woff2" | asset_url }}') format('woff2'),
         url('{{ "InputMono-Italic.woff" | asset_url }}') format('woff');
    font-weight: normal;
    font-style: italic;
    font-display: swap;
  }
  
  /* Medium (500) */
  @font-face {
    font-family: 'InputMono';
    src: url('{{ "InputMono-Medium.woff2" | asset_url }}') format('woff2'),
         url('{{ "InputMono-Medium.woff" | asset_url }}') format('woff');
    font-weight: 500;
    font-style: normal;
    font-display: swap;
  }
  
  @font-face {
    font-family: 'InputMono';
    src: url('{{ "InputMono-MediumItalic.woff2" | asset_url }}') format('woff2'),
         url('{{ "InputMono-MediumItalic.woff" | asset_url }}') format('woff');
    font-weight: 500;
    font-style: italic;
    font-display: swap;
  }
  
  /* Bold (700) */
  @font-face {
    font-family: 'InputMono';
    src: url('{{ "InputMono-Bold.woff2" | asset_url }}') format('woff2'),
         url('{{ "InputMono-Bold.woff" | asset_url }}') format('woff');
    font-weight: bold;
    font-style: normal;
    font-display: swap;
  }
  
  @font-face {
    font-family: 'InputMono';
    src: url('{{ "InputMono-BoldItalic.woff2" | asset_url }}') format('woff2'),
         url('{{ "InputMono-BoldItalic.woff" | asset_url }}') format('woff');
    font-weight: bold;
    font-style: italic;
    font-display: swap;
  }
  
  /* Black (900) */
  @font-face {
    font-family: 'InputMono';
    src: url('{{ "InputMono-Black.woff2" | asset_url }}') format('woff2'),
         url('{{ "InputMono-Black.woff" | asset_url }}') format('woff');
    font-weight: 900;
    font-style: normal;
    font-display: swap;
  }
  
  @font-face {
    font-family: 'InputMono';
    src: url('{{ "InputMono-BlackItalic.woff2" | asset_url }}') format('woff2'),
         url('{{ "InputMono-BlackItalic.woff" | asset_url }}') format('woff');
    font-weight: 900;
    font-style: italic;
    font-display: swap;
  }
  
  /* -------------------- */
  /* Grtsk Exa Font Family */
  /* -------------------- */
  
  /* Thin (100) */
  @font-face {
    font-family: 'Grtsk Exa';
    src: url('{{ "GrtskExa-Thin.woff2" | asset_url }}') format('woff2'),
         url('{{ "GrtskExa-Thin.woff" | asset_url }}') format('woff');
    font-weight: 100;
    font-style: normal;
    font-display: swap;
  }
  
  @font-face {
    font-family: 'Grtsk Exa';
    src: url('{{ "GrtskExa-ThinItalic.woff2" | asset_url }}') format('woff2'),
         url('{{ "GrtskExa-ThinItalic.woff" | asset_url }}') format('woff');
    font-weight: 100;
    font-style: italic;
    font-display: swap;
  }
  
  /* ExtraLight (200) */
  @font-face {
    font-family: 'Grtsk Exa';
    src: url('{{ "GrtskExa-Extralight.woff2" | asset_url }}') format('woff2'),
         url('{{ "GrtskExa-Extralight.woff" | asset_url }}') format('woff');
    font-weight: 200;
    font-style: normal;
    font-display: swap;
  }
  
  @font-face {
    font-family: 'Grtsk Exa';
    src: url('{{ "GrtskExa-ExtralightItalic.woff2" | asset_url }}') format('woff2'),
         url('{{ "GrtskExa-ExtralightItalic.woff" | asset_url }}') format('woff');
    font-weight: 200;
    font-style: italic;
    font-display: swap;
  }
  
  /* Light (300) */
  @font-face {
    font-family: 'Grtsk Exa';
    src: url('{{ "GrtskExa-Light.woff2" | asset_url }}') format('woff2'),
         url('{{ "GrtskExa-Light.woff" | asset_url }}') format('woff');
    font-weight: 300;
    font-style: normal;
    font-display: swap;
  }
  
  @font-face {
    font-family: 'Grtsk Exa';
    src: url('{{ "GrtskExa-LightItalic.woff2" | asset_url }}') format('woff2'),
         url('{{ "GrtskExa-LightItalic.woff" | asset_url }}') format('woff');
    font-weight: 300;
    font-style: italic;
    font-display: swap;
  }
  
  /* Regular (400) */
  @font-face {
    font-family: 'Grtsk Exa';
    src: url('{{ "GrtskExa-Regular.woff2" | asset_url }}') format('woff2'),
         url('{{ "GrtskExa-Regular.woff" | asset_url }}') format('woff');
    font-weight: normal;
    font-style: normal;
    font-display: swap;
  }
  
  @font-face {
    font-family: 'Grtsk Exa';
    src: url('{{ "GrtskExa-Italic.woff2" | asset_url }}') format('woff2'),
         url('{{ "GrtskExa-Italic.woff" | asset_url }}') format('woff');
    font-weight: normal;
    font-style: italic;
    font-display: swap;
  }

  /* Regular (500) */
    @font-face {
    font-family: 'Grtsk Exa';
    src: url('{{ "GrtskExa-Regular.woff2" | asset_url }}') format('woff2'),
         url('{{ "GrtskExa-Regular.woff" | asset_url }}') format('woff');
    font-weight: 500;
    font-style: normal;
    font-display: swap;
  }
  
  @font-face {
    font-family: 'Grtsk Exa';
    src: url('{{ "GrtskExa-MediumItalic.woff2" | asset_url }}') format('woff2'),
         url('{{ "GrtskExa-MediumItalic.woff" | asset_url }}') format('woff');
    font-weight: 500;
    font-style: italic;
    font-display: swap;
  }
  
  /* Semibold (600) */
  @font-face {
    font-family: 'Grtsk Exa';
    src: url('{{ "GrtskExa-Semibold.woff2" | asset_url }}') format('woff2'),
         url('{{ "GrtskExa-Semibold.woff" | asset_url }}') format('woff');
    font-weight: 600;
    font-style: normal;
    font-display: swap;
  }
  
  @font-face {
    font-family: 'Grtsk Exa';
    src: url('{{ "GrtskExa-SemiboldItalic.woff2" | asset_url }}') format('woff2'),
         url('{{ "GrtskExa-SemiboldItalic.woff" | asset_url }}') format('woff');
    font-weight: 600;
    font-style: italic;
    font-display: swap;
  }
  
  /* Bold (700) */
  @font-face {
    font-family: 'Grtsk Exa';
    src: url('{{ "GrtskExa-Bold.woff2" | asset_url }}') format('woff2'),
         url('{{ "GrtskExa-Bold.woff" | asset_url }}') format('woff');
    font-weight: bold;
    font-style: normal;
    font-display: swap;
  }
  
  @font-face {
    font-family: 'Grtsk Exa';
    src: url('{{ "GrtskExa-BoldItalic.woff2" | asset_url }}') format('woff2'),
         url('{{ "GrtskExa-BoldItalic.woff" | asset_url }}') format('woff');
    font-weight: bold;
    font-style: italic;
    font-display: swap;
  }
</style>