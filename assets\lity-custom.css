.lity {
  z-index: 9990;
  position: fixed;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  white-space: nowrap;
  background: rgba(0, 0, 0, 0.9);
  outline: none !important;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.lity.lity-opened {
  opacity: 1;
}

.lity.lity-closed {
  opacity: 0;
}

.lity * {
  box-sizing: border-box;
}

.lity-wrap {
  z-index: 9990;
  position: fixed;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  text-align: center;
  outline: none !important;
  display: flex;
  align-items: center;
  justify-content: center;
}

.lity-loader {
  z-index: 9991;
  color: #fff;
  position: absolute;
  top: 50%;
  margin-top: -0.8em;
  width: 100%;
  text-align: center;
  font-size: 14px;
  font-family: Arial, Helvetica, sans-serif;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.lity-loading .lity-loader {
  opacity: 1;
}

.lity-container {
  z-index: 9992;
  position: relative;
  text-align: left;
  vertical-align: middle;
  display: inline-block;
  white-space: normal;
  max-width: 90% !important;
  max-height: 90vh !important;
  outline: none !important;
  margin: 0 auto;
}

.lity-content {
  z-index: 9993;
  width: 100%;
  transform: scale(1);
  transition: transform 0.3s ease;
  margin: 0 auto;
}

.lity-loading .lity-content,
.lity-closed .lity-content {
  transform: scale(0.8);
}

.lity-content:after {
  content: '';
  position: absolute;
  left: 0;
  top: 0;
  bottom: 0;
  display: block;
  right: 0;
  width: auto;
  height: auto;
  z-index: -1;
  box-shadow: 0 0 8px rgba(0, 0, 0, 0.6);
}

.lity-close {
  z-index: 9994;
  width: 35px;
  height: 35px;
  position: fixed;
  right: 10px;
  top: 10px;
  -webkit-appearance: none;
  cursor: pointer;
  text-decoration: none;
  text-align: center;
  padding: 0;
  color: #fff;
  font-style: normal;
  font-size: 35px;
  font-family: Arial, Baskerville, monospace;
  line-height: 35px;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.6);
  border: 0;
  background: none;
  outline: none;
  box-shadow: none;
}

.lity-close:hover,
.lity-close:focus,
.lity-close:active,
.lity-close:visited {
  text-decoration: none;
  text-align: center;
  padding: 0;
  color: #fff;
  font-style: normal;
  font-size: 35px;
  font-family: Arial, Baskerville, monospace;
  line-height: 35px;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.6);
  border: 0;
  background: none;
  outline: none;
  box-shadow: none;
}

.lity-image img {
  max-width: 100%;
  display: block;
  line-height: 0;
  border: 0;
}

.lity-iframe .lity-container,
.lity-youtube .lity-container,
.lity-vimeo .lity-container,
.lity-facebookvideo .lity-container,
.lity-googlemaps .lity-container {
  width: 100%;
  max-width: 964px;
}

.lity-iframe-container {
  width: 100%;
  height: 0;
  padding-top: 56.25%;
  overflow: auto;
  pointer-events: auto;
  transform: translateZ(0);
  -webkit-overflow-scrolling: touch;
}

.lity-iframe-container iframe {
  position: absolute;
  display: block;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  box-shadow: 0 0 8px rgba(0, 0, 0, 0.6);
  background: #000;
}

.lity-hide {
  display: none;
}

@media screen and (max-width: 767px) {
  .lity-container {
    width: 90% !important;
    max-width: 90% !important;
  }

  .lity-content {
    width: 100% !important;
  }
}
