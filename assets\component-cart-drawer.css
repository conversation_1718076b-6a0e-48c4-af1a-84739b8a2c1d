.drawer {
  position: fixed;
  z-index: 1000;
  left: 0;
  top: 0;
  width: 100vw;
  height: 100%;
  display: flex;
  justify-content: flex-end;
  background-color: rgba(var(--color-foreground), 0.5);
  transition: visibility var(--duration-default) ease;
}

/* Fix for product pages where the drawer has extra space at the top */
.template-product .drawer {
  top: -30px;
  height: calc(100% + 30px);
}

.drawer.active {
  visibility: visible;
}

.drawer__inner {
  height: 100%;
  width: 40rem;
  max-width: calc(100vw - 3rem);
  padding: 0 1.5rem;
  border: 0.1rem solid rgba(var(--color-foreground), 0.2);
  border-right: 0;
  border-top: 0;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  transform: translateX(100%);
  transition: transform var(--duration-default) ease;
}

.drawer__inner-empty {
  height: 100%;
  padding: 0 1.5rem;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.cart-drawer__warnings {
  display: flex;
  flex-direction: column;
  flex: 1;
  justify-content: center;
}

cart-drawer.is-empty .drawer__inner {
  display: grid;
  grid-template-rows: 1fr;
  align-items: center;
  padding: 0;
}

cart-drawer.is-empty .drawer__header {
  display: none;
}

cart-drawer:not(.is-empty) .cart-drawer__warnings,
cart-drawer:not(.is-empty) .cart-drawer__collection {
  display: none;
}

.cart-drawer__warnings--has-collection .cart__login-title {
  margin-top: 2.5rem;
}

.drawer.active .drawer__inner {
  transform: translateX(0);
}

.drawer__header {
  position: relative;
  padding: 1.5rem 0;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.drawer__heading {
  margin: 0 0 1rem;
}

.drawer__close {
  display: inline-block;
  padding: 0;
  min-width: 4.4rem;
  min-height: 4.4rem;
  box-shadow: 0 0 0 0.2rem rgba(var(--color-button), 0);
  position: absolute;
  top: 10px;
  right: -10px;
  color: rgb(var(--color-foreground));
  background-color: transparent;
  border: none;
  cursor: pointer;
}

.cart-drawer__warnings .drawer__close {
  right: 5px;
}

.drawer__close svg {
  height: 2.4rem;
  width: 2.4rem;
}

.drawer__contents {
  flex-grow: 1;
  display: flex;
  flex-direction: column;
}

.drawer__footer {
  border-top: 0.1rem solid rgba(var(--color-foreground), 0.2);
  padding: 1.5rem 0;
}

cart-drawer-items.is-empty + .drawer__footer {
  display: none;
}

.drawer__footer > details {
  margin-top: -1.5rem;
  border-bottom: 0.1rem solid rgba(var(--color-foreground), 0.2);
}

.drawer__footer > details[open] {
  padding-bottom: 1.5rem;
}

.drawer__footer summary {
  display: flex;
  position: relative;
  line-height: 1;
  padding: 1.5rem 0;
}

.drawer__footer > details + .cart-drawer__footer {
  padding-top: 1.5rem;
}

cart-drawer {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100%;
}

/* Fix for product pages where the cart-drawer has extra space at the top */
.template-product cart-drawer {
  top: -30px;
  height: calc(100% + 30px);
}

.cart-drawer__overlay {
  position: fixed;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  backdrop-filter: blur(4px);
  -webkit-backdrop-filter: blur(4px);
}

/* Fix for product pages where the overlay has extra space at the top */
.template-product .cart-drawer__overlay {
  top: -30px;
  height: calc(100% + 30px);
}

.cart-drawer__overlay:empty {
  display: block;
}

.cart-drawer__form {
  flex-grow: 1;
  display: flex;
  flex-wrap: wrap;
}

.cart-drawer__collection {
  margin: 0 2.5rem 1.5rem;
}

.cart-drawer .drawer__cart-items-wrapper {
  flex-grow: 1;
}

.cart-drawer .cart-items,
.cart-drawer tbody {
  display: block;
  width: 100%;
}

.cart-drawer thead {
  display: inline-table;
  width: 100%;
  position: sticky;
  top: 0;
  z-index: 2;
}

cart-drawer-items {
  overflow: auto;
  flex: 1;
}

@media screen and (max-height: 650px) {
  cart-drawer-items {
    overflow: visible;
  }

  .drawer__inner {
    overflow: scroll;
  }
}

.cart-drawer .cart-item {
  display: grid;
  grid-template: repeat(2, auto) / repeat(4, 1fr);
  gap: 1.5rem;
  margin-bottom: 0;
}

.cart-drawer .cart-item:last-child {
  margin-bottom: 1rem;
}

.cart-drawer .cart-item__media {
  grid-row: 1 / 3;
}

.cart-drawer .cart-item__image {
  max-width: 100%;
}

.cart-drawer .cart-items thead {
  margin-bottom: 0.5rem;
}

.cart-drawer .cart-items thead th:first-child,
.cart-drawer .cart-items thead th:last-child {
  width: 0;
  padding: 0;
}

.cart-drawer .cart-items thead th:nth-child(2) {
  width: 50%;
  padding-left: 0;
}

.cart-drawer .cart-items thead tr {
  display: table-row;
  margin-bottom: 0;
}

.cart-drawer .cart-items th {
  border-bottom: 0.1rem solid rgba(var(--color-foreground), 0.08);
}

.cart-drawer .cart-item:last-child {
  margin-bottom: 1.5rem;
}

.cart-drawer .cart-item .loading__spinner {
  right: 5px;
  padding-top: 2.5rem;
}

.cart-drawer .cart-items td {
  padding-top: 2rem;
}

.cart-drawer .cart-item > td + td {
  padding-left: 1rem;
}

.cart-drawer .cart-item__details {
  width: auto;
  grid-column: 2 / 4;
}

.cart-drawer .cart-item__totals {
  pointer-events: none;
  display: flex;
  align-items: flex-start;
  justify-content: flex-end;
}

.cart-drawer.cart-drawer .cart-item__price-wrapper > *:only-child {
  margin-top: 0;
}

.cart-drawer .cart-item__price-wrapper .cart-item__discounted-prices {
  display: flex;
  flex-direction: column;
  gap: 0.6rem;
}

.cart-drawer .unit-price {
  margin-top: 0.6rem;
}

.cart-drawer .cart-items .cart-item__quantity {
  padding-top: 0;
  grid-column: 2 / 5;
}

@media screen and (max-width: 749px) {
  .cart-drawer .cart-item cart-remove-button {
    margin-left: 0;
  }
}

.cart-drawer__footer > * + * {
  margin-top: 1rem;
}

.cart-drawer .totals {
  justify-content: space-between;
}

.cart-drawer .price {
  line-height: 1;
}

.cart-drawer .tax-note {
  margin: 1.2rem 0 1rem auto;
  text-align: left;
}

.cart-drawer .product-option dd {
  word-break: break-word;
}

.cart-drawer details[open] > summary .icon-caret {
  transform: rotate(180deg);
}

.cart-drawer .cart__checkout-button {
  max-width: none;
}

.drawer__footer .cart__dynamic-checkout-buttons {
  max-width: 100%;
}

.drawer__footer #dynamic-checkout-cart ul {
  flex-wrap: wrap !important;
  flex-direction: row !important;
  margin: 0.5rem -0.5rem 0 0 !important;
  gap: 0.5rem;
}

.drawer__footer [data-shopify-buttoncontainer] {
  justify-content: flex-start;
}

.drawer__footer #dynamic-checkout-cart ul > li {
  flex-basis: calc(50% - 0.5rem) !important;
  margin: 0 !important;
}

.drawer__footer #dynamic-checkout-cart ul > li:only-child {
  flex-basis: 100% !important;
  margin-right: 0.5rem !important;
}

@media screen and (min-width: 750px) {
  .drawer__footer #dynamic-checkout-cart ul > li {
    flex-basis: calc(100% / 3 - 0.5rem) !important;
    margin: 0 !important;
  }

  .drawer__footer #dynamic-checkout-cart ul > li:first-child:nth-last-child(2),
  .drawer__footer #dynamic-checkout-cart ul > li:first-child:nth-last-child(2) ~ li,
  .drawer__footer #dynamic-checkout-cart ul > li:first-child:nth-last-child(4),
  .drawer__footer #dynamic-checkout-cart ul > li:first-child:nth-last-child(4) ~ li {
    flex-basis: calc(50% - 0.5rem) !important;
  }
}

cart-drawer-items::-webkit-scrollbar {
  width: 3px;
}

cart-drawer-items::-webkit-scrollbar-thumb {
  background-color: rgba(var(--color-foreground), 0.7);
  border-radius: 100px;
}

cart-drawer-items::-webkit-scrollbar-track-piece {
  margin-top: 31px;
}

.cart-drawer .quantity-popover-container {
  padding: 0;
}

.cart-drawer .quantity-popover__info.global-settings-popup {
  transform: translateY(0);
  right: 0;
}

.cart-drawer .cart-item__error {
  margin-top: 0.8rem;
}

.cart-drawer .quantity-popover__info + .cart-item__error {
  margin-top: 0.2rem;
}

@media screen and (min-width: 750px) {
  .cart-drawer .cart-item__quantity--info quantity-popover > * {
    padding-left: 0;
  }

  .cart-drawer .cart-item__error {
    margin-left: 0;
  }
}
