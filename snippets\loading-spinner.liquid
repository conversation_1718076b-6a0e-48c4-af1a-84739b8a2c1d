{% comment %}
  Renders loading-spinner.
  Accepts:
    - class: {string} css classes to replace the default ones (optional)

  Usage:
  {% render 'loading-spinner' %}
{% endcomment %}

{{ 'component-loading-spinner.css' | asset_url | stylesheet_tag }}

<div class="{% if class %}{{ class }}{% else %}loading__spinner hidden{% endif %}">
  <svg
    aria-hidden="true"
    focusable="false"
    class="spinner"
    viewBox="0 0 66 66"
    xmlns="http://www.w3.org/2000/svg"
  >
    <circle class="path" fill="none" stroke-width="6" cx="33" cy="33" r="30"></circle>
  </svg>
</div>
