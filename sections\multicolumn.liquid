{{ 'section-multicolumn.css' | asset_url | stylesheet_tag }}
{{ 'component-slider.css' | asset_url | stylesheet_tag }}

{%- style -%}
  .section-{{ section.id }}-padding {
    padding-top: {{ section.settings.padding_top | times: 0.75 | round: 0 }}px;
    padding-bottom: {{ section.settings.padding_bottom | times: 0.75 | round: 0 }}px;
  }

  @media screen and (min-width: 750px) {
    .section-{{ section.id }}-padding {
      padding-top: {{ section.settings.padding_top }}px;
      padding-bottom: {{ section.settings.padding_bottom }}px;
    }
  }
{%- endstyle -%}

{%- liquid
  assign columns_mobile_int = section.settings.columns_mobile | plus: 0
  assign show_mobile_slider = false
  if section.settings.swipe_on_mobile and section.blocks.size > columns_mobile_int
    assign show_mobile_slider = true
  endif
-%}

<div class="multicolumn color-{{ section.settings.color_scheme }} gradient{% unless section.settings.background_style == 'none' and settings.text_boxes_border_thickness > 0 or settings.text_boxes_shadow_opacity > 0 %} background-{{ section.settings.background_style }}{% endunless %}{% if section.settings.title == blank %} no-heading{% endif %}">
  <div
    class="page-width section-{{ section.id }}-padding isolate{% if settings.animations_reveal_on_scroll %} scroll-trigger animate--slide-in{% endif %}"
    {% if settings.animations_reveal_on_scroll %}
      data-cascade
    {% endif %}
  >
    {%- unless section.settings.title == blank -%}
      <div class="title-wrapper-with-link title-wrapper--self-padded-mobile title-wrapper--no-top-margin">
        <h2 class="title inline-richtext {{ section.settings.heading_size }}">
          {{ section.settings.title }}
        </h2>
        {%- if section.settings.button_label != blank and show_mobile_slider -%}
          <a href="{{ section.settings.button_link }}" class="link underlined-link large-up-hide">
            {{- section.settings.button_label | escape -}}
          </a>
        {%- endif -%}
      </div>
    {%- endunless -%}
    <slider-component class="slider-mobile-gutter">
      <ul
        class="multicolumn-list contains-content-container grid grid--{{ section.settings.columns_mobile }}-col-tablet-down grid--{{ section.settings.columns_desktop }}-col-desktop{% if show_mobile_slider %} slider slider--tablet grid--peek{% endif %}"
        id="Slider-{{ section.id }}"
        role="list"
      >
        {%- liquid
          assign highest_ratio = 0
          for block in section.blocks
            if block.settings.image.aspect_ratio > highest_ratio
              assign highest_ratio = block.settings.image.aspect_ratio
            endif
          endfor
        -%}
        {%- for block in section.blocks -%}
          {%- assign empty_column = '' -%}
          {%- if block.settings.image == blank
            and block.settings.title == blank
            and block.settings.text == blank
            and block.settings.link_label == blank
          -%}
            {%- assign empty_column = ' multicolumn-list__item--empty' -%}
          {%- endif -%}

          <li
            id="Slide-{{ section.id }}-{{ forloop.index }}"
            class="multicolumn-list__item grid__item{% if section.settings.swipe_on_mobile %} slider__slide{% endif %}{% if section.settings.column_alignment == 'center' %} center{% endif %}{{ empty_column }}{% if settings.animations_reveal_on_scroll %} scroll-trigger animate--slide-in{% endif %}"
            {{ block.shopify_attributes }}
            {% if settings.animations_reveal_on_scroll %}
              data-cascade
              style="--animation-order: {{ forloop.index }};"
            {% endif %}
          >
            <div class="multicolumn-card content-container">
              {%- if block.settings.image != blank -%}
                {% if section.settings.image_ratio == 'adapt' or section.settings.image_ratio == 'circle' %}
                  {% assign spaced_image = true %}
                {% endif %}
                <div class="multicolumn-card__image-wrapper multicolumn-card__image-wrapper--{{ section.settings.image_width }}-width{% if section.settings.image_width != 'full' or spaced_image %} multicolumn-card-spacing{% endif %}">
                  <div
                    class="media media--transparent media--{{ section.settings.image_ratio }}"
                    {% if section.settings.image_ratio == 'adapt' %}
                      style="padding-bottom: {{ 1 | divided_by: highest_ratio | times: 100 }}%;"
                    {% endif %}
                  >
                    {%- liquid
                      assign number_of_columns = section.settings.columns_desktop
                      assign number_of_columns_mobile = section.settings.columns_mobile
                      assign grid_space_desktop = number_of_columns | minus: 1 | times: settings.spacing_grid_horizontal | plus: 100 | append: 'px'
                      assign grid_space_tablet = number_of_columns_mobile | minus: 1 | times: settings.spacing_grid_horizontal | plus: 100 | append: 'px'
                      assign grid_space_mobile = number_of_columns_mobile | minus: 1 | times: settings.spacing_grid_horizontal | divided_by: 2 | plus: 30 | append: 'px'
                      if section.settings.image_width == 'half'
                        assign image_width = 0.5
                      elsif section.settings.image_width == 'third'
                        assign image_width = 0.33
                      else
                        assign image_width = 1
                      endif
                    -%}
                    {% capture sizes %}
                      (min-width: {{ settings.page_width }}px) calc(({{ settings.page_width }}px - {{ grid_space_desktop }}) * {{ image_width }} /  {{ number_of_columns }}),
                      (min-width: 990px) calc((100vw - {{ grid_space_desktop }}) * {{ image_width }} / {{ number_of_columns }}),
                      (min-width: 750px) calc((100vw - {{ grid_space_tablet }}) * {{ image_width }} / {{ number_of_columns_mobile }}),
                      calc((100vw - {{ grid_space_mobile }}) * {{ image_width }} / {{ number_of_columns_mobile }})
                    {% endcapture %}
                    {{
                      block.settings.image
                      | image_url: width: 3200
                      | image_tag:
                        widths: '50, 75, 100, 150, 200, 300, 400, 500, 750, 1000, 1250, 1500, 1750, 2000, 2250, 2500, 2750, 3000, 3200',
                        sizes: sizes,
                        class: 'multicolumn-card__image'
                    }}
                  </div>
                </div>
              {%- endif -%}
              <div class="multicolumn-card__info">
                {%- if block.settings.title != blank -%}
                  <h3 class="inline-richtext">{{ block.settings.title }}</h3>
                {%- endif -%}
                {%- if block.settings.text != blank -%}
                  <div class="rte">{{ block.settings.text }}</div>
                {%- endif -%}
                {%- if block.settings.link_label != blank -%}
                  <a
                    class="link animate-arrow"
                    {% if block.settings.link == blank %}
                      role="link" aria-disabled="true"
                    {% else %}
                      href="{{ block.settings.link }}"
                    {% endif %}
                  >
                    {{- block.settings.link_label | escape -}}
                    <span class="icon-wrap">&nbsp;{% render 'icon-arrow' %}</span></a
                  >
                {%- endif -%}
              </div>
            </div>
          </li>
        {%- endfor -%}
      </ul>

      {%- if show_mobile_slider -%}
        <div class="slider-buttons no-js-hidden large-up-hide">
          <button
            type="button"
            class="slider-button slider-button--prev"
            name="previous"
            aria-label="{{ 'general.slider.previous_slide' | t }}"
          >
            {% render 'icon-caret' %}
          </button>
          <div class="slider-counter caption">
            <span class="slider-counter--current">1</span>
            <span aria-hidden="true"> / </span>
            <span class="visually-hidden">{{ 'general.slider.of' | t }}</span>
            <span class="slider-counter--total">{{ section.blocks.size }}</span>
          </div>
          <button
            type="button"
            class="slider-button slider-button--next"
            name="next"
            aria-label="{{ 'general.slider.next_slide' | t }}"
          >
            {% render 'icon-caret' %}
          </button>
        </div>
      {%- endif -%}
    </slider-component>
    <div class="center{% if show_mobile_slider %} small-hide medium-hide{% endif %}">
      {%- if section.settings.button_label != blank -%}
        <a
          class="button button--primary"
          {% if section.settings.button_link == blank %}
            role="link" aria-disabled="true"
          {% else %}
            href="{{ section.settings.button_link }}"
          {% endif %}
        >
          {{ section.settings.button_label | escape }}
        </a>
      {%- endif -%}
    </div>
  </div>
</div>

{% schema %}
{
  "name": "t:sections.multicolumn.name",
  "class": "section",
  "tag": "section",
  "disabled_on": {
    "groups": ["header", "footer"]
  },
  "settings": [
    {
      "type": "inline_richtext",
      "id": "title",
      "default": "Multicolumn",
      "label": "t:sections.multicolumn.settings.title.label"
    },
    {
      "type": "select",
      "id": "heading_size",
      "options": [
        {
          "value": "h2",
          "label": "t:sections.all.heading_size.options__1.label"
        },
        {
          "value": "h1",
          "label": "t:sections.all.heading_size.options__2.label"
        },
        {
          "value": "h0",
          "label": "t:sections.all.heading_size.options__3.label"
        }
      ],
      "default": "h1",
      "label": "t:sections.all.heading_size.label"
    },
    {
      "type": "select",
      "id": "image_width",
      "options": [
        {
          "value": "third",
          "label": "t:sections.multicolumn.settings.image_width.options__1.label"
        },
        {
          "value": "half",
          "label": "t:sections.multicolumn.settings.image_width.options__2.label"
        },
        {
          "value": "full",
          "label": "t:sections.multicolumn.settings.image_width.options__3.label"
        }
      ],
      "default": "full",
      "label": "t:sections.multicolumn.settings.image_width.label"
    },
    {
      "type": "select",
      "id": "image_ratio",
      "options": [
        {
          "value": "adapt",
          "label": "t:sections.multicolumn.settings.image_ratio.options__1.label"
        },
        {
          "value": "portrait",
          "label": "t:sections.multicolumn.settings.image_ratio.options__2.label"
        },
        {
          "value": "square",
          "label": "t:sections.multicolumn.settings.image_ratio.options__3.label"
        },
        {
          "value": "circle",
          "label": "t:sections.multicolumn.settings.image_ratio.options__4.label"
        }
      ],
      "default": "adapt",
      "label": "t:sections.multicolumn.settings.image_ratio.label"
    },
    {
      "type": "range",
      "id": "columns_desktop",
      "min": 1,
      "max": 6,
      "step": 1,
      "default": 3,
      "label": "t:sections.multicolumn.settings.columns_desktop.label"
    },
    {
      "type": "select",
      "id": "column_alignment",
      "options": [
        {
          "value": "left",
          "label": "t:sections.multicolumn.settings.column_alignment.options__1.label"
        },
        {
          "value": "center",
          "label": "t:sections.multicolumn.settings.column_alignment.options__2.label"
        }
      ],
      "default": "left",
      "label": "t:sections.multicolumn.settings.column_alignment.label"
    },
    {
      "type": "select",
      "id": "background_style",
      "options": [
        {
          "value": "none",
          "label": "t:sections.multicolumn.settings.background_style.options__1.label"
        },
        {
          "value": "primary",
          "label": "t:sections.multicolumn.settings.background_style.options__2.label"
        }
      ],
      "default": "primary",
      "label": "t:sections.multicolumn.settings.background_style.label"
    },
    {
      "type": "text",
      "id": "button_label",
      "default": "Button label",
      "label": "t:sections.multicolumn.settings.button_label.label"
    },
    {
      "type": "url",
      "id": "button_link",
      "label": "t:sections.multicolumn.settings.button_link.label"
    },
    {
      "type": "color_scheme",
      "id": "color_scheme",
      "label": "t:sections.all.colors.label",
      "default": "scheme-1"
    },
    {
      "type": "header",
      "content": "t:sections.multicolumn.settings.header_mobile.content"
    },
    {
      "type": "select",
      "id": "columns_mobile",
      "options": [
        {
          "value": "1",
          "label": "t:sections.multicolumn.settings.columns_mobile.options__1.label"
        },
        {
          "value": "2",
          "label": "t:sections.multicolumn.settings.columns_mobile.options__2.label"
        }
      ],
      "default": "1",
      "label": "t:sections.multicolumn.settings.columns_mobile.label"
    },
    {
      "type": "checkbox",
      "id": "swipe_on_mobile",
      "default": false,
      "label": "t:sections.multicolumn.settings.swipe_on_mobile.label"
    },
    {
      "type": "header",
      "content": "t:sections.all.padding.section_padding_heading"
    },
    {
      "type": "range",
      "id": "padding_top",
      "min": 0,
      "max": 100,
      "step": 4,
      "unit": "px",
      "label": "t:sections.all.padding.padding_top",
      "default": 36
    },
    {
      "type": "range",
      "id": "padding_bottom",
      "min": 0,
      "max": 100,
      "step": 4,
      "unit": "px",
      "label": "t:sections.all.padding.padding_bottom",
      "default": 36
    }
  ],
  "blocks": [
    {
      "type": "column",
      "name": "t:sections.multicolumn.blocks.column.name",
      "settings": [
        {
          "type": "image_picker",
          "id": "image",
          "label": "t:sections.multicolumn.blocks.column.settings.image.label"
        },
        {
          "type": "inline_richtext",
          "id": "title",
          "default": "Column",
          "label": "t:sections.multicolumn.blocks.column.settings.title.label"
        },
        {
          "type": "richtext",
          "id": "text",
          "default": "<p>Pair text with an image to focus on your chosen product, collection, or blog post. Add details on availability, style, or even provide a review.</p>",
          "label": "t:sections.multicolumn.blocks.column.settings.text.label"
        },
        {
          "type": "text",
          "id": "link_label",
          "label": "t:sections.multicolumn.blocks.column.settings.link_label.label"
        },
        {
          "type": "url",
          "id": "link",
          "label": "t:sections.multicolumn.blocks.column.settings.link.label"
        }
      ]
    }
  ],
  "presets": [
    {
      "name": "t:sections.multicolumn.presets.name",
      "blocks": [
        {
          "type": "column"
        },
        {
          "type": "column"
        },
        {
          "type": "column"
        }
      ]
    }
  ]
}
{% endschema %}
