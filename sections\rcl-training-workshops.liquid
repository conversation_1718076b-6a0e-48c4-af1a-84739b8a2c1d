{% schema %}
  {
    "name": "Training Workshops",
    "tag": "section",
    "class": "section",
    "settings": [
      {
        "type": "text",
        "id": "title",
        "label": "Section Title",
        "default": "Training & Workshops"
      },
      {
        "type": "color",
        "id": "title_color",
        "label": "Title Color",
        "default": "#d1b073"
      },
      {
        "type": "richtext",
        "id": "description",
        "label": "Section Description",
        "default": "<p>Our team of expert decorators regularly conducts specialized training sessions and workshops for professionals looking to master the art of applying decorative coatings.</p>"
      },
      {
        "type": "color",
        "id": "text_color",
        "label": "Text Color",
        "default": "#222228"
      },
      {
        "type": "color",
        "id": "background_color",
        "label": "Background Color",
        "default": "#ffffff"
      },
      {
        "type": "color",
        "id": "accent_color",
        "label": "Accent Color",
        "default": "#d1b073"
      },
      {
        "type": "select",
        "id": "layout",
        "label": "Layout Style",
        "options": [
          {
            "value": "list",
            "label": "List View"
          },
          {
            "value": "grid",
            "label": "Grid View"
          }
        ],
        "default": "grid"
      },
      {
        "type": "image_picker",
        "id": "background_image",
        "label": "Background Image (Optional)"
      },
      {
        "type": "range",
        "id": "overlay_opacity",
        "min": 0,
        "max": 95,
        "step": 5,
        "unit": "%",
        "label": "Background Overlay Opacity",
        "default": 10
      }
    ],
    "blocks": [
      {
        "type": "workshop",
        "name": "Workshop",
        "settings": [
          {
            "type": "image_picker",
            "id": "image",
            "label": "Workshop Image"
          },
          {
            "type": "text",
            "id": "title",
            "label": "Workshop Title",
            "default": "Advanced Texturing Techniques"
          },
          {
            "type": "color",
            "id": "title_color",
            "label": "Title Color",
            "default": "#222228"
          },
          {
            "type": "select",
            "id": "level",
            "label": "Difficulty Level",
            "options": [
              {
                "value": "beginner",
                "label": "Beginner"
              },
              {
                "value": "intermediate",
                "label": "Intermediate"
              },
              {
                "value": "advanced",
                "label": "Advanced"
              },
              {
                "value": "professional",
                "label": "Professional"
              }
            ],
            "default": "intermediate"
          },
          {
            "type": "text",
            "id": "duration",
            "label": "Duration",
            "default": "2 Days"
          },
          {
            "type": "textarea",
            "id": "description",
            "label": "Workshop Description",
            "default": "Learn the art of creating sophisticated textures using lime-based decorative coatings. This hands-on workshop covers multiple application techniques."
          },
          {
            "type": "text",
            "id": "schedule",
            "label": "Next Schedule",
            "default": "March 15-16, 2025"
          },
          {
            "type": "url",
            "id": "link",
            "label": "Registration Link"
          }
        ]
      },
      {
        "type": "highlight",
        "name": "Workshops Highlight",
        "limit": 1,
        "settings": [
          {
            "type": "text",
            "id": "title",
            "label": "Highlight Title",
            "default": "Why Choose Our Workshops?"
          },
          {
            "type": "color",
            "id": "title_color",
            "label": "Title Color",
            "default": "#d1b073"
          },
          {
            "type": "richtext",
            "id": "content",
            "label": "Highlight Content",
            "default": "<p>Our workshops are led by skilled professionals with years of industry experience. Participants receive hands-on training, comprehensive learning materials, and a certificate upon completion.</p>"
          },
          {
            "type": "image_picker",
            "id": "image",
            "label": "Highlight Image"
          },
          {
            "type": "text",
            "id": "button_text",
            "label": "Button Text",
            "default": "View All Workshops"
          },
          {
            "type": "url",
            "id": "button_link",
            "label": "Button Link"
          }
        ]
      }
    ],
    "presets": [
      {
        "name": "Training Workshops",
        "blocks": [
          {
            "type": "workshop"
          },
          {
            "type": "workshop"
          },
          {
            "type": "workshop"
          },
          {
            "type": "highlight"
          }
        ]
      }
    ]
  }
{% endschema %}
    
<div class="training-workshops-section" 
      style="background-color: {{ section.settings.background_color }}; 
            color: {{ section.settings.text_color }};"
      data-layout="{{ section.settings.layout }}">
  
  {% if section.settings.background_image != blank %}
    <div class="workshops-bg">
      {{ section.settings.background_image | image_url: width: 1500 | image_tag:
        loading: 'lazy',
        class: 'workshops-bg-image'
      }}
      <div class="workshops-overlay" 
            style="background-color: {{ section.settings.background_color }}; 
                  opacity: {{ section.settings.overlay_opacity | divided_by: 100.0 }};"></div>
    </div>
  {% endif %}
  
  <div class="page-width">
    <div class="section-header text-center">
      <h2 class="section-title" style="color: {{ section.settings.title_color }};">
        {{ section.settings.title }}
      </h2>
      
      <div class="section-description rte">
        {{ section.settings.description }}
      </div>
    </div>

    <div class="workshops-container">
      {% assign highlight_block = section.blocks | where: "type", "highlight" | first %}
      {% assign workshop_blocks = section.blocks | where: "type", "workshop" %}
      
      <div class="workshops-list">
        {% for block in workshop_blocks %}
          <div class="workshop-item" {{ block.shopify_attributes }}>
            <div class="workshop-card">
              {% if block.settings.image != blank %}
                <div class="workshop-image">
                  {{ block.settings.image | image_url: width: 600 | image_tag:
                    loading: 'lazy',
                    class: 'workshop-img',
                    widths: '275, 550, 710',
                    sizes: '(min-width: 750px) 33vw, 100vw'
                  }}
                  
                  <div class="workshop-level" style="background-color: {{ section.settings.accent_color }};">
                    {{ block.settings.level | capitalize }}
                  </div>
                </div>
              {% endif %}
              
              <div class="workshop-content">
                <h3 class="workshop-title" style="color: {{ block.settings.title_color }};">
                  {{ block.settings.title }}
                </h3>
                
                <div class="workshop-meta">
                  <div class="workshop-duration">
                    <svg width="18" height="18" viewBox="0 0 18 18" fill="none" xmlns="http://www.w3.org/2000/svg">
                      <path d="M9 16.5C13.1421 16.5 16.5 13.1421 16.5 9C16.5 4.85786 13.1421 1.5 9 1.5C4.85786 1.5 1.5 4.85786 1.5 9C1.5 13.1421 4.85786 16.5 9 16.5Z" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
                      <path d="M9 4.5V9L12 10.5" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
                    </svg>
                    <span>{{ block.settings.duration }}</span>
                  </div>
                  
                  <div class="workshop-schedule">
                    <svg width="18" height="18" viewBox="0 0 18 18" fill="none" xmlns="http://www.w3.org/2000/svg">
                      <path d="M14.25 3H3.75C2.92157 3 2.25 3.67157 2.25 4.5V15C2.25 15.8284 2.92157 16.5 3.75 16.5H14.25C15.0784 16.5 15.75 15.8284 15.75 15V4.5C15.75 3.67157 15.0784 3 14.25 3Z" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
                      <path d="M12 1.5V4.5" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
                      <path d="M6 1.5V4.5" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
                      <path d="M2.25 7.5H15.75" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
                    </svg>
                    <span>{{ block.settings.schedule }}</span>
                  </div>
                </div>
                
                <div class="workshop-description">
                  {{ block.settings.description }}
                </div>
                
                {% if block.settings.link != blank %}
                  <a href="{{ block.settings.link }}" class="workshop-button" 
                      style="background-color: {{ section.settings.accent_color }}; 
                            color: {{ section.settings.background_color }};">
                    Register Now
                  </a>
                {% endif %}
              </div>
            </div>
          </div>
        {% endfor %}
      </div>
      
      {% if highlight_block != nil %}
        <div class="workshops-highlight" {{ highlight_block.shopify_attributes }}>
          <div class="highlight-card">
            <h3 class="highlight-title" style="color: {{ highlight_block.settings.title_color }};">
              {{ highlight_block.settings.title }}
            </h3>
            
            <div class="highlight-content rte">
              {{ highlight_block.settings.content }}
            </div>
            
            {% if highlight_block.settings.image != blank %}
              <div class="highlight-image">
                {{ highlight_block.settings.image | image_url: width: 400 | image_tag:
                  loading: 'lazy',
                  class: 'highlight-img',
                  widths: '275, 400, 550',
                  sizes: '(min-width: 750px) 400px, 100vw'
                }}
              </div>
            {% endif %}
            
            {% if highlight_block.settings.button_text != blank and highlight_block.settings.button_link != blank %}
              <a href="{{ highlight_block.settings.button_link }}" class="highlight-button"
                  style="color: {{ section.settings.accent_color }}; 
                        border-color: {{ section.settings.accent_color }};">
                {{ highlight_block.settings.button_text }}
              </a>
            {% endif %}
          </div>
        </div>
      {% endif %}
    </div>
  </div>
</div>
    
<style>
  .training-workshops-section {
    padding: 80px 0;
    position: relative;
  }
  
  .workshops-bg {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 0;
  }
  
  .workshops-bg-image {
    width: 100%;
    height: 100%;
    object-fit: cover;
  }
  
  .workshops-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
  }
  
  .page-width {
    position: relative;
    z-index: 1;
  }
  
  .section-title {
    margin-bottom: 20px;
    font-size: calc(var(--font-heading-scale) * 2.3rem);
    letter-spacing: 0.05em;
    text-transform: uppercase;
    position: relative;
    display: inline-block;
    padding-bottom: 20px;
  }
  
  .section-title:after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 50%;
    transform: translateX(-50%);
    width: 80px;
    height: 3px;
    background-color: currentColor;
    border-radius: 2px;
  }
  
  .section-description {
    max-width: 800px;
    margin: 0 auto;
    font-size: 1.1rem;
    line-height: 1.6;
    margin-bottom: 60px;
  }
  
  /* Workshop Layout */
  .workshops-container {
    display: flex;
    flex-wrap: wrap;
    gap: 40px;
  }
  
  [data-layout="list"] .workshops-container {
    flex-direction: column;
  }
  
  [data-layout="grid"] .workshops-container {
    flex-direction: row;
  }
  
  [data-layout="list"] .workshops-list {
    flex: 1 1 100%;
  }
  
  [data-layout="grid"] .workshops-list {
    flex: 1 1 60%;
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 30px;
  }
  
  [data-layout="list"] .workshops-highlight {
    flex: 1 1 100%;
  }
  
  [data-layout="grid"] .workshops-highlight {
    flex: 1 1 30%;
  }
  
  /* Workshop Card */
  .workshop-item {
    margin-bottom: 30px;
  }
  
  [data-layout="list"] .workshop-item:last-child {
    margin-bottom: 0;
  }
  
  .workshop-card {
    background-color: #ffffff;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 5px 20px rgba(0, 0, 0, 0.05);
    height: 100%;
    transition: transform 0.3s ease, box-shadow 0.3s ease;
  }
  
  .workshop-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
  }
  
  [data-layout="list"] .workshop-card {
    display: flex;
    flex-direction: row;
  }
  
  [data-layout="grid"] .workshop-card {
    display: flex;
    flex-direction: column;
  }
  
  .workshop-image {
    position: relative;
  }
  
  [data-layout="list"] .workshop-image {
    flex: 0 0 35%;
  }
  
  .workshop-img {
    width: 100%;
    height: 100%;
    display: block;
    object-fit: cover;
  }
  
  [data-layout="list"] .workshop-img {
    height: 100%;
    object-fit: cover;
  }
  
  [data-layout="grid"] .workshop-img {
    aspect-ratio: 16 / 9;
  }
  
  .workshop-level {
    position: absolute;
    top: 15px;
    right: 15px;
    padding: 5px 10px;
    border-radius: 4px;
    font-size: 0.8rem;
    font-weight: 600;
    color: #ffffff;
    letter-spacing: 0.05em;
    text-transform: uppercase;
  }
  
  .workshop-content {
    padding: 25px;
    display: flex;
    flex-direction: column;
    height: 100%;
  }
  
  [data-layout="list"] .workshop-content {
    flex: 1;
  }
  
  .workshop-title {
    margin: 0 0 15px;
    font-size: 1.3rem;
    font-weight: 600;
  }
  
  .workshop-meta {
    display: flex;
    gap: 20px;
    margin-bottom: 15px;
    opacity: 0.8;
    font-size: 0.9rem;
  }
  
  .workshop-duration,
  .workshop-schedule {
    display: flex;
    align-items: center;
    gap: 5px;
  }
  
  .workshop-description {
    margin-bottom: 20px;
    font-size: 0.95rem;
    line-height: 1.6;
    flex-grow: 1;
  }
  
  .workshop-button {
    display: inline-flex;
    padding: 0.7rem 1.5rem;
    font-size: 0.9rem;
    font-weight: 600;
    text-decoration: none;
    border-radius: 4px;
    align-self: flex-start;
    transition: opacity 0.3s;
  }
  
  .workshop-button:hover {
    opacity: 0.9;
  }
  
  /* Highlight Card */
  .highlight-card {
    background-color: #ffffff;
    border-radius: 8px;
    padding: 30px;
    box-shadow: 0 5px 20px rgba(0, 0, 0, 0.05);
    height: 100%;
    display: flex;
    flex-direction: column;
  }
  
  .highlight-title {
    margin: 0 0 20px;
    font-size: 1.5rem;
    font-weight: 600;
    position: relative;
    padding-bottom: 15px;
  }
  
  .highlight-title:after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 60px;
    height: 3px;
    background-color: {{ section.settings.accent_color }};
    border-radius: 2px;
  }
  
  .highlight-content {
    margin-bottom: 25px;
    font-size: 1rem;
    line-height: 1.6;
  }
  
  .highlight-image {
    margin: 0 -30px 25px;
  }
  
  .highlight-img {
    width: 100%;
    display: block;
    border-radius: 8px;
  }
  
  .highlight-button {
    display: inline-flex;
    padding: 0.8rem 1.8rem;
    font-size: 0.95rem;
    font-weight: 600;
    text-decoration: none;
    border-radius: 4px;
    border: 2px solid;
    align-self: flex-start;
    margin-top: auto;
    transition: background-color 0.3s ease;
  }
  
  .highlight-button:hover {
    background-color: {{ section.settings.accent_color }};
    color: {{ section.settings.background_color }} !important;
  }
  
  /* Responsive */
  @media screen and (max-width: 989px) {
    .section-title {
      font-size: calc(var(--font-heading-scale) * 2rem);
    }
    
    [data-layout="grid"] .workshops-list {
      grid-template-columns: 1fr;
      gap: 20px;
    }
    
    [data-layout="grid"] .workshops-container {
      flex-direction: column;
    }
    
    [data-layout="list"] .workshop-card {
      flex-direction: column;
    }
    
    [data-layout="list"] .workshop-image {
      flex: 0 0 auto;
    }
  }
  
  @media screen and (max-width: 749px) {
    .training-workshops-section {
      padding: 60px 0;
    }
    
    .section-title {
      font-size: calc(var(--font-heading-scale) * 1.8rem);
      margin-bottom: 40px;
    }
    
    .section-description {
      font-size: 1rem;
    }
    
    .workshop-title {
      font-size: 1.2rem;
    }
    
    .workshop-meta {
      flex-direction: column;
      gap: 10px;
    }
    
    .workshop-content {
      padding: 20px;
    }
    
    .highlight-card {
      padding: 25px;
    }
    
    .highlight-title {
      font-size: 1.3rem;
    }
    
    .highlight-image {
      margin: 0 -25px 20px;
    }
  }
</style>