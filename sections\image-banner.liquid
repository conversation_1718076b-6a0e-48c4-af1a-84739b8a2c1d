{{ 'section-image-banner.css' | asset_url | stylesheet_tag }}

{%- if section.settings.image_height == 'adapt' and section.settings.image != blank -%}
  {%- style -%}
    @media screen and (max-width: 749px) {
      #Banner-{{ section.id }}::before,
      #Banner-{{ section.id }} .banner__media::before,
      #Banner-{{ section.id }}:not(.banner--mobile-bottom) .banner__content::before {
        padding-bottom: {{ 1 | divided_by: section.settings.image.aspect_ratio | times: 100 }}%;
        content: '';
        display: block;
      }
    }

    @media screen and (min-width: 750px) {
      #Banner-{{ section.id }}::before,
      #Banner-{{ section.id }} .banner__media::before {
        padding-bottom: {{ 1 | divided_by: section.settings.image.aspect_ratio | times: 100 }}%;
        content: '';
        display: block;
      }
    }
  {%- endstyle -%}
{%- endif -%}

{%- style -%}
  #Banner-{{ section.id }}::after {
    opacity: {{ section.settings.image_overlay_opacity | divided_by: 100.0 }};
  }
{%- endstyle -%}

{%- liquid
  assign full_width = '100vw'
  assign widths = '375, 550, 750, 1100, 1500, 1780, 2000, 3000, 3840'

  if section.settings.image_behavior == 'ambient'
    assign half_width = '60vw'
    assign full_width = '120vw'
    assign stacked_sizes = '(min-width: 750px) 60vw, 120vw'
    assign widths = '450, 660, 900, 1320, 1800, 2136, 2400, 3600, 7680'
  elsif section.settings.image_behavior == 'fixed' or section.settings.image_behavior == 'zoom-in'
    assign half_width = '100vw'
    assign stacked_sizes = '100vw'
  else
    assign half_width = '50vw'
    assign stacked_sizes = '(min-width: 750px) 50vw, 100vw'
  endif
  assign fetch_priority = 'auto'
  if section.index == 1
    assign fetch_priority = 'high'
  endif
-%}

<div
  id="Banner-{{ section.id }}"
  class="banner banner--content-align-{{ section.settings.desktop_content_alignment }} banner--content-align-mobile-{{ section.settings.mobile_content_alignment }} banner--{{ section.settings.image_height }}{% if section.settings.stack_images_on_mobile and section.settings.image != blank and section.settings.image_2 != blank %} banner--stacked{% endif %}{% if section.settings.image_height == 'adapt' and section.settings.image != blank %} banner--adapt{% endif %}{% if section.settings.show_text_below %} banner--mobile-bottom{%- endif -%}{% if section.settings.show_text_box == false %} banner--desktop-transparent{% endif %}{% if settings.animations_reveal_on_scroll %} scroll-trigger animate--fade-in{% endif %}
  {% if section.settings.hide_on_mobile %} hide-mobile {% endif %}
  {% if section.settings.hide_on_tablet %} hide-tablet {% endif %}
  {% if section.settings.hide_on_desktop %} hide-desktop {% endif %}"
>
  {%- if section.settings.image != blank -%}
    <div class="banner__media media{% if section.settings.image == blank and section.settings.image_2 == blank %} placeholder{% endif %}{% if section.settings.image_2 != blank %} banner__media-half{% endif %}{% if section.settings.image_behavior != 'none' %} animate--{{ section.settings.image_behavior }}{% endif %}{% if settings.animations_reveal_on_scroll %} scroll-trigger animate--fade-in{% endif %}">
      {%- liquid
        assign image_height = section.settings.image.width | divided_by: section.settings.image.aspect_ratio
        if section.settings.image_2 != blank
          assign image_class = 'banner__media-image-half'
        endif
        if section.settings.image_2 != blank and section.settings.stack_images_on_mobile
          assign sizes = stacked_sizes
        elsif section.settings.image_2 != blank
          assign sizes = half_width
        else
          assign sizes = full_width
        endif
      -%}
      {{
        section.settings.image
        | image_url: width: 3840
        | image_tag:
          width: section.settings.image.width,
          height: image_height,
          class: image_class,
          sizes: sizes,
          widths: widths,
          fetchpriority: fetch_priority
      }}
    </div>
  {%- elsif section.settings.image_2 == blank -%}
    <div class="banner__media media{% if section.settings.image == blank and section.settings.image_2 == blank %} placeholder{% endif %}{% if section.settings.image_2 != blank %} banner__media-half{% endif %}{% if section.settings.image_behavior != 'none' %} animate--{{ section.settings.image_behavior }}{% endif %}{% if settings.animations_reveal_on_scroll %} scroll-trigger animate--fade-in{% endif %}">
      {{ 'hero-apparel-1' | placeholder_svg_tag: 'placeholder-svg' }}
    </div>
  {%- endif -%}
  {%- if section.settings.image_2 != blank -%}
    <div class="banner__media media{% if section.settings.image != blank %} banner__media-half{% endif %}{% if section.settings.image_behavior != 'none' %} animate--{{ section.settings.image_behavior }}{% endif %}{% if settings.animations_reveal_on_scroll %} scroll-trigger animate--fade-in{% endif %}">
      {%- liquid
        assign image_height_2 = section.settings.image_2.width | divided_by: section.settings.image_2.aspect_ratio
        if section.settings.image != blank
          assign image_class_2 = 'banner__media-image-half'
        endif
        if section.settings.image != blank and section.settings.stack_images_on_mobile
          assign sizes = stacked_sizes
        elsif section.settings.image_2 != blank
          assign sizes = half_width
        else
          assign sizes = full_width
        endif
      -%}
      {{
        section.settings.image_2
        | image_url: width: 3840
        | image_tag:
          width: section.settings.image_2.width,
          height: image_height_2,
          class: image_class_2,
          sizes: sizes,
          widths: widths,
          fetchpriority: fetch_priority
      }}
    </div>
  {%- endif -%}
  <div class="banner__content banner__content--{{ section.settings.desktop_content_position }} page-width{% if settings.animations_reveal_on_scroll %} scroll-trigger animate--slide-in{% endif %}">
    <div class="banner__box content-container content-container--full-width-mobile color-{{ section.settings.color_scheme }} gradient">
      {%- for block in section.blocks -%}
        {%- case block.type -%}
          {%- when 'heading' -%}
            <h2
              class="banner__heading inline-richtext {{ block.settings.heading_size }}"
              {{ block.shopify_attributes }}
            >
              {{ block.settings.heading }}
            </h2>
          {%- when 'text' -%}
            <div class="banner__text rte {{ block.settings.text_style }}" {{ block.shopify_attributes }}>
              <p>{{ block.settings.text }}</p>
            </div>
          {%- when 'buttons' -%}
            <div
              class="banner__buttons{% if block.settings.button_label_1 != blank and block.settings.button_label_2 != blank %} banner__buttons--multiple{% endif %}"
              {{ block.shopify_attributes }}
            >
              {%- if block.settings.button_label_1 != blank -%}
                <a
                  {% if block.settings.button_link_1 == blank %}
                    role="link" aria-disabled="true"
                  {% else %}
                    href="{{ block.settings.button_link_1 }}"
                  {% endif %}
                  class="button{% if block.settings.button_style_secondary_1 %} button--secondary{% else %} button--primary{% endif %}"
                >
                  {{- block.settings.button_label_1 | escape -}}
                </a>
              {%- endif -%}
              {%- if block.settings.button_label_2 != blank -%}
                <a
                  {% if block.settings.button_link_2 == blank %}
                    role="link" aria-disabled="true"
                  {% else %}
                    href="{{ block.settings.button_link_2 }}"
                  {% endif %}
                  class="button{% if block.settings.button_style_secondary_2 %} button--secondary{% else %} button--primary{% endif %}"
                >
                  {{- block.settings.button_label_2 | escape -}}
                </a>
              {%- endif -%}
            </div>
        {%- endcase -%}
      {%- endfor -%}
    </div>
  </div>
</div>

{% schema %}
{
  "name": "t:sections.image-banner.name",
  "tag": "section",
  "class": "section",
  "disabled_on": {
    "groups": ["header", "footer"]
  },
  "settings": [
    {
      "type": "header",
      "content": "Visibility Settings"
    },
    {
      "type": "checkbox",
      "id": "hide_on_mobile",
      "label": "Hide on Mobile"
    },
    {
      "type": "checkbox",
      "id": "hide_on_tablet",
      "label": "Hide on Tablet"
    },
    {
      "type": "checkbox",
      "id": "hide_on_desktop",
      "label": "Hide on Desktop"
    },
    {
      "type": "image_picker",
      "id": "image",
      "label": "t:sections.image-banner.settings.image.label"
    },
    {
      "type": "image_picker",
      "id": "image_2",
      "label": "t:sections.image-banner.settings.image_2.label"
    },
    {
      "type": "range",
      "id": "image_overlay_opacity",
      "min": 0,
      "max": 100,
      "step": 10,
      "unit": "%",
      "label": "t:sections.image-banner.settings.image_overlay_opacity.label",
      "default": 0
    },
    {
      "type": "select",
      "id": "image_height",
      "options": [
        {
          "value": "adapt",
          "label": "t:sections.image-banner.settings.image_height.options__1.label"
        },
        {
          "value": "small",
          "label": "t:sections.image-banner.settings.image_height.options__2.label"
        },
        {
          "value": "medium",
          "label": "t:sections.image-banner.settings.image_height.options__3.label"
        },
        {
          "value": "large",
          "label": "t:sections.image-banner.settings.image_height.options__4.label"
        }
      ],
      "default": "medium",
      "label": "t:sections.image-banner.settings.image_height.label",
      "info": "t:sections.image-banner.settings.image_height.info"
    },
    {
      "type": "select",
      "id": "desktop_content_position",
      "options": [
        {
          "value": "top-left",
          "label": "t:sections.image-banner.settings.desktop_content_position.options__1.label"
        },
        {
          "value": "top-center",
          "label": "t:sections.image-banner.settings.desktop_content_position.options__2.label"
        },
        {
          "value": "top-right",
          "label": "t:sections.image-banner.settings.desktop_content_position.options__3.label"
        },
        {
          "value": "middle-left",
          "label": "t:sections.image-banner.settings.desktop_content_position.options__4.label"
        },
        {
          "value": "middle-center",
          "label": "t:sections.image-banner.settings.desktop_content_position.options__5.label"
        },
        {
          "value": "middle-right",
          "label": "t:sections.image-banner.settings.desktop_content_position.options__6.label"
        },
        {
          "value": "bottom-left",
          "label": "t:sections.image-banner.settings.desktop_content_position.options__7.label"
        },
        {
          "value": "bottom-center",
          "label": "t:sections.image-banner.settings.desktop_content_position.options__8.label"
        },
        {
          "value": "bottom-right",
          "label": "t:sections.image-banner.settings.desktop_content_position.options__9.label"
        }
      ],
      "default": "middle-center",
      "label": "t:sections.image-banner.settings.desktop_content_position.label"
    },
    {
      "type": "checkbox",
      "id": "show_text_box",
      "default": true,
      "label": "t:sections.image-banner.settings.show_text_box.label"
    },
    {
      "type": "select",
      "id": "desktop_content_alignment",
      "options": [
        {
          "value": "left",
          "label": "t:sections.image-banner.settings.desktop_content_alignment.options__1.label"
        },
        {
          "value": "center",
          "label": "t:sections.image-banner.settings.desktop_content_alignment.options__2.label"
        },
        {
          "value": "right",
          "label": "t:sections.image-banner.settings.desktop_content_alignment.options__3.label"
        }
      ],
      "default": "center",
      "label": "t:sections.image-banner.settings.desktop_content_alignment.label"
    },
    {
      "type": "color_scheme",
      "id": "color_scheme",
      "label": "t:sections.all.colors.label",
      "default": "scheme-1"
    },
    {
      "type": "header",
      "content": "t:sections.all.animation.content"
    },
    {
      "type": "select",
      "id": "image_behavior",
      "options": [
        {
          "value": "none",
          "label": "t:sections.all.animation.image_behavior.options__1.label"
        },
        {
          "value": "ambient",
          "label": "t:sections.all.animation.image_behavior.options__2.label"
        },
        {
          "value": "fixed",
          "label": "t:sections.all.animation.image_behavior.options__3.label"
        },
        {
          "value": "zoom-in",
          "label": "t:sections.all.animation.image_behavior.options__4.label"
        }
      ],
      "default": "none",
      "label": "t:sections.all.animation.image_behavior.label"
    },
    {
      "type": "header",
      "content": "t:sections.image-banner.settings.mobile.content"
    },
    {
      "type": "select",
      "id": "mobile_content_alignment",
      "options": [
        {
          "value": "left",
          "label": "t:sections.image-banner.settings.mobile_content_alignment.options__1.label"
        },
        {
          "value": "center",
          "label": "t:sections.image-banner.settings.mobile_content_alignment.options__2.label"
        },
        {
          "value": "right",
          "label": "t:sections.image-banner.settings.mobile_content_alignment.options__3.label"
        }
      ],
      "default": "center",
      "label": "t:sections.image-banner.settings.mobile_content_alignment.label"
    },
    {
      "type": "checkbox",
      "id": "stack_images_on_mobile",
      "default": true,
      "label": "t:sections.image-banner.settings.stack_images_on_mobile.label"
    },
    {
      "type": "checkbox",
      "id": "show_text_below",
      "default": true,
      "label": "t:sections.image-banner.settings.show_text_below.label"
    }
  ],
  "blocks": [
    {
      "type": "heading",
      "name": "t:sections.image-banner.blocks.heading.name",
      "limit": 1,
      "settings": [
        {
          "type": "inline_richtext",
          "id": "heading",
          "default": "Image banner",
          "label": "t:sections.image-banner.blocks.heading.settings.heading.label"
        },
        {
          "type": "select",
          "id": "heading_size",
          "options": [
            {
              "value": "h2",
              "label": "t:sections.all.heading_size.options__1.label"
            },
            {
              "value": "h1",
              "label": "t:sections.all.heading_size.options__2.label"
            },
            {
              "value": "h0",
              "label": "t:sections.all.heading_size.options__3.label"
            }
          ],
          "default": "h1",
          "label": "t:sections.all.heading_size.label"
        }
      ]
    },
    {
      "type": "text",
      "name": "t:sections.image-banner.blocks.text.name",
      "limit": 1,
      "settings": [
        {
          "type": "inline_richtext",
          "id": "text",
          "default": "Give customers details about the banner image(s) or content on the template.",
          "label": "t:sections.image-banner.blocks.text.settings.text.label"
        },
        {
          "type": "select",
          "id": "text_style",
          "options": [
            {
              "value": "body",
              "label": "t:sections.image-banner.blocks.text.settings.text_style.options__1.label"
            },
            {
              "value": "subtitle",
              "label": "t:sections.image-banner.blocks.text.settings.text_style.options__2.label"
            },
            {
              "value": "caption-with-letter-spacing",
              "label": "t:sections.image-banner.blocks.text.settings.text_style.options__3.label"
            }
          ],
          "default": "body",
          "label": "t:sections.image-banner.blocks.text.settings.text_style.label"
        }
      ]
    },
    {
      "type": "buttons",
      "name": "t:sections.image-banner.blocks.buttons.name",
      "limit": 1,
      "settings": [
        {
          "type": "text",
          "id": "button_label_1",
          "default": "Button label",
          "label": "t:sections.image-banner.blocks.buttons.settings.button_label_1.label",
          "info": "t:sections.image-banner.blocks.buttons.settings.button_label_1.info"
        },
        {
          "type": "url",
          "id": "button_link_1",
          "label": "t:sections.image-banner.blocks.buttons.settings.button_link_1.label"
        },
        {
          "type": "checkbox",
          "id": "button_style_secondary_1",
          "default": false,
          "label": "t:sections.image-banner.blocks.buttons.settings.button_style_secondary_1.label"
        },
        {
          "type": "text",
          "id": "button_label_2",
          "default": "Button label",
          "label": "t:sections.image-banner.blocks.buttons.settings.button_label_2.label",
          "info": "t:sections.image-banner.blocks.buttons.settings.button_label_2.info"
        },
        {
          "type": "url",
          "id": "button_link_2",
          "label": "t:sections.image-banner.blocks.buttons.settings.button_link_2.label"
        },
        {
          "type": "checkbox",
          "id": "button_style_secondary_2",
          "default": false,
          "label": "t:sections.image-banner.blocks.buttons.settings.button_style_secondary_2.label"
        }
      ]
    }
  ],
  "presets": [
    {
      "name": "t:sections.image-banner.presets.name",
      "blocks": [
        {
          "type": "heading"
        },
        {
          "type": "text"
        },
        {
          "type": "buttons"
        }
      ]
    }
  ]
}
{% endschema %}
