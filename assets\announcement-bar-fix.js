(function() {
  function fixAnnouncementBarOnly() {
    const announcementBars = document.querySelectorAll('.announcement-bar, .announcement-bar-section');
    
    if (!announcementBars || announcementBars.length === 0) return;
    
    announcementBars.forEach(bar => {
      const hiddenSlides = bar.querySelectorAll('.slider__slide[aria-hidden="true"]');
      
      hiddenSlides.forEach(slide => {
        if (!slide.hasAttribute('inert')) {
          slide.removeAttribute('aria-hidden');
          slide.setAttribute('inert', '');
        }
      });
      
      const visibleSlides = bar.querySelectorAll('.slider__slide:not([aria-hidden="true"]):not([inert])');
      visibleSlides.forEach(slide => {
        if (slide.hasAttribute('inert')) {
          slide.removeAttribute('inert');
        }
      });
      
      const buttons = bar.querySelectorAll('.slider-button--next, .slider-button--prev');
      buttons.forEach(btn => {
        if (btn.disabled) {
          btn.disabled = false;
        }
        if (btn.hasAttribute('aria-disabled')) {
          btn.removeAttribute('aria-disabled');
        }
      });
    });
  }

  function setupAnnouncementBarObserver() {
    const announcementBars = document.querySelectorAll('.announcement-bar, .announcement-bar-section');
    
    if (!announcementBars || announcementBars.length === 0) return;
    
    const observer = new MutationObserver((mutations) => {
      let needsUpdate = false;
      
      mutations.forEach(mutation => {
        if (mutation.type === 'attributes' && 
            (mutation.attributeName === 'aria-hidden' || 
            mutation.attributeName === 'class')) {
          needsUpdate = true;
        }
      });
      
      if (needsUpdate) {
        fixAnnouncementBarOnly();
      }
    });
    
    announcementBars.forEach(bar => {
      observer.observe(bar, { 
        attributes: true, 
        childList: true, 
        subtree: true,
        attributeFilter: ['aria-hidden', 'class'] 
      });
    });
  }

  function hookAnnouncementBarButtons() {
    const announcementBars = document.querySelectorAll('.announcement-bar, .announcement-bar-section');
    
    if (!announcementBars || announcementBars.length === 0) return;
    
    announcementBars.forEach(bar => {
      const buttons = bar.querySelectorAll('.slider-button--next, .slider-button--prev');
      
      buttons.forEach(btn => {
        const originalClick = btn.onclick;
        
        btn.onclick = function(e) {
          if (originalClick) {
            originalClick.call(this, e);
          }
          
          setTimeout(fixAnnouncementBarOnly, 50);
          setTimeout(fixAnnouncementBarOnly, 300);
        };
      });
    });
  }

  function addAnnouncementStyles() {
    const styleId = 'announcement-bar-a11y-fix';
    
    if (!document.getElementById(styleId)) {
      const style = document.createElement('style');
      style.id = styleId;
      style.textContent = `
        .announcement-bar .slider__slide[inert] {
          display: none !important;
          visibility: hidden !important;
        }
        .announcement-bar-section .slider__slide[inert] {
          display: none !important;
          visibility: hidden !important;
        }
      `;
      document.head.appendChild(style);
    }
  }

  function initialize() {
    if (!document.querySelector('.announcement-bar, .announcement-bar-section')) {
      return;
    }
    
    addAnnouncementStyles();
    fixAnnouncementBarOnly();
    hookAnnouncementBarButtons();
    setupAnnouncementBarObserver();
    
    setTimeout(fixAnnouncementBarOnly, 1000);
  }

  if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', initialize);
  } else {
    initialize();
  }
})();