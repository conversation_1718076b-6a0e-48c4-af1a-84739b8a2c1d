{% schema %}
    {
      "name": "Company Creation",
      "tag": "section",
      "class": "section",
      "settings": [
        {
          "type": "text",
          "id": "title",
          "label": "Section Title",
          "default": "Creating a Company"
        },
        {
          "type": "color",
          "id": "title_color",
          "label": "Title Color",
          "default": "#d1b073"
        },
        {
          "type": "color",
          "id": "text_color",
          "label": "Text Color",
          "default": "#222228"
        },
        {
          "type": "color",
          "id": "background_color",
          "label": "Background Color",
          "default": "#ffffff"
        },
        {
          "type": "color",
          "id": "accent_color",
          "label": "Accent Color",
          "default": "#d1b073"
        },
        {
          "type": "image_picker",
          "id": "background_image",
          "label": "Background Image (Optional)"
        },
        {
          "type": "range",
          "id": "overlay_opacity",
          "min": 0,
          "max": 90,
          "step": 10,
          "unit": "%",
          "label": "Background Overlay Opacity",
          "default": 20
        }
      ],
      "blocks": [
        {
          "type": "text_column",
          "name": "Text Column",
          "settings": [
            {
              "type": "text",
              "id": "heading",
              "label": "Heading",
              "default": "Our History"
            },
            {
              "type": "color",
              "id": "heading_color",
              "label": "Heading Color",
              "default": "#222228"
            },
            {
              "type": "richtext",
              "id": "text",
              "label": "Text Content",
              "default": "<p>Elf Decor brand was established in 2006. The idea of its creation appeared after five years of experience in the production of building materials of Elf™, as well as the successful work of our showrooms, representing decorative coatings of well-known European manufacturers.</p>"
            }
          ]
        },
        {
          "type": "statistic",
          "name": "Statistic",
          "settings": [
            {
              "type": "text",
              "id": "number",
              "label": "Number",
              "default": "20"
            },
            {
              "type": "color",
              "id": "number_color",
              "label": "Number Color",
              "default": "#d1b073"
            },
            {
              "type": "text",
              "id": "label",
              "label": "Label",
              "default": "years of work"
            },
            {
              "type": "color",
              "id": "label_color",
              "label": "Label Color",
              "default": "#222228"
            },
            {
              "type": "richtext",
              "id": "description",
              "label": "Description",
              "default": "<p>Over these years, we have gained enough experience and knowledge to confidently state that we know everything about decorative coatings.</p>"
            }
          ]
        },
        {
          "type": "image",
          "name": "Image",
          "settings": [
            {
              "type": "image_picker",
              "id": "image",
              "label": "Image"
            },
            {
              "type": "select",
              "id": "image_shape",
              "label": "Image Shape",
              "options": [
                {
                  "value": "square",
                  "label": "Square"
                },
                {
                  "value": "portrait",
                  "label": "Portrait"
                },
                {
                  "value": "landscape",
                  "label": "Landscape"
                },
                {
                  "value": "rounded",
                  "label": "Rounded"
                }
              ],
              "default": "rounded"
            }
          ]
        }
      ],
      "presets": [
        {
          "name": "Company Creation",
          "blocks": [
            {
              "type": "text_column"
            },
            {
              "type": "statistic"
            },
            {
              "type": "image"
            }
          ]
        }
      ]
    }
    {% endschema %}
    
    <div class="company-creation-section" 
         style="background-color: {{ section.settings.background_color }}; 
                color: {{ section.settings.text_color }};">
      
      {% if section.settings.background_image != blank %}
        <div class="company-creation-bg">
          {{ section.settings.background_image | image_url: width: 1500 | image_tag:
            loading: 'lazy',
            class: 'company-creation-bg-image'
          }}
          <div class="company-creation-overlay" 
               style="background-color: {{ section.settings.background_color }}; 
                      opacity: {{ section.settings.overlay_opacity | divided_by: 100.0 }};"></div>
        </div>
      {% endif %}
      
      <div class="page-width">
        <div class="section-header text-center">
          <h2 class="section-title" style="color: {{ section.settings.title_color }};">
            {{ section.settings.title }}
          </h2>
        </div>
    
        <div class="company-creation-grid">
          {% for block in section.blocks %}
            {% case block.type %}
            
              {% when 'text_column' %}
                <div class="company-creation-column text-column" {{ block.shopify_attributes }}>
                  {% if block.settings.heading != blank %}
                    <h3 class="column-heading" style="color: {{ block.settings.heading_color }};">
                      {{ block.settings.heading }}
                    </h3>
                  {% endif %}
                  
                  <div class="column-text rte">
                    {{ block.settings.text }}
                  </div>
                </div>
              
              {% when 'statistic' %}
                <div class="company-creation-column statistic-column" {{ block.shopify_attributes }}>
                  <div class="statistic-number" style="color: {{ block.settings.number_color }};">
                    {{ block.settings.number }}
                  </div>
                  
                  <div class="statistic-label" style="color: {{ block.settings.label_color }};">
                    {{ block.settings.label }}
                  </div>
                  
                  <div class="statistic-description rte">
                    {{ block.settings.description }}
                  </div>
                </div>
              
              {% when 'image' %}
                <div class="company-creation-column image-column {{ block.settings.image_shape }}" {{ block.shopify_attributes }}>
                  {% if block.settings.image != blank %}
                    {{ block.settings.image | image_url: width: 600 | image_tag:
                      loading: 'lazy',
                      class: 'column-image',
                      widths: '275, 550, 710, 1420',
                      sizes: '(min-width: 750px) 33vw, 100vw'
                    }}
                  {% else %}
                    {{ 'image' | placeholder_svg_tag: 'placeholder-svg column-image' }}
                  {% endif %}
                </div>
                
            {% endcase %}
          {% endfor %}
        </div>
      </div>
    </div>
    
    <style>
      .company-creation-section {
        position: relative;
        padding: 80px 0;
        overflow: hidden;
      }
      
      .company-creation-bg {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        z-index: -1;
      }
      
      .company-creation-bg-image {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        object-fit: cover;
      }
      
      .company-creation-overlay {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
      }
      
      .section-title {
        text-transform: uppercase;
        position: relative;
        display: inline-block;
        padding-bottom: 20px;
      }
      
      .section-title:after {
        content: '';
        position: absolute;
        bottom: 0;
        left: 50%;
        transform: translateX(-50%);
        width: 80px;
        height: 3px;
        background-color: currentColor;
        border-radius: 2px;
      }
      
      .company-creation-grid {
        display: grid;
        grid-template-columns: repeat(3, 1fr);
        gap: 30px;
      }
      
      .company-creation-column {
        padding: 30px;
        background-color: rgba(255, 255, 255, 0.8);
        border-radius: 8px;
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
        transition: transform 0.3s ease, box-shadow 0.3s ease;
      }
      
      .company-creation-column:hover {
        transform: translateY(-5px);
        box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
      }
      
      /* Text Column */
      .column-heading {
        margin: 0 0 20px;
        font-size: 1.6rem;
        font-weight: 600;
        position: relative;
        padding-bottom: 15px;
      }
      
      .column-heading:after {
        content: '';
        position: absolute;
        bottom: 0;
        left: 0;
        width: 60px;
        height: 3px;
        background-color: {{ section.settings.accent_color }};
        border-radius: 2px;
      }
      
      .column-text {
        font-size: 1.05rem;
        line-height: 1.7;
      }
      
      /* Statistic Column */
      .statistic-column {
        text-align: center;
      }
      
      .statistic-number {
        font-size: 4rem;
        font-weight: 700;
        line-height: 1;
        margin-bottom: 10px;
      }
      
      .statistic-label {
        font-size: 1.2rem;
        font-weight: 600;
        text-transform: uppercase;
        letter-spacing: 0.05em;
        margin-bottom: 20px;
      }
      
      .statistic-description {
        font-size: 1rem;
        line-height: 1.6;
      }
      
      /* Image Column */
      .image-column {
        padding: 0;
        overflow: hidden;
        box-shadow: none;
        background-color: transparent;
      }
      
      .image-column:hover {
        transform: none;
        box-shadow: none;
      }
      
      .column-image {
        display: block;
        width: 100%;
        height: 100%;
        object-fit: cover;
        transition: transform 0.6s ease;
      }
      
      .image-column:hover .column-image {
        transform: scale(1.05);
      }
      
      .image-column.square {
        aspect-ratio: 1 / 1;
      }
      
      .image-column.portrait {
        aspect-ratio: 3 / 4;
      }
      
      .image-column.landscape {
        aspect-ratio: 4 / 3;
      }
      
      .image-column.rounded {
        border-radius: 8px;
      }
      
      .rounded .column-image {
        border-radius: 8px;
      }
      
      /* Responsive */
      @media screen and (max-width: 989px) {
        .company-creation-grid {
          grid-template-columns: repeat(2, 1fr);
        }
        
        .image-column {
          grid-column: span 2;
        }
      }
      
      @media screen and (max-width: 749px) {
        .company-creation-section {
          padding: 60px 0;
        }
        
        .section-title {
          font-size: calc(var(--font-heading-scale) * 1.8rem);
        }
        
        .company-creation-grid {
          grid-template-columns: 1fr;
        }
        
        .image-column {
          grid-column: span 1;
        }
        
        .column-heading {
          font-size: 1.4rem;
        }
        
        .column-text {
          font-size: 1rem;
        }
        
        .statistic-number {
          font-size: 3rem;
        }
      }
    </style>