<section
  id="MainProduct-{{ section.id }}"
  class="section-{{ section.id }}-padding gradient color-{{ section.settings.color_scheme }}"
  data-section="{{ section.id }}"
>
  {{ 'section-main-product.css' | asset_url | stylesheet_tag }}
  {{ 'component-accordion.css' | asset_url | stylesheet_tag }}
  {{ 'component-price.css' | asset_url | stylesheet_tag }}
  {{ 'component-slider.css' | asset_url | stylesheet_tag }}
  {{ 'component-rating.css' | asset_url | stylesheet_tag }}
  {{ 'component-deferred-media.css' | asset_url | stylesheet_tag }}
  {% unless product.has_only_default_variant %}
    {{ 'component-product-variant-picker.css' | asset_url | stylesheet_tag }}
    {{ 'component-swatch-input.css' | asset_url | stylesheet_tag }}
    {{ 'component-swatch.css' | asset_url | stylesheet_tag }}
  {% endunless %}
  {%- if product.quantity_price_breaks_configured? -%}
    {{ 'component-volume-pricing.css' | asset_url | stylesheet_tag }}
  {%- endif -%}

  {%- style -%}
    .section-{{ section.id }}-padding {
      padding-top: {{ section.settings.padding_top | times: 0.75 | round: 0 }}px;
      padding-bottom: {{ section.settings.padding_bottom | times: 0.75 | round: 0 }}px;
    }

    @media screen and (min-width: 750px) {
      .section-{{ section.id }}-padding {
        padding-top: {{ section.settings.padding_top }}px;
        padding-bottom: {{ section.settings.padding_bottom }}px;
      }
    }
  {%- endstyle -%}

  <script src="{{ 'product-info.js' | asset_url }}" defer="defer"></script>
  <script src="{{ 'product-form.js' | asset_url }}" defer="defer"></script>
  {%- if product.quantity_price_breaks_configured? -%}
    <script src="{{ 'show-more.js' | asset_url }}" defer="defer"></script>
    <script src="{{ 'price-per-item.js' | asset_url }}" defer="defer"></script>
  {%- endif -%}

  {% if section.settings.image_zoom == 'hover' %}
    <script id="EnableZoomOnHover-main" src="{{ 'magnify.js' | asset_url }}" defer="defer"></script>
  {% endif %}
  {%- if request.design_mode -%}
    <script src="{{ 'theme-editor.js' | asset_url }}" defer="defer"></script>
  {%- endif -%}

  {%- assign first_3d_model = product.media | where: 'media_type', 'model' | first -%}
  {%- if first_3d_model -%}
    {{ 'component-product-model.css' | asset_url | stylesheet_tag }}
    <link
      id="ModelViewerStyle"
      rel="stylesheet"
      href="https://cdn.shopify.com/shopifycloud/model-viewer-ui/assets/v1.0/model-viewer-ui.css"
      media="print"
      onload="this.media='all'"
    >
    <link
      id="ModelViewerOverride"
      rel="stylesheet"
      href="{{ 'component-model-viewer-ui.css' | asset_url }}"
      media="print"
      onload="this.media='all'"
    >
  {%- endif -%}

  {% assign variant_images = product.images | where: 'attached_to_variant?', true | map: 'src' %}
  <div class="page-width">
    <div class="product product--{{ section.settings.media_size }} product--{{ section.settings.media_position }} product--{{ section.settings.gallery_layout }} product--mobile-{{ section.settings.mobile_thumbnails }} grid grid--1-col {% if product.media.size > 0 %}grid--2-col-tablet{% else %}product--no-media{% endif %}">
    <div class="grid__item product__media-wrapper{% if section.settings.media_position == 'right' %} medium-hide large-up-hide{% endif %}">
      {% render 'product-media-gallery', variant_images: variant_images %}
    </div>
    <div class="product__info-wrapper grid__item{% if settings.page_width > 1400 and section.settings.media_size == "small" %} product__info-wrapper--extra-padding{% endif %}{% if settings.animations_reveal_on_scroll %} scroll-trigger animate--slide-in{% endif %}">
      <product-info
        id="ProductInfo-{{ section.id }}"
        data-section="{{ section.id }}"
        data-url="{{ product.url }}"
        class="product__info-container{% if section.settings.enable_sticky_info %} product__column-sticky{% endif %}"
      >
        {%- assign product_form_id = 'product-form-' | append: section.id -%}

        {%- for block in section.blocks -%}
          {%- case block.type -%}
            {%- when '@app' -%}
              {% render block %}
            {%- when 'text' -%}
              <p
                class="product__text inline-richtext{% if block.settings.text_style == 'uppercase' %} caption-with-letter-spacing{% elsif block.settings.text_style == 'subtitle' %} subtitle{% endif %}"
                {{ block.shopify_attributes }}
              >
                {{- block.settings.text -}}
              </p>
            {%- when 'title' -%}
              <div class="product__title" {{ block.shopify_attributes }}>
                <h1>{{ product.title | escape }}</h1>
                <a href="{{ product.url }}" class="product__title">
                  <h2 class="h1">
                    {{ product.title | escape }}
                  </h2>
                </a>
              </div>
            {%- when 'price' -%}
              <div class="no-js-hidden" id="price-{{ section.id }}" role="status" {{ block.shopify_attributes }}>
                {%- render 'price',
                  product: product,
                  use_variant: true,
                  show_badges: true,
                  price_class: 'price--large'
                -%}
              </div>
              {%- if product.quantity_price_breaks_configured? -%}
                <div class="volume-pricing-note" id="Volume-Note-{{ section.id }}">
                  <span>{{ 'products.product.volume_pricing.note' | t }}</span>
                </div>
              {%- endif -%}
                <div class="product__tax caption rte">
                  {%- if cart.taxes_included -%}
                    {{ 'products.product.include_taxes' | t }}
                  {%- endif -%}
                  {%- if shop.shipping_policy.body != blank -%}
                    {{ 'products.product.shipping_policy_html' | t: link: shop.shipping_policy.url }}
                  {%- endif -%}
                </div>
              <div {{ block.shopify_attributes }}>
                {%- assign product_form_installment_id = 'product-form-installment-' | append: section.id -%}
                {%- form 'product', product, id: product_form_installment_id, class: 'installment caption-large' -%}
                  <input type="hidden" name="id" value="{{ product.selected_or_first_available_variant.id }}">
                  {{ form | payment_terms }}
                {%- endform -%}
              </div>
            {%- when 'inventory' -%}
              <p
                class="product__inventory no-js-hidden{% if block.settings.text_style == 'uppercase' %} caption-with-letter-spacing{% elsif block.settings.text_style == 'subtitle' %} subtitle{% endif %}{% if product.selected_or_first_available_variant.inventory_management != 'shopify' %} visibility-hidden{% endif %}"
                {{ block.shopify_attributes }}
                id="Inventory-{{ section.id }}"
                role="status"
              >
                {%- if product.selected_or_first_available_variant.inventory_management == 'shopify' -%}
                  {%- if product.selected_or_first_available_variant.inventory_quantity > 0 -%}
                    {%- if product.selected_or_first_available_variant.inventory_quantity <= block.settings.inventory_threshold -%}
                      <svg width="15" height="15" aria-hidden="true">
                        <circle cx="7.5" cy="7.5" r="7.5" fill="rgb(238,148,65, 0.3)"/>
                        <circle cx="7.5" cy="7.5" r="5" stroke="rgb(255, 255, 255)" stroke-width="1" fill="rgb(238,148,65)"/>
                      </svg>
                      {%- if block.settings.show_inventory_quantity -%}
                        {{- 'products.product.inventory_low_stock_show_count' | t: quantity: product.selected_or_first_available_variant.inventory_quantity -}}
                      {%- else -%}
                        {{- 'products.product.inventory_low_stock' | t -}}
                      {%- endif -%}
                    {%- else -%}
                      <svg width="15" height="15" aria-hidden="true">
                        <circle cx="7.5" cy="7.5" r="7.5" fill="rgb(62,214,96, 0.3)"/>
                        <circle cx="7.5" cy="7.5" r="5" stroke="rgb(255, 255, 255)" stroke-width="1" fill="rgb(62,214,96)"/>
                      </svg>
                      {%- if block.settings.show_inventory_quantity -%}
                        {{- 'products.product.inventory_in_stock_show_count' | t: quantity: product.selected_or_first_available_variant.inventory_quantity -}}
                      {%- else -%}
                          {{- 'products.product.inventory_in_stock' | t -}}
                      {%- endif -%}
                    {%- endif -%}
                  {%- else -%}
                    {%- if product.selected_or_first_available_variant.inventory_policy == 'continue' -%}
                      <svg width="15" height="15" aria-hidden="true">
                        <circle cx="7.5" cy="7.5" r="7.5" fill="rgb(62,214,96, 0.3)"/>
                        <circle cx="7.5" cy="7.5" r="5" stroke="rgb(255, 255, 255)" stroke-width="1" fill="rgb(62,214,96)"/>
                      </svg>
                      {{- 'products.product.inventory_out_of_stock_continue_selling' | t -}}
                    {%- else -%}
                      <svg width="15" height="15" aria-hidden="true">
                        <circle cx="7.5" cy="7.5" r="7.5" fill="rgb(200,200,200, 0.3)"/>
                        <circle cx="7.5" cy="7.5" r="5" stroke="rgb(255, 255, 255)" stroke-width="1" fill="rgb(200,200,200)"/>
                      </svg>
                      {{- 'products.product.inventory_out_of_stock' | t -}}
                    {%- endif -%}
                  {%- endif -%}
                {%- endif -%}
              </p>
            {%- when 'description' -%}
              {%- if product.description != blank -%}
                <div class="product__description rte quick-add-hidden" {{ block.shopify_attributes }}>
                  {{ product.description }}
                </div>
              {%- endif -%}
            {%- when 'sku' -%}
              <p
                class="product__sku no-js-hidden{% if block.settings.text_style == 'uppercase' %} caption-with-letter-spacing{% elsif block.settings.text_style == 'subtitle' %} subtitle{% endif %}{% if product.selected_or_first_available_variant.sku.size == 0 %} visibility-hidden{% endif %}"
                id="Sku-{{ section.id }}"
                role="status"
                {{ block.shopify_attributes }}
              >
                <span class="visually-hidden">{{ 'products.product.sku' | t }}:</span> {{- product.selected_or_first_available_variant.sku -}}
              </p>
            {%- when 'custom_liquid' -%}
              {{ block.settings.custom_liquid }}
            {%- when 'collapsible_tab' -%}
              <div class="product__accordion accordion quick-add-hidden" {{ block.shopify_attributes }}>
                <details id="Details-{{ block.id }}-{{ section.id }}">
                  <summary>
                    <div class="summary__title">
                      {% render 'icon-accordion', icon: block.settings.icon %}
                      <h2 class="h4 accordion__title inline-richtext">
                        {{ block.settings.heading | default: block.settings.page.title }}
                      </h2>
                    </div>
                    {% render 'icon-caret' %}
                  </summary>
                  <div class="accordion__content rte" id="ProductAccordion-{{ block.id }}-{{ section.id }}">
                    {{ block.settings.content }}
                    {{ block.settings.page.content }}
                  </div>
                </details>
              </div>
            {%- when 'quantity_selector' -%}
              <div
                id="Quantity-Form-{{ section.id }}"
                class="product-form__input product-form__quantity{% if settings.inputs_shadow_vertical_offset != 0 and settings.inputs_shadow_vertical_offset < 0 %} product-form__quantity-top{% endif %}"
                {{ block.shopify_attributes }}
              >
                {% comment %} TODO: enable theme-check once `item_count_for_variant` is accepted as valid filter {% endcomment %}
                {% # theme-check-disable %}
                {%- assign cart_qty = cart | item_count_for_variant: product.selected_or_first_available_variant.id -%}
                {% # theme-check-enable %}

                {% comment %} Calculate default location inventory {% endcomment %}
                {%- liquid
                  assign default_location_inventory = product.selected_or_first_available_variant.inventory_quantity

                  if product.selected_or_first_available_variant.store_availabilities.size > 0
                    assign default_location = product.selected_or_first_available_variant.store_availabilities.first
                    if default_location.available and default_location.inventory_quantity
                      assign default_location_inventory = default_location.inventory_quantity
                    endif
                  endif
                -%}

                <label class="quantity__label form__label" for="Quantity-{{ section.id }}">
                  {{ 'products.product.quantity.label' | t }}
                  <span class="quantity__rules-cart no-js-hidden{% if cart_qty == 0 %} hidden{% endif %}">
                    {%- render 'loading-spinner' -%}
                    <span>({{- 'products.product.quantity.in_cart_html' | t: quantity: cart_qty -}})</span>
                  </span>
                </label>
                <div class="price-per-item__container">
                  <quantity-input class="quantity" data-url="{{ product.url }}" data-section="{{ section.id }}">
                    <button class="quantity__button no-js-hidden" name="minus" type="button">
                      <span class="visually-hidden">
                        {{- 'products.product.quantity.decrease' | t: product: product.title | escape -}}
                      </span>
                      {% render 'icon-minus' %}
                    </button>
                    <input
                      class="quantity__input"
                      type="number"
                      name="quantity"
                      id="Quantity-{{ section.id }}"
                      data-cart-quantity="{{ cart_qty }}"
                      data-quantity-variant-id="{{ product.selected_or_first_available_variant.id }}"
                      data-inventory-management="{{ product.selected_or_first_available_variant.inventory_management }}"
                      data-inventory-policy="{{ product.selected_or_first_available_variant.inventory_policy }}"
                      data-inventory-quantity="{{ default_location_inventory }}"
                      data-total-inventory-quantity="{{ product.selected_or_first_available_variant.inventory_quantity }}"
                      data-safety-stock-check="true"
                      data-min="{{ product.selected_or_first_available_variant.quantity_rule.min }}"
                      min="{{ product.selected_or_first_available_variant.quantity_rule.min }}"
                      {% if product.selected_or_first_available_variant.quantity_rule.max != null %}
                        data-max="{{ product.selected_or_first_available_variant.quantity_rule.max }}"
                        max="{{ product.selected_or_first_available_variant.quantity_rule.max }}"
                      {% elsif product.selected_or_first_available_variant.inventory_management == 'shopify' and product.selected_or_first_available_variant.inventory_policy != 'continue' and default_location_inventory != null %}
                        max="{{ default_location_inventory }}"
                      {% endif %}
                      step="{{ product.selected_or_first_available_variant.quantity_rule.increment }}"
                      value="{{ product.selected_or_first_available_variant.quantity_rule.min }}"
                      form="{{ product_form_id }}"
                    />
                    <button class="quantity__button no-js-hidden" name="plus" type="button">
                      <span class="visually-hidden">
                        {{- 'products.product.quantity.increase' | t: product: product.title | escape -}}
                      </span>
                      {% render 'icon-plus' %}
                    </button>
                  </quantity-input>
                  {%- liquid
                    assign volume_pricing_array = product.selected_or_first_available_variant.quantity_price_breaks | sort: 'quantity' | reverse
                    assign current_qty_for_volume_pricing = cart_qty | plus: product.selected_or_first_available_variant.quantity_rule.min
                    if cart_qty > 0
                      assign current_qty_for_volume_pricing = cart_qty | plus: product.selected_or_first_available_variant.quantity_rule.increment
                    endif

                  -%}
                  {%- if product.quantity_price_breaks_configured? -%}
                    <price-per-item class="no-js-hidden" id="Price-Per-Item-{{ section.id }}" data-section-id="{{ section.id }}" data-variant-id="{{ product.selected_or_first_available_variant.id }}">
                      {%- if product.selected_or_first_available_variant.quantity_price_breaks.size > 0 -%}
                        {%- assign variant_price_compare = product.selected_or_first_available_variant.compare_at_price -%}
                        <div class="price-per-item">
                          {%- if variant_price_compare -%}
                            <dl class="price-per-item--current">
                              <dt class="visually-hidden">
                                {{ 'products.product.price.regular_price' | t }}
                              </dt>
                              <dd>
                                <s class="variant-item__old-price">
                                  {{ variant_price_compare | money_with_currency }}
                                </s>
                              </dd>
                            </dl>
                          {%- endif -%}
                          {%- if current_qty_for_volume_pricing < volume_pricing_array.last.minimum_quantity -%}
                            {%- assign variant_price = product.selected_or_first_available_variant.price | money_with_currency -%}
                            <span class="price-per-item--current">{{ 'products.product.volume_pricing.price_at_each' | t: price: variant_price }}</span>
                          {%- else -%}
                            {%- for price_break in volume_pricing_array -%}
                              {%- if current_qty_for_volume_pricing >= price_break.minimum_quantity -%}
                                {%- assign price_break_price = price_break.price | money_with_currency -%}
                                <span class="price-per-item--current">{{ 'products.product.volume_pricing.price_at_each' | t: price: price_break_price }}</span>
                                {%- break -%}
                              {%- endif -%}
                            {%- endfor -%}
                          {%- endif -%}
                        </div>
                      {%- else -%}
                        {%- assign variant_price = product.selected_or_first_available_variant.price | money_with_currency -%}
                        {%- assign variant_price_compare = product.selected_or_first_available_variant.compare_at_price -%}
                        <div class="price-per-item">
                          {%- if variant_price_compare -%}
                            <dl class="price-per-item--current">
                              <dt class="visually-hidden">
                                {{ 'products.product.price.regular_price' | t }}
                              </dt>
                              <dd>
                                <s class="variant-item__old-price">
                                  {{ variant_price_compare | money_with_currency }}
                                </s>
                              </dd>
                              <dt class="visually-hidden">
                                {{ 'products.product.price.sale_price' | t }}
                              </dt>
                              <dd>
                                <span class="price-per-item--current">{{ 'products.product.volume_pricing.price_at_each' | t: price: variant_price }}</span>
                              </dd>
                            </dl>
                          {%- else -%}
                            <span class="price-per-item--current">{{ 'products.product.volume_pricing.price_at_each' | t: price: variant_price }}</span>
                          {%- endif -%}
                        </div>
                      {%- endif -%}
                    </price-per-item>
                  {%- endif -%}
                </div>
                <div class="quantity__rules caption no-js-hidden" id="Quantity-Rules-{{ section.id }}">
                  {%- if product.selected_or_first_available_variant.quantity_rule.increment > 1 -%}
                    <span class="divider">
                      {{-
                        'products.product.quantity.multiples_of'
                        | t: quantity: product.selected_or_first_available_variant.quantity_rule.increment
                      -}}
                    </span>
                  {%- endif -%}
                  {%- if product.selected_or_first_available_variant.quantity_rule.min > 1 -%}
                    <span class="divider">
                      {{-
                        'products.product.quantity.minimum_of'
                        | t: quantity: product.selected_or_first_available_variant.quantity_rule.min
                      -}}
                    </span>
                  {%- endif -%}
                  {%- if product.selected_or_first_available_variant.quantity_rule.max != null -%}
                    <span class="divider">
                      {{-
                        'products.product.quantity.maximum_of'
                        | t: quantity: product.selected_or_first_available_variant.quantity_rule.max
                      -}}
                    </span>
                  {%- endif -%}
                </div>
                {%- if product.quantity_price_breaks_configured? -%}
                  <volume-pricing class="parent-display no-js-hidden" id="Volume-{{ section.id }}">
                    {%- if product.selected_or_first_available_variant.quantity_price_breaks.size > 0 -%}
                      <span class="caption-large">{{ 'products.product.volume_pricing.title' | t }}</span>
                      <ul class="list-unstyled no-js-hidden">
                        <li>
                          <span>{{ product.selected_or_first_available_variant.quantity_rule.min }}+</span>
                          {%- assign price = product.selected_or_first_available_variant.price | money_with_currency -%}
                          <span data-text="{{ 'products.product.volume_pricing.price_at_each' | t: price: variant_price }}">{{ 'sections.quick_order_list.each' | t: money: price }}</span>
                        </li>
                        {%- for price_break in product.selected_or_first_available_variant.quantity_price_breaks -%}
                          {%- assign price_break_price = price_break.price | money_with_currency -%}
                          <li class="{%- if forloop.index >= 3 -%}show-more-item hidden{%- endif -%}">
                            <span>{{ price_break.minimum_quantity }}<span aria-hidden="true">+</span></span>
                            <span data-text="{{ 'products.product.volume_pricing.price_at_each' | t: price: price_break_price }}">{{ 'sections.quick_order_list.each' | t: money: price_break_price }}</span>
                          </li>
                        {%- endfor -%}
                      </ul>
                      {%- if product.selected_or_first_available_variant.quantity_price_breaks.size >= 3 -%}
                        <show-more-button>
                          <button
                            class="button-show-more link underlined-link"
                            id="Show-More-{{ section.id }}"
                            type="button"
                          >
                            <span class="label-show-more label-text"
                              ><span aria-hidden="true">+ </span>{{ 'products.facets.show_more' | t }}
                            </span>
                          </button>
                        </show-more-button>
                    {%- endif -%}
                  {%- endif -%}
                  </volume-pricing>
                {%- endif -%}
              </div>
            {%- when 'popup' -%}
              <modal-opener
                class="product-popup-modal__opener no-js-hidden quick-add-hidden"
                data-modal="#PopupModal-{{ block.id }}"
                {{ block.shopify_attributes }}
              >
                <button
                  id="ProductPopup-{{ block.id }}"
                  class="product-popup-modal__button link"
                  type="button"
                  aria-haspopup="dialog"
                >
                  {{ block.settings.text | default: block.settings.page.title }}
                </button>
              </modal-opener>
              <a href="{{ block.settings.page.url }}" class="product-popup-modal__button link no-js">
                {{- block.settings.text -}}
              </a>
            {%- when 'share' -%}
              {% assign share_url = product.selected_variant.url | default: product.url | prepend: request.origin %}
              {% render 'share-button',
                block: block,
                share_link: share_url
              %}

            {%- when 'variant_picker' -%}
              {% render 'product-variant-picker', product: product, block: block, product_form_id: product_form_id %}
            {%- when 'buy_buttons' -%}
              {%- render 'buy-buttons', block: block, product: product, product_form_id: product_form_id, section_id: section.id, show_pickup_availability: true -%}
            {%- when 'rating' -%}
              {%- if product.metafields.reviews.rating.value != blank -%}
                {% liquid
                  assign rating_decimal = 0
                  assign decimal = product.metafields.reviews.rating.value.rating | modulo: 1
                  if decimal >= 0.3 and decimal <= 0.7
                    assign rating_decimal = 0.5
                  elsif decimal > 0.7
                    assign rating_decimal = 1
                  endif
                %}
                <div
                  class="rating"
                  role="img"
                  aria-label="{{ 'accessibility.star_reviews_info' | t: rating_value: product.metafields.reviews.rating.value, rating_max: product.metafields.reviews.rating.value.scale_max }}"
                >
                  <span
                    aria-hidden="true"
                    class="rating-star"
                    style="--rating: {{ product.metafields.reviews.rating.value.rating | floor }}; --rating-max: {{ product.metafields.reviews.rating.value.scale_max }}; --rating-decimal: {{ rating_decimal }};"
                  ></span>
                </div>
                <p class="rating-text caption">
                  <span aria-hidden="true">
                    {{- product.metafields.reviews.rating.value }} /
                    {{ product.metafields.reviews.rating.value.scale_max -}}
                  </span>
                </p>
                <p class="rating-count caption">
                  <span aria-hidden="true">({{ product.metafields.reviews.rating_count }})</span>
                  <span class="visually-hidden">
                    {{- product.metafields.reviews.rating_count }}
                    {{ 'accessibility.total_reviews' | t -}}
                  </span>
                </p>
              {%- endif -%}
            {%- when 'complementary' -%}
              <product-recommendations class="complementary-products quick-add-hidden no-js-hidden{% if block.settings.make_collapsible_row %} is-accordion{% endif %}{% if block.settings.enable_quick_add %} complementary-products-contains-quick-add{% endif %}" data-url="{{ routes.product_recommendations_url }}?section_id={{ section.id }}&product_id={{ product.id }}&limit={{ block.settings.product_list_limit }}&intent=complementary">
                {%- if recommendations.performed and recommendations.products_count > 0 -%}
                  <aside aria-label="{{ 'accessibility.complementary_products' | t }}" {{ block.shopify_attributes }}{% if block.settings.make_collapsible_row %} class="product__accordion accordion"{% endif %}>
                    <div class="complementary-products__container">
                      {%- if block.settings.make_collapsible_row -%}
                        <details id="Details-{{ block.id }}-{{ section.id }}" open>
                          <summary>
                      {%- endif %}
                      <div class="summary__title">
                        {%- if block.settings.make_collapsible_row -%}
                          {% render 'icon-accordion', icon: block.settings.icon %}
                          <h2 class="h4 accordion__title">{{ block.settings.block_heading }}</h2>
                        {%- else -%}
                          <h2 class="h3 accordion__title">{{ block.settings.block_heading }}</h2>
                        {%- endif -%}
                      </div>
                      {%- if block.settings.make_collapsible_row -%}
                          {% render 'icon-caret' %}
                        </summary>
                      {%- endif -%}
                      <slideshow-component class="slider-mobile-gutter">
                        {%- assign number_of_slides = recommendations.products_count | plus: 0.0 | divided_by: block.settings.products_per_page | ceil -%}
                        <div id="Slider-{{ block.id }}" class="contains-card contains-card--product complementary-slider grid grid--1-col slider slider--everywhere" role="list"{% if number_of_slides > 1 %} aria-label="{{ 'general.slider.name' | t }}"{% endif %}>
                          {%- for i in (1..number_of_slides) -%}
                            <div id="Slide-{{ block.id }}-{{ forloop.index }}" class="complementary-slide complementary-slide--{{ settings.card_style }} grid__item slider__slide slideshow__slide" tabindex="-1" role="group"{% if number_of_slides > 1 %} aria-roledescription="{{ 'sections.slideshow.slide' | t }}" aria-label="{{ forloop.index }} {{ 'general.slider.of' | t }} {{ forloop.length }}"{% endif %}>
                              <ul class="list-unstyled" role="list">
                                {%- for product in recommendations.products limit: block.settings.products_per_page offset: continue -%}
                                  <li>
                                    {% render 'card-product',
                                      card_product: product,
                                      media_aspect_ratio: block.settings.image_ratio,
                                      show_secondary_image: false,
                                      lazy_load: false,
                                      show_quick_add: block.settings.enable_quick_add,
                                      section_id: section.id,
                                      horizontal_class: true,
                                      horizontal_quick_add: true
                                    %}
                                  </li>
                                {%- endfor -%}
                              </ul>
                            </div>
                          {%- endfor -%}
                        </div>
                        {%- if number_of_slides > 1 -%}
                          <div class="slider-buttons no-js-hidden">
                            <button type="button" class="slider-button slider-button--prev" name="previous" aria-label="{{ 'general.slider.previous_slide' | t }}">{% render 'icon-caret' %}</button>
                            <div class="slider-counter slider-counter--{{ block.settings.pagination_style }}{% if block.settings.pagination_style == 'counter' or block.settings.pagination_style == 'numbers' %} caption{% endif %}">
                              {%- if block.settings.pagination_style == 'counter' -%}
                                <span class="slider-counter--current">1</span>
                                <span aria-hidden="true"> / </span>
                                <span class="visually-hidden">{{ 'general.slider.of' | t }}</span>
                                <span class="slider-counter--total">{{ number_of_slides }}</span>
                              {%- else -%}
                                <div class="slideshow__control-wrapper">
                                  {%- for i in (1..number_of_slides) -%}
                                    <button class="slider-counter__link slider-counter__link--{{ block.settings.pagination_style }} link" aria-label="{{ 'sections.slideshow.load_slide' | t }} {{ forloop.index }} {{ 'general.slider.of' | t }} {{ forloop.length }}" aria-controls="Slider-{{ block.id }}">
                                      {%- if block.settings.pagination_style == 'numbers' -%}{{ forloop.index }}{% else %}<span class="dot"></span>{%- endif -%}
                                    </button>
                                  {%- endfor -%}
                                </div>
                              {%- endif -%}
                            </div>
                            <button type="button" class="slider-button slider-button--next" name="next" aria-label="{{ 'general.slider.next_slide' | t }}">{% render 'icon-caret' %}</button>
                          </div>
                        {%- endif -%}
                      </slideshow-component>
                      {%- if block.settings.make_collapsible_row -%}
                        </details>
                      {%- endif -%}
                    </div>
                  </aside>
                {%- endif -%}
                {{ 'component-card.css' | asset_url | stylesheet_tag }}
                {{ 'component-complementary-products.css' | asset_url | stylesheet_tag }}
                {%- if block.settings.enable_quick_add -%}
                  {{ 'quick-add.css' | asset_url | stylesheet_tag }}
                  <script src="{{ 'quick-add.js' | asset_url }}" defer="defer"></script>
                {%- endif -%}
              </product-recommendations>
            {%- when 'icon-with-text' -%}
              {% render 'icon-with-text',
                block: block
              %}
          {%- endcase -%}
        {%- endfor -%}
        <a href="{{ product.url }}" class="link product__view-details animate-arrow">
          {{ 'products.product.view_full_details' | t }}
          {% render 'icon-arrow' %}
        </a>
      </product-info>
    </div>
    {%- if section.settings.media_position == 'right' -%}
      {% comment %} Duplicate gallery to display after product content on tablet/desktop breakpoint {% endcomment %}
      <div class="grid__item product__media-wrapper small-hide">
        {% render 'product-media-gallery', variant_images: variant_images, is_duplicate: true %}
      </div>
    {%- endif -%}
  </div>

  {% render 'product-media-modal' variant_images: variant_images %}

  {% assign popups = section.blocks | where: 'type', 'popup' %}
  {%- for block in popups -%}
    <modal-dialog id="PopupModal-{{ block.id }}" class="product-popup-modal" {{ block.shopify_attributes }}>
      <div
        role="dialog"
        aria-label="{{ block.settings.text }}"
        aria-modal="true"
        class="product-popup-modal__content"
        tabindex="-1"
      >
        <button
          id="ModalClose-{{ block.id }}"
          type="button"
          class="product-popup-modal__toggle"
          aria-label="{{ 'accessibility.close' | t }}"
        >
          {% render 'icon-close' %}
        </button>
        <div class="product-popup-modal__content-info">
          <h1 class="h2">{{ block.settings.page.title }}</h1>
          {{ block.settings.page.content }}
        </div>
      </div>
    </modal-dialog>
  {%- endfor -%}

  {%- if product.media.size > 0 -%}
    <script src="{{ 'product-modal.js' | asset_url }}" defer="defer"></script>
    <script src="{{ 'media-gallery.js' | asset_url }}" defer="defer"></script>
  {%- endif -%}

  {%- if first_3d_model -%}
    <script type="application/json" id="ProductJSON-{{ product.id }}">
      {{ product.media | where: 'media_type', 'model' | json }}
    </script>
    <script src="{{ 'product-model.js' | asset_url }}" defer></script>
  {%- endif -%}

  <script>
    document.addEventListener('DOMContentLoaded', function () {
      function isIE() {
        const ua = window.navigator.userAgent;
        const msie = ua.indexOf('MSIE ');
        const trident = ua.indexOf('Trident/');

        return msie > 0 || trident > 0;
      }

      if (!isIE()) return;
      const hiddenInput = document.querySelector('#{{ product_form_id }} input[name="id"]');
      const noScriptInputWrapper = document.createElement('div');
      const variantPicker = document.querySelector('variant-selects[data-section="{{ section.id }}"]');
      noScriptInputWrapper.innerHTML = document.querySelector(
        '.product-form__noscript-wrapper-{{ section.id }}'
      ).textContent;
      variantPicker.outerHTML = noScriptInputWrapper.outerHTML;

      document.querySelector('#Variants-{{ section.id }}').addEventListener('change', function (event) {
        hiddenInput.value = event.currentTarget.value;
      });
    });
  </script>

  {%- liquid
    if product.selected_or_first_available_variant.featured_media
      assign seo_media = product.selected_or_first_available_variant.featured_media
    else
      assign seo_media = product.featured_media
    endif
  -%}

  <script type="application/ld+json">
    {
      "@context": "http://schema.org/",
      "@type": "Product",
      "name": {{ product.title | json }},
      "url": {{ request.origin | append: product.url | json }},
      {% if seo_media -%}
        "image": [
          {{ seo_media | image_url: width: 1920 | prepend: "https:" | json }}
        ],
      {%- endif %}
      "description": {{ product.description | strip_html | json }},
      {% if product.selected_or_first_available_variant.sku != blank -%}
        "sku": {{ product.selected_or_first_available_variant.sku | json }},
      {%- endif %}
      "brand": {
        "@type": "Brand",
        "name": {{ product.vendor | json }}
      },
      "offers": [
        {%- for variant in product.variants -%}
          {
            "@type" : "Offer",
            {%- if variant.sku != blank -%}
              "sku": {{ variant.sku | json }},
            {%- endif -%}
            {%- if variant.barcode.size == 12 -%}
              "gtin12": {{ variant.barcode }},
            {%- endif -%}
            {%- if variant.barcode.size == 13 -%}
              "gtin13": {{ variant.barcode }},
            {%- endif -%}
            {%- if variant.barcode.size == 14 -%}
              "gtin14": {{ variant.barcode }},
            {%- endif -%}
            "availability" : "http://schema.org/{% if variant.available %}InStock{% else %}OutOfStock{% endif %}",
            "price" : {{ variant.price | divided_by: 100.00 | json }},
            "priceCurrency" : {{ cart.currency.iso_code | json }},
            "url" : {{ request.origin | append: variant.url | json }}
          }{% unless forloop.last %},{% endunless %}
        {%- endfor -%}
      ]
    }
  </script>
  </div>
</section>

{% schema %}
{
  "name": "t:sections.main-product.name",
  "tag": "section",
  "class": "section",
  "blocks": [
    {
      "type": "@app"
    },
    {
      "type": "text",
      "name": "t:sections.main-product.blocks.text.name",
      "settings": [
        {
          "type": "inline_richtext",
          "id": "text",
          "default": "Text block",
          "label": "t:sections.main-product.blocks.text.settings.text.label"
        },
        {
          "type": "select",
          "id": "text_style",
          "options": [
            {
              "value": "body",
              "label": "t:sections.main-product.blocks.text.settings.text_style.options__1.label"
            },
            {
              "value": "subtitle",
              "label": "t:sections.main-product.blocks.text.settings.text_style.options__2.label"
            },
            {
              "value": "uppercase",
              "label": "t:sections.main-product.blocks.text.settings.text_style.options__3.label"
            }
          ],
          "default": "body",
          "label": "t:sections.main-product.blocks.text.settings.text_style.label"
        }
      ]
    },
    {
      "type": "title",
      "name": "t:sections.main-product.blocks.title.name",
      "limit": 1
    },
    {
      "type": "price",
      "name": "t:sections.main-product.blocks.price.name",
      "limit": 1
    },
    {
      "type": "sku",
      "name": "t:sections.main-product.blocks.sku.name",
      "limit": 1,
      "settings": [
        {
          "type": "select",
          "id": "text_style",
          "options": [
            {
              "value": "body",
              "label": "t:sections.main-product.blocks.sku.settings.text_style.options__1.label"
            },
            {
              "value": "subtitle",
              "label": "t:sections.main-product.blocks.sku.settings.text_style.options__2.label"
            },
            {
              "value": "uppercase",
              "label": "t:sections.main-product.blocks.sku.settings.text_style.options__3.label"
            }
          ],
          "default": "body",
          "label": "t:sections.main-product.blocks.sku.settings.text_style.label"
        }
      ]
    },
    {
      "type": "inventory",
      "name": "t:sections.main-product.blocks.inventory.name",
      "limit": 1,
      "settings": [
        {
          "type": "select",
          "id": "text_style",
          "options": [
            {
              "value": "body",
              "label": "t:sections.main-product.blocks.inventory.settings.text_style.options__1.label"
            },
            {
              "value": "subtitle",
              "label": "t:sections.main-product.blocks.inventory.settings.text_style.options__2.label"
            },
            {
              "value": "uppercase",
              "label": "t:sections.main-product.blocks.inventory.settings.text_style.options__3.label"
            }
          ],
          "default": "body",
          "label": "t:sections.main-product.blocks.inventory.settings.text_style.label"
        },
        {
          "type": "range",
          "id": "inventory_threshold",
          "label": "t:sections.main-product.blocks.inventory.settings.inventory_threshold.label",
          "min": 0,
          "max": 100,
          "step": 1,
          "info": "t:sections.main-product.blocks.inventory.settings.inventory_threshold.info",
          "default": 10
        },
        {
          "type": "checkbox",
          "id": "show_inventory_quantity",
          "label": "t:sections.main-product.blocks.inventory.settings.show_inventory_quantity.label",
          "default": true
        }
      ]
    },
    {
      "type": "quantity_selector",
      "name": "t:sections.main-product.blocks.quantity_selector.name",
      "limit": 1
    },
    {
      "type": "variant_picker",
      "name": "t:sections.main-product.blocks.variant_picker.name",
      "limit": 1,
      "settings": [
        {
          "type": "select",
          "id": "picker_type",
          "options": [
            {
              "value": "dropdown",
              "label": "t:sections.main-product.blocks.variant_picker.settings.picker_type.options__1.label"
            },
            {
              "value": "button",
              "label": "t:sections.main-product.blocks.variant_picker.settings.picker_type.options__2.label"
            }
          ],
          "default": "button",
          "label": "t:sections.main-product.blocks.variant_picker.settings.picker_type.label"
        },
        {
          "id": "swatch_shape",
          "label": "t:sections.main-product.blocks.variant_picker.settings.swatch_shape.label",
          "type": "select",
          "info": "t:sections.main-product.blocks.variant_picker.settings.swatch_shape.info",
          "options": [
            {
              "value": "circle",
              "label": "t:sections.main-product.blocks.variant_picker.settings.swatch_shape.options__1.label"
            },
            {
              "value": "square",
              "label": "t:sections.main-product.blocks.variant_picker.settings.swatch_shape.options__2.label"
            },
            {
              "value": "none",
              "label": "t:sections.main-product.blocks.variant_picker.settings.swatch_shape.options__3.label"
            }
          ],
          "default": "circle"
        }
      ]
    },
    {
      "type": "buy_buttons",
      "name": "t:sections.main-product.blocks.buy_buttons.name",
      "limit": 1,
      "settings": [
        {
          "type": "checkbox",
          "id": "show_dynamic_checkout",
          "default": true,
          "label": "t:sections.main-product.blocks.buy_buttons.settings.show_dynamic_checkout.label",
          "info": "t:sections.main-product.blocks.buy_buttons.settings.show_dynamic_checkout.info"
        },
        {
          "type": "checkbox",
          "id": "show_gift_card_recipient",
          "default": true,
          "label": "t:sections.main-product.blocks.buy_buttons.settings.show_gift_card_recipient.label",
          "info": "t:sections.main-product.blocks.buy_buttons.settings.show_gift_card_recipient.info"
        }
      ]
    },
    {
      "type": "description",
      "name": "t:sections.main-product.blocks.description.name",
      "limit": 1
    },
    {
      "type": "share",
      "name": "t:sections.main-product.blocks.share.name",
      "limit": 1,
      "settings": [
        {
          "type": "text",
          "id": "share_label",
          "label": "t:sections.main-product.blocks.share.settings.text.label",
          "default": "Share"
        },
        {
          "type": "paragraph",
          "content": "t:sections.main-product.blocks.share.settings.featured_image_info.content"
        },
        {
          "type": "paragraph",
          "content": "t:sections.main-product.blocks.share.settings.title_info.content"
        }
      ]
    },
    {
      "type": "custom_liquid",
      "name": "t:sections.custom-liquid.name",
      "settings": [
        {
          "type": "liquid",
          "id": "custom_liquid",
          "label": "t:sections.custom-liquid.settings.custom_liquid.label",
          "info": "t:sections.custom-liquid.settings.custom_liquid.info"
        }
      ]
    },
    {
      "type": "collapsible_tab",
      "name": "t:sections.main-product.blocks.collapsible_tab.name",
      "settings": [
        {
          "type": "text",
          "id": "heading",
          "default": "Collapsible row",
          "info": "t:sections.main-product.blocks.collapsible_tab.settings.heading.info",
          "label": "t:sections.main-product.blocks.collapsible_tab.settings.heading.label"
        },
        {
          "type": "select",
          "id": "icon",
          "options": [
            {
              "value": "none",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__1.label"
            },
            {
              "value": "apple",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__2.label"
            },
            {
              "value": "banana",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__3.label"
            },
            {
              "value": "bottle",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__4.label"
            },
            {
              "value": "box",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__5.label"
            },
            {
              "value": "carrot",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__6.label"
            },
            {
              "value": "chat_bubble",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__7.label"
            },
            {
              "value": "check_mark",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__8.label"
            },
            {
              "value": "clipboard",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__9.label"
            },
            {
              "value": "dairy",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__10.label"
            },
            {
              "value": "dairy_free",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__11.label"
            },
            {
              "value": "dryer",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__12.label"
            },
            {
              "value": "eye",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__13.label"
            },
            {
              "value": "fire",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__14.label"
            },
            {
              "value": "gluten_free",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__15.label"
            },
            {
              "value": "heart",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__16.label"
            },
            {
              "value": "iron",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__17.label"
            },
            {
              "value": "leaf",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__18.label"
            },
            {
              "value": "leather",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__19.label"
            },
            {
              "value": "lightning_bolt",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__20.label"
            },
            {
              "value": "lipstick",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__21.label"
            },
            {
              "value": "lock",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__22.label"
            },
            {
              "value": "map_pin",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__23.label"
            },
            {
              "value": "nut_free",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__24.label"
            },
            {
              "value": "pants",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__25.label"
            },
            {
              "value": "paw_print",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__26.label"
            },
            {
              "value": "pepper",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__27.label"
            },
            {
              "value": "perfume",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__28.label"
            },
            {
              "value": "plane",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__29.label"
            },
            {
              "value": "plant",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__30.label"
            },
            {
              "value": "price_tag",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__31.label"
            },
            {
              "value": "question_mark",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__32.label"
            },
            {
              "value": "recycle",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__33.label"
            },
            {
              "value": "return",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__34.label"
            },
            {
              "value": "ruler",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__35.label"
            },
            {
              "value": "serving_dish",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__36.label"
            },
            {
              "value": "shirt",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__37.label"
            },
            {
              "value": "shoe",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__38.label"
            },
            {
              "value": "silhouette",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__39.label"
            },
            {
              "value": "snowflake",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__40.label"
            },
            {
              "value": "star",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__41.label"
            },
            {
              "value": "stopwatch",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__42.label"
            },
            {
              "value": "truck",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__43.label"
            },
            {
              "value": "washing",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__44.label"
            }
          ],
          "default": "check_mark",
          "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.label"
        },
        {
          "type": "richtext",
          "id": "content",
          "label": "t:sections.main-product.blocks.collapsible_tab.settings.content.label"
        },
        {
          "type": "page",
          "id": "page",
          "label": "t:sections.main-product.blocks.collapsible_tab.settings.page.label"
        }
      ]
    },
    {
      "type": "popup",
      "name": "t:sections.main-product.blocks.popup.name",
      "settings": [
        {
          "type": "text",
          "id": "text",
          "default": "Pop-up link text",
          "label": "t:sections.main-product.blocks.popup.settings.link_label.label"
        },
        {
          "id": "page",
          "type": "page",
          "label": "t:sections.main-product.blocks.popup.settings.page.label"
        }
      ]
    },
    {
      "type": "rating",
      "name": "t:sections.main-product.blocks.rating.name",
      "limit": 1,
      "settings": [
        {
          "type": "paragraph",
          "content": "t:sections.main-product.blocks.rating.settings.paragraph.content"
        }
      ]
    },
    {
      "type": "complementary",
      "name": "t:sections.main-product.blocks.complementary_products.name",
      "limit": 1,
      "settings": [
        {
          "type": "paragraph",
          "content": "t:sections.main-product.blocks.complementary_products.settings.paragraph.content"
        },
        {
          "type": "text",
          "id": "block_heading",
          "default": "Pairs well with",
          "label": "t:sections.main-product.blocks.complementary_products.settings.heading.label"
        },
        {
          "type": "checkbox",
          "id": "make_collapsible_row",
          "default": false,
          "label": "t:sections.main-product.blocks.complementary_products.settings.make_collapsible_row.label"
        },
        {
          "type": "select",
          "id": "icon",
          "options": [
            {
              "value": "none",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__1.label"
            },
            {
              "value": "apple",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__2.label"
            },
            {
              "value": "banana",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__3.label"
            },
            {
              "value": "bottle",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__4.label"
            },
            {
              "value": "box",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__5.label"
            },
            {
              "value": "carrot",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__6.label"
            },
            {
              "value": "chat_bubble",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__7.label"
            },
            {
              "value": "check_mark",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__8.label"
            },
            {
              "value": "clipboard",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__9.label"
            },
            {
              "value": "dairy",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__10.label"
            },
            {
              "value": "dairy_free",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__11.label"
            },
            {
              "value": "dryer",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__12.label"
            },
            {
              "value": "eye",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__13.label"
            },
            {
              "value": "fire",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__14.label"
            },
            {
              "value": "gluten_free",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__15.label"
            },
            {
              "value": "heart",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__16.label"
            },
            {
              "value": "iron",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__17.label"
            },
            {
              "value": "leaf",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__18.label"
            },
            {
              "value": "leather",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__19.label"
            },
            {
              "value": "lightning_bolt",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__20.label"
            },
            {
              "value": "lipstick",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__21.label"
            },
            {
              "value": "lock",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__22.label"
            },
            {
              "value": "map_pin",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__23.label"
            },
            {
              "value": "nut_free",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__24.label"
            },
            {
              "value": "pants",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__25.label"
            },
            {
              "value": "paw_print",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__26.label"
            },
            {
              "value": "pepper",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__27.label"
            },
            {
              "value": "perfume",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__28.label"
            },
            {
              "value": "plane",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__29.label"
            },
            {
              "value": "plant",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__30.label"
            },
            {
              "value": "price_tag",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__31.label"
            },
            {
              "value": "question_mark",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__32.label"
            },
            {
              "value": "recycle",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__33.label"
            },
            {
              "value": "return",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__34.label"
            },
            {
              "value": "ruler",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__35.label"
            },
            {
              "value": "serving_dish",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__36.label"
            },
            {
              "value": "shirt",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__37.label"
            },
            {
              "value": "shoe",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__38.label"
            },
            {
              "value": "silhouette",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__39.label"
            },
            {
              "value": "snowflake",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__40.label"
            },
            {
              "value": "star",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__41.label"
            },
            {
              "value": "stopwatch",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__42.label"
            },
            {
              "value": "truck",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__43.label"
            },
            {
              "value": "washing",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__44.label"
            }
          ],
          "default": "price_tag",
          "info": "t:sections.main-product.blocks.complementary_products.settings.icon.info",
          "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.label"
        },
        {
          "type": "range",
          "id": "product_list_limit",
          "min": 1,
          "max": 10,
          "step": 1,
          "default": 10,
          "label": "t:sections.main-product.blocks.complementary_products.settings.product_list_limit.label"
        },
        {
          "type": "range",
          "id": "products_per_page",
          "min": 1,
          "max": 4,
          "step": 1,
          "default": 3,
          "label": "t:sections.main-product.blocks.complementary_products.settings.products_per_page.label"
        },
        {
          "type": "select",
          "id": "pagination_style",
          "options": [
            {
              "value": "dots",
              "label": "t:sections.main-product.blocks.complementary_products.settings.pagination_style.options.option_1"
            },
            {
              "value": "counter",
              "label": "t:sections.main-product.blocks.complementary_products.settings.pagination_style.options.option_2"
            },
            {
              "value": "numbers",
              "label": "t:sections.main-product.blocks.complementary_products.settings.pagination_style.options.option_3"
            }
          ],
          "label": "t:sections.main-product.blocks.complementary_products.settings.pagination_style.label",
          "default": "counter"
        },
        {
          "type": "header",
          "content": "t:sections.main-product.blocks.complementary_products.settings.product_card.heading"
        },
        {
          "type": "select",
          "id": "image_ratio",
          "options": [
            {
              "value": "portrait",
              "label": "t:sections.main-product.blocks.complementary_products.settings.image_ratio.options.option_1"
            },
            {
              "value": "square",
              "label": "t:sections.main-product.blocks.complementary_products.settings.image_ratio.options.option_2"
            }
          ],
          "label": "t:sections.main-product.blocks.complementary_products.settings.image_ratio.label",
          "default": "square"
        },
        {
          "type": "checkbox",
          "id": "enable_quick_add",
          "label": "t:sections.main-product.blocks.complementary_products.settings.enable_quick_add.label",
          "default": false
        }
      ]
    },
    {
      "type": "icon-with-text",
      "name": "t:sections.main-product.blocks.icon_with_text.name",
      "settings": [
        {
          "type": "select",
          "id": "layout",
          "options": [
            {
              "value": "horizontal",
              "label": "t:sections.main-product.blocks.icon_with_text.settings.layout.options__1.label"
            },
            {
              "value": "vertical",
              "label": "t:sections.main-product.blocks.icon_with_text.settings.layout.options__2.label"
            }
          ],
          "default": "horizontal",
          "label": "t:sections.main-product.blocks.icon_with_text.settings.layout.label"
        },
        {
          "type": "header",
          "content": "t:sections.main-product.blocks.icon_with_text.settings.content.label",
          "info": "t:sections.main-product.blocks.icon_with_text.settings.content.info"
        },
        {
          "type": "select",
          "id": "icon_1",
          "options": [
            {
              "value": "none",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__1.label"
            },
            {
              "value": "apple",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__2.label"
            },
            {
              "value": "banana",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__3.label"
            },
            {
              "value": "bottle",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__4.label"
            },
            {
              "value": "box",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__5.label"
            },
            {
              "value": "carrot",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__6.label"
            },
            {
              "value": "chat_bubble",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__7.label"
            },
            {
              "value": "check_mark",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__8.label"
            },
            {
              "value": "clipboard",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__9.label"
            },
            {
              "value": "dairy",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__10.label"
            },
            {
              "value": "dairy_free",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__11.label"
            },
            {
              "value": "dryer",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__12.label"
            },
            {
              "value": "eye",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__13.label"
            },
            {
              "value": "fire",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__14.label"
            },
            {
              "value": "gluten_free",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__15.label"
            },
            {
              "value": "heart",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__16.label"
            },
            {
              "value": "iron",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__17.label"
            },
            {
              "value": "leaf",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__18.label"
            },
            {
              "value": "leather",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__19.label"
            },
            {
              "value": "lightning_bolt",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__20.label"
            },
            {
              "value": "lipstick",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__21.label"
            },
            {
              "value": "lock",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__22.label"
            },
            {
              "value": "map_pin",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__23.label"
            },
            {
              "value": "nut_free",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__24.label"
            },
            {
              "value": "pants",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__25.label"
            },
            {
              "value": "paw_print",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__26.label"
            },
            {
              "value": "pepper",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__27.label"
            },
            {
              "value": "perfume",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__28.label"
            },
            {
              "value": "plane",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__29.label"
            },
            {
              "value": "plant",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__30.label"
            },
            {
              "value": "price_tag",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__31.label"
            },
            {
              "value": "question_mark",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__32.label"
            },
            {
              "value": "recycle",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__33.label"
            },
            {
              "value": "return",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__34.label"
            },
            {
              "value": "ruler",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__35.label"
            },
            {
              "value": "serving_dish",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__36.label"
            },
            {
              "value": "shirt",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__37.label"
            },
            {
              "value": "shoe",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__38.label"
            },
            {
              "value": "silhouette",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__39.label"
            },
            {
              "value": "snowflake",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__40.label"
            },
            {
              "value": "star",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__41.label"
            },
            {
              "value": "stopwatch",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__42.label"
            },
            {
              "value": "truck",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__43.label"
            },
            {
              "value": "washing",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__44.label"
            }
          ],
          "default": "heart",
          "label": "t:sections.main-product.blocks.icon_with_text.settings.icon_1.label"
        },
        {
          "type": "image_picker",
          "id": "image_1",
          "label": "t:sections.main-product.blocks.icon_with_text.settings.image_1.label"
        },
        {
          "type": "inline_richtext",
          "id": "heading_1",
          "default": "Heading",
          "label": "t:sections.main-product.blocks.icon_with_text.settings.heading_1.label",
          "info": "t:sections.main-product.blocks.icon_with_text.settings.heading.info"
        },
        {
          "type": "select",
          "id": "icon_2",
          "options": [
            {
              "value": "none",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__1.label"
            },
            {
              "value": "apple",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__2.label"
            },
            {
              "value": "banana",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__3.label"
            },
            {
              "value": "bottle",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__4.label"
            },
            {
              "value": "box",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__5.label"
            },
            {
              "value": "carrot",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__6.label"
            },
            {
              "value": "chat_bubble",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__7.label"
            },
            {
              "value": "check_mark",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__8.label"
            },
            {
              "value": "clipboard",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__9.label"
            },
            {
              "value": "dairy",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__10.label"
            },
            {
              "value": "dairy_free",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__11.label"
            },
            {
              "value": "dryer",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__12.label"
            },
            {
              "value": "eye",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__13.label"
            },
            {
              "value": "fire",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__14.label"
            },
            {
              "value": "gluten_free",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__15.label"
            },
            {
              "value": "heart",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__16.label"
            },
            {
              "value": "iron",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__17.label"
            },
            {
              "value": "leaf",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__18.label"
            },
            {
              "value": "leather",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__19.label"
            },
            {
              "value": "lightning_bolt",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__20.label"
            },
            {
              "value": "lipstick",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__21.label"
            },
            {
              "value": "lock",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__22.label"
            },
            {
              "value": "map_pin",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__23.label"
            },
            {
              "value": "nut_free",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__24.label"
            },
            {
              "value": "pants",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__25.label"
            },
            {
              "value": "paw_print",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__26.label"
            },
            {
              "value": "pepper",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__27.label"
            },
            {
              "value": "perfume",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__28.label"
            },
            {
              "value": "plane",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__29.label"
            },
            {
              "value": "plant",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__30.label"
            },
            {
              "value": "price_tag",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__31.label"
            },
            {
              "value": "question_mark",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__32.label"
            },
            {
              "value": "recycle",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__33.label"
            },
            {
              "value": "return",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__34.label"
            },
            {
              "value": "ruler",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__35.label"
            },
            {
              "value": "serving_dish",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__36.label"
            },
            {
              "value": "shirt",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__37.label"
            },
            {
              "value": "shoe",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__38.label"
            },
            {
              "value": "silhouette",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__39.label"
            },
            {
              "value": "snowflake",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__40.label"
            },
            {
              "value": "star",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__41.label"
            },
            {
              "value": "stopwatch",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__42.label"
            },
            {
              "value": "truck",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__43.label"
            },
            {
              "value": "washing",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__44.label"
            }
          ],
          "default": "return",
          "label": "t:sections.main-product.blocks.icon_with_text.settings.icon_2.label"
        },
        {
          "type": "image_picker",
          "id": "image_2",
          "label": "t:sections.main-product.blocks.icon_with_text.settings.image_2.label"
        },
        {
          "type": "inline_richtext",
          "id": "heading_2",
          "default": "Heading",
          "label": "t:sections.main-product.blocks.icon_with_text.settings.heading_2.label",
          "info": "t:sections.main-product.blocks.icon_with_text.settings.heading.info"
        },
        {
          "type": "select",
          "id": "icon_3",
          "options": [
            {
              "value": "none",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__1.label"
            },
            {
              "value": "apple",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__2.label"
            },
            {
              "value": "banana",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__3.label"
            },
            {
              "value": "bottle",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__4.label"
            },
            {
              "value": "box",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__5.label"
            },
            {
              "value": "carrot",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__6.label"
            },
            {
              "value": "chat_bubble",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__7.label"
            },
            {
              "value": "check_mark",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__8.label"
            },
            {
              "value": "clipboard",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__9.label"
            },
            {
              "value": "dairy",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__10.label"
            },
            {
              "value": "dairy_free",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__11.label"
            },
            {
              "value": "dryer",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__12.label"
            },
            {
              "value": "eye",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__13.label"
            },
            {
              "value": "fire",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__14.label"
            },
            {
              "value": "gluten_free",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__15.label"
            },
            {
              "value": "heart",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__16.label"
            },
            {
              "value": "iron",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__17.label"
            },
            {
              "value": "leaf",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__18.label"
            },
            {
              "value": "leather",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__19.label"
            },
            {
              "value": "lightning_bolt",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__20.label"
            },
            {
              "value": "lipstick",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__21.label"
            },
            {
              "value": "lock",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__22.label"
            },
            {
              "value": "map_pin",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__23.label"
            },
            {
              "value": "nut_free",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__24.label"
            },
            {
              "value": "pants",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__25.label"
            },
            {
              "value": "paw_print",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__26.label"
            },
            {
              "value": "pepper",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__27.label"
            },
            {
              "value": "perfume",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__28.label"
            },
            {
              "value": "plane",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__29.label"
            },
            {
              "value": "plant",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__30.label"
            },
            {
              "value": "price_tag",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__31.label"
            },
            {
              "value": "question_mark",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__32.label"
            },
            {
              "value": "recycle",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__33.label"
            },
            {
              "value": "return",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__34.label"
            },
            {
              "value": "ruler",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__35.label"
            },
            {
              "value": "serving_dish",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__36.label"
            },
            {
              "value": "shirt",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__37.label"
            },
            {
              "value": "shoe",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__38.label"
            },
            {
              "value": "silhouette",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__39.label"
            },
            {
              "value": "snowflake",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__40.label"
            },
            {
              "value": "star",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__41.label"
            },
            {
              "value": "stopwatch",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__42.label"
            },
            {
              "value": "truck",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__43.label"
            },
            {
              "value": "washing",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__44.label"
            }
          ],
          "default": "truck",
          "label": "t:sections.main-product.blocks.icon_with_text.settings.icon_3.label"
        },
        {
          "type": "image_picker",
          "id": "image_3",
          "label": "t:sections.main-product.blocks.icon_with_text.settings.image_3.label"
        },
        {
          "type": "inline_richtext",
          "id": "heading_3",
          "default": "Heading",
          "label": "t:sections.main-product.blocks.icon_with_text.settings.heading_3.label",
          "info": "t:sections.main-product.blocks.icon_with_text.settings.heading.info"
        }
      ]
    }
  ],
  "settings": [
    {
      "type": "checkbox",
      "id": "enable_sticky_info",
      "default": true,
      "label": "t:sections.main-product.settings.enable_sticky_info.label"
    },
    {
      "type": "color_scheme",
      "id": "color_scheme",
      "label": "t:sections.all.colors.label",
      "default": "scheme-1"
    },
    {
      "type": "header",
      "content": "t:sections.main-product.settings.header.content",
      "info": "t:sections.main-product.settings.header.info"
    },
    {
      "type": "select",
      "id": "media_size",
      "options": [
        {
          "value": "small",
          "label": "t:sections.main-product.settings.media_size.options__1.label"
        },
        {
          "value": "medium",
          "label": "t:sections.main-product.settings.media_size.options__2.label"
        },
        {
          "value": "large",
          "label": "t:sections.main-product.settings.media_size.options__3.label"
        }
      ],
      "default": "large",
      "label": "t:sections.main-product.settings.media_size.label",
      "info": "t:sections.main-product.settings.media_size.info"
    },
    {
      "type": "checkbox",
      "id": "constrain_to_viewport",
      "default": true,
      "label": "t:sections.main-product.settings.constrain_to_viewport.label"
    },
    {
      "type": "select",
      "id": "media_fit",
      "options": [
        {
          "value": "contain",
          "label": "t:sections.main-product.settings.media_fit.options__1.label"
        },
        {
          "value": "cover",
          "label": "t:sections.main-product.settings.media_fit.options__2.label"
        }
      ],
      "default": "contain",
      "label": "t:sections.main-product.settings.media_fit.label"
    },
    {
      "type": "select",
      "id": "gallery_layout",
      "options": [
        {
          "value": "stacked",
          "label": "t:sections.main-product.settings.gallery_layout.options__1.label"
        },
        {
          "value": "columns",
          "label": "t:sections.main-product.settings.gallery_layout.options__2.label"
        },
        {
          "value": "thumbnail",
          "label": "t:sections.main-product.settings.gallery_layout.options__3.label"
        },
        {
          "value": "thumbnail_slider",
          "label": "t:sections.main-product.settings.gallery_layout.options__4.label"
        }
      ],
      "default": "stacked",
      "label": "t:sections.main-product.settings.gallery_layout.label"
    },
    {
      "type": "select",
      "id": "media_position",
      "options": [
        {
          "value": "left",
          "label": "t:sections.main-product.settings.media_position.options__1.label"
        },
        {
          "value": "right",
          "label": "t:sections.main-product.settings.media_position.options__2.label"
        }
      ],
      "default": "left",
      "label": "t:sections.main-product.settings.media_position.label",
      "info": "t:sections.main-product.settings.media_position.info"
    },
    {
      "type": "select",
      "id": "image_zoom",
      "options": [
        {
          "value": "lightbox",
          "label": "t:sections.main-product.settings.image_zoom.options__1.label"
        },
        {
          "value": "hover",
          "label": "t:sections.main-product.settings.image_zoom.options__2.label"
        },
        {
          "value": "none",
          "label": "t:sections.main-product.settings.image_zoom.options__3.label"
        }
      ],
      "default": "lightbox",
      "label": "t:sections.main-product.settings.image_zoom.label",
      "info": "t:sections.main-product.settings.image_zoom.info"
    },
    {
      "type": "select",
      "id": "mobile_thumbnails",
      "options": [
        {
          "value": "columns",
          "label": "t:sections.main-product.settings.mobile_thumbnails.options__1.label"
        },
        {
          "value": "show",
          "label": "t:sections.main-product.settings.mobile_thumbnails.options__2.label"
        },
        {
          "value": "hide",
          "label": "t:sections.main-product.settings.mobile_thumbnails.options__3.label"
        }
      ],
      "default": "hide",
      "label": "t:sections.main-product.settings.mobile_thumbnails.label"
    },
    {
      "type": "checkbox",
      "id": "hide_variants",
      "default": false,
      "label": "t:sections.main-product.settings.hide_variants.label"
    },
    {
      "type": "checkbox",
      "id": "enable_video_looping",
      "default": false,
      "label": "t:sections.main-product.settings.enable_video_looping.label"
    },
    {
      "type": "header",
      "content": "t:sections.all.padding.section_padding_heading"
    },
    {
      "type": "range",
      "id": "padding_top",
      "min": 0,
      "max": 100,
      "step": 4,
      "unit": "px",
      "label": "t:sections.all.padding.padding_top",
      "default": 36
    },
    {
      "type": "range",
      "id": "padding_bottom",
      "min": 0,
      "max": 100,
      "step": 4,
      "unit": "px",
      "label": "t:sections.all.padding.padding_bottom",
      "default": 36
    }
  ]
}
{% endschema %}

<script>
  // Function to update inventory data attributes when variant changes
  function updateMainProductInventoryDataAttributes(variantData) {
    const quantityInput = document.querySelector(`#Quantity-{{ section.id }}`);
    if (!quantityInput || !variantData) return;

    // Reset quantity immediately when variant changes (before async operations)
    const minValue = parseInt(quantityInput.getAttribute('min')) || 1;

    // Use multiple timeouts to ensure this runs after any other event handlers
    setTimeout(() => {
      quantityInput.value = minValue;
      console.log(`Main product first reset: quantity to ${minValue} for variant ${variantData.id}`);
    }, 0);

    setTimeout(() => {
      quantityInput.value = minValue;
      // Trigger change event to ensure all listeners are notified
      quantityInput.dispatchEvent(new Event('change', { bubbles: true }));
      console.log(`Main product second reset: quantity to ${minValue} for variant ${variantData.id}`);
    }, 50);

    // Fetch variant-specific inventory data from the page
    fetch(`{{ product.url }}?variant=${variantData.id}&section_id={{ section.id }}`)
      .then(response => response.text())
      .then(html => {
        const parser = new DOMParser();
        const doc = parser.parseFromString(html, 'text/html');
        const newQuantityInput = doc.querySelector(`#Quantity-{{ section.id }}`);
        const newAddToCartButton = doc.querySelector(`#ProductSubmitButton-{{ section.id }}`);

        if (newQuantityInput) {
          // Get the calculated inventory data from the server-rendered HTML
          const defaultLocationInventory = newQuantityInput.dataset.inventoryQuantity;
          const totalInventory = newQuantityInput.dataset.totalInventoryQuantity;
          const maxAttribute = newQuantityInput.getAttribute('max');

          // Update quantity input data attributes
          quantityInput.dataset.quantityVariantId = variantData.id;
          quantityInput.dataset.inventoryManagement = variantData.inventory_management;
          quantityInput.dataset.inventoryPolicy = variantData.inventory_policy;
          quantityInput.dataset.inventoryQuantity = defaultLocationInventory;
          quantityInput.dataset.totalInventoryQuantity = totalInventory;

          // Update max attribute
          if (maxAttribute && maxAttribute !== 'null') {
            quantityInput.setAttribute('max', maxAttribute);
          } else {
            quantityInput.removeAttribute('max');
          }

          // Update Add to Cart button with fresh data from server
          if (newAddToCartButton) {
            const currentAddToCartButton = document.querySelector(`#ProductSubmitButton-{{ section.id }}`);
            if (currentAddToCartButton) {
              // Copy all data attributes from the fresh button
              Array.from(newAddToCartButton.attributes).forEach(attr => {
                if (attr.name.startsWith('data-')) {
                  currentAddToCartButton.setAttribute(attr.name, attr.value);
                }
              });

              // Copy disabled state and button text
              if (newAddToCartButton.hasAttribute('disabled')) {
                currentAddToCartButton.setAttribute('disabled', 'disabled');
                currentAddToCartButton.setAttribute('aria-disabled', 'true');
              } else {
                currentAddToCartButton.removeAttribute('disabled');
                currentAddToCartButton.removeAttribute('aria-disabled');
              }

              const newButtonText = newAddToCartButton.querySelector('span');
              const currentButtonText = currentAddToCartButton.querySelector('span');
              if (newButtonText && currentButtonText) {
                currentButtonText.textContent = newButtonText.textContent;
              }
            }
          }

          // Trigger change event to update any dependent UI
          quantityInput.dispatchEvent(new Event('change', { bubbles: true }));

          console.log(`Updated main product inventory for variant ${variantData.id}: ${defaultLocationInventory} (was ${totalInventory} total), available: ${variantData.available}`);
        }
      })
      .catch(error => {
        console.error('Error fetching variant inventory data:', error);
        // Fallback to basic variant data
        quantityInput.dataset.quantityVariantId = variantData.id;
        quantityInput.dataset.inventoryManagement = variantData.inventory_management;
        quantityInput.dataset.inventoryPolicy = variantData.inventory_policy;
        quantityInput.dataset.inventoryQuantity = variantData.inventory_quantity;
        quantityInput.dataset.totalInventoryQuantory = variantData.inventory_quantity;

        if (variantData.inventory_management === 'shopify' &&
            variantData.inventory_policy !== 'continue' &&
            variantData.inventory_quantity !== null) {
          quantityInput.setAttribute('max', variantData.inventory_quantity);
        } else {
          quantityInput.removeAttribute('max');
        }

        // Update button with fallback data
        const addToCartButton = document.querySelector(`#ProductSubmitButton-{{ section.id }}`);
        if (addToCartButton) {
          addToCartButton.dataset.variantId = variantData.id;
          addToCartButton.dataset.inventoryManagement = variantData.inventory_management;
          addToCartButton.dataset.inventoryPolicy = variantData.inventory_policy;
          addToCartButton.dataset.inventoryQuantity = variantData.inventory_quantity;

          const buttonText = addToCartButton.querySelector('span');
          if (!variantData.available) {
            addToCartButton.setAttribute('disabled', 'disabled');
            addToCartButton.setAttribute('aria-disabled', 'true');
            if (buttonText) {
              buttonText.textContent = 'Sold out';
            }
          } else {
            addToCartButton.removeAttribute('disabled');
            addToCartButton.removeAttribute('aria-disabled');
            if (buttonText) {
              buttonText.textContent = 'Add to cart';
            }
          }
        }
      });
  }

  // Subscribe to variant change events
  document.addEventListener('DOMContentLoaded', function() {
    if (typeof subscribe === 'function' && typeof PUB_SUB_EVENTS !== 'undefined') {
      subscribe(PUB_SUB_EVENTS.variantChange, (event) => {
        // Only update if this is the current section
        if (event.data.sectionId === '{{ section.id }}') {
          updateMainProductInventoryDataAttributes(event.data.variant);
        }
      });
    }
  });
</script>
