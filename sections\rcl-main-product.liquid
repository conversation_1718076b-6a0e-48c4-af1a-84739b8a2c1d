{{ 'section-rcl-product.css' | asset_url | stylesheet_tag: preload: true }}
{{ 'section-rcl-quantity-calc.css' | asset_url | stylesheet_tag: preload: true }}
{{ 'section-product-additional-content.css' | asset_url | stylesheet_tag }}

<section
  id="MainProduct-{{ section.id }}"
  class="section-{{ section.id }}-padding gradient color-{{ section.settings.color_scheme }}"
  data-section="{{ section.id }}"
>
  {{ 'section-main-product.css' | asset_url | stylesheet_tag }}
  {{ 'component-accordion.css' | asset_url | stylesheet_tag }}
  {{ 'component-price.css' | asset_url | stylesheet_tag }}
  {{ 'component-slider.css' | asset_url | stylesheet_tag }}
  {{ 'component-rating.css' | asset_url | stylesheet_tag }}
  {{ 'component-deferred-media.css' | asset_url | stylesheet_tag }}
  {% unless product.has_only_default_variant %}
    {{ 'component-product-variant-picker.css' | asset_url | stylesheet_tag }}
  {% endunless %}

  {%- style -%}
    .drawer {
      top: -30px!important;
    }

    .section-{{ section.id }}-padding {
      padding-top: {{ section.settings.padding_top | times: 0.75 | round: 0 }}px;
      padding-bottom: {{ section.settings.padding_bottom | times: 0.75 | round: 0 }}px;
    }

    @media screen and (min-width: 750px) {
      .section-{{ section.id }}-padding {
        padding-top: {{ section.settings.padding_top }}px;
        padding-bottom: {{ section.settings.padding_bottom }}px;
      }
    }

    .texture-gallery {
      display: grid;
      grid-template-columns: repeat(auto-fill, minmax(100px, 1fr));
      gap: 15px;
      margin-top: 20px;
    }

    @media screen and (min-width: 750px) {
      .texture-gallery {
        grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
        gap: 20px;
      }
    }

    .texture-item {
      position: relative;
      overflow: hidden;
      border-radius: 8px;
      box-shadow: 0 2px 5px rgba(0,0,0,0.1);
      transition: transform 0.3s ease;
    }

    .texture-item:hover {
      transform: translateY(-5px);
    }

    .texture-image {
      display: block;
      width: 100%;
      height: auto;
      aspect-ratio: 1/1;
      object-fit: cover;
    }

    .texture-link {
      display: block;
      cursor: zoom-in;
    }

    .application-tech-images {
      display: grid;
      grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
      gap: 20px;
      margin-top: 20px;
    }

    .simple-lightbox {
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background-color: rgba(0,0,0,0.9);
      z-index: 9999;
      display: none;
      align-items: center;
      justify-content: center;
    }

    .lightbox-container {
      position: relative;
      max-width: 80%;
      max-height: 80%;
      display: flex;
      flex-direction: column;
      align-items: center;
    }

    .lightbox-img {
      max-width: 100%;
      max-height: 80vh;
      object-fit: contain;
      border: 2px solid white;
      box-shadow: 0 0 20px rgba(0,0,0,0.5);
    }

    .lightbox-counter {
      position: absolute;
      bottom: -30px;
      left: 0;
      right: 0;
      text-align: center;
      color: white;
      font-size: 14px;
      padding: 5px;
    }

    .lightbox-close {
      position: absolute;
      top: 20px;
      right: 20px;
      font-size: 30px;
      color: white;
      background: transparent;
      border: none;
      cursor: pointer;
      z-index: 10;
      padding: 5px;
      line-height: 1;
      height: 40px;
      width: 40px;
      display: flex;
      align-items: center;
      justify-content: center;
      border-radius: 50%;
      transition: background-color 0.3s ease;
    }

    .lightbox-close:hover {
      background-color: rgba(255,255,255,0.2);
    }

    .lightbox-nav {
      position: absolute;
      top: 50%;
      transform: translateY(-50%);
      background: rgba(0,0,0,0.5);
      color: white;
      border: none;
      width: 50px;
      height: 50px;
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      cursor: pointer;
      z-index: 10;
      transition: background-color 0.3s ease;
    }

    .lightbox-nav:hover {
      background: rgba(0,0,0,0.8);
    }

    .lightbox-prev {
      left: 20px;
    }

    .lightbox-next {
      right: 20px;
    }

    @media screen and (max-width: 767px) {
      .lightbox-nav {
        width: 40px;
        height: 40px;
      }

      .lightbox-prev {
        left: 10px;
      }

      .lightbox-next {
        right: 10px;
      }

      .lightbox-img {
        max-width: 95%;
        min-width: 85vw;
        min-height: 50vh;
      }
    }

    .product__info-container > * + * {
      margin: 2rem 0;
    }
  {%- endstyle -%}

  <script src="{{ 'product-info.js' | asset_url }}" defer="defer"></script>
  <script src="{{ 'product-form.js' | asset_url }}" defer="defer"></script>
  <script src="{{ 'product-calculator.js' | asset_url }}" defer="defer"></script>

  {% if section.settings.image_zoom == 'hover' %}
    <script id="EnableZoomOnHover-main" src="{{ 'magnify.js' | asset_url }}" defer="defer"></script>
  {% endif %}

  {% assign variant_images = product.images | where: 'attached_to_variant?', true | map: 'src' %}
  <div class="page-width">
    <div class="product product--{{ section.settings.media_size }} product--{{ section.settings.media_position }} product--{{ section.settings.gallery_layout }} product--mobile-{{ section.settings.mobile_thumbnails }} grid grid--1-col {% if product.media.size > 0 %}grid--2-col-tablet{% else %}product--no-media{% endif %}">
      <div class="grid__item product__media-wrapper{% if section.settings.media_position == 'right' %} medium-hide large-up-hide{% endif %}">
        {% render 'product-media-gallery', variant_images: variant_images %}
      </div>
      <div class="product__info-wrapper grid__item{% if settings.page_width > 1400 and section.settings.media_size == "small" %} product__info-wrapper--extra-padding{% endif %}">
        <product-info
          id="ProductInfo-{{ section.id }}"
          data-section="{{ section.id }}"
          data-url="{{ product.url }}"
          class="product__info-container{% if section.settings.enable_sticky_info %} product__column-sticky{% endif %}"
        >
          {%- assign product_form_id = 'product-form-' | append: section.id -%}

          {%- for block in section.blocks -%}
            {%- case block.type -%}
              {%- when '@app' -%}
                <div id="{{ block.id }}" class="app-block" data-shopify-editor-block="true" data-shopify-editor-block-type="@app">
                  {% render block %}
                </div>
              {%- when 'text' -%}
                <p
                  class="product__text{% if block.settings.text_style == 'uppercase' %} caption-with-letter-spacing{% elsif block.settings.text_style == 'subtitle' %} subtitle{% endif %}"
                  {{ block.shopify_attributes }}
                >
                  {{- block.settings.text -}}
                </p>
              {%- when 'title' -%}
                <div class="product__title" {{ block.shopify_attributes }}>
                  <h1>{{ product.title | escape }}</h1>
                </div>
              {%- when 'price' -%}
                <div class="no-js-hidden" id="price-{{ section.id }}" role="status" {{ block.shopify_attributes }}>
                  {%- render 'price',
                    product: product,
                    use_variant: true,
                    show_badges: true,
                    price_class: 'price--large'
                  -%}
                </div>
              {%- when 'variant_picker' -%}
                {% render 'product-variant-picker', product: product, block: block, product_form_id: product_form_id %}
              {%- when 'quantity_selector' -%}
                <div
                  id="Quantity-Form-{{ section.id }}"
                  class="product-form__input product-form__quantity"
                  {{ block.shopify_attributes }}
                >
                  <label class="quantity__label form__label" for="Quantity-{{ section.id }}">
                    {{ 'products.product.quantity.label' | t }}
                  </label>

                  {% comment %} Calculate default location inventory {% endcomment %}
                  {%- liquid
                    assign default_location_inventory = product.selected_or_first_available_variant.inventory_quantity

                    if product.selected_or_first_available_variant.store_availabilities.size > 0
                      assign default_location = product.selected_or_first_available_variant.store_availabilities.first
                      if default_location.available and default_location.inventory_quantity
                        assign default_location_inventory = default_location.inventory_quantity
                      endif
                    endif
                  -%}

                  <quantity-input class="quantity">
                    <button class="quantity__button" name="minus" type="button">
                      <span class="visually-hidden">
                        {{- 'products.product.quantity.decrease' | t: product: product.title | escape -}}
                      </span>
                      {% render 'icon-minus' %}
                    </button>
                    <input
                      class="quantity__input test3"
                      type="number"
                      name="quantity"
                      id="Quantity-{{ section.id }}"
                      min="1"
                      value="1"
                      form="{{ product_form_id }}"
                      data-quantity-variant-id="{{ product.selected_or_first_available_variant.id }}"
                      data-inventory-management="{{ product.selected_or_first_available_variant.inventory_management }}"
                      data-inventory-policy="{{ product.selected_or_first_available_variant.inventory_policy }}"
                      data-inventory-quantity="{{ default_location_inventory }}"
                      data-total-inventory-quantity="{{ product.selected_or_first_available_variant.inventory_quantity }}"
                      data-safety-stock-check="true"
                      {% if product.selected_or_first_available_variant.inventory_management == 'shopify' and product.selected_or_first_available_variant.inventory_policy != 'continue' and default_location_inventory != null %}
                        max="{{ default_location_inventory }}"
                      {% endif %}
                    />
                    <button class="quantity__button" name="plus" type="button">
                      <span class="visually-hidden">
                        {{- 'products.product.quantity.increase' | t: product: product.title | escape -}}
                      </span>
                      {% render 'icon-plus' %}
                    </button>
                  </quantity-input>
                </div>
                {%- when 'texture_gallery' -%}
                  {% if product.metafields.custom.gallery_textures != blank %}
                    {% assign texture_count = 0 %}
                    {% for texture in product.metafields.custom.gallery_textures.value %}
                      {% assign texture_count = texture_count | plus: 1 %}
                    {% endfor %}

                    <div class="product-additional-specs js-additional-specs {% if block.settings.open_by_default %}additional-specs--active{% endif %}" {{ block.shopify_attributes }}>
                      <div class="additional-specs__header js-additional-specs-header">
                        <h3 class="additional-specs__title">
                          {{ block.settings.title | default: 'Gallery of textures' }}{% if block.settings.show_count %} ({{ texture_count }}){% endif %}
                          <span class="additional-specs__icon"></span>
                        </h3>
                      </div>
                      <div class="additional-specs__content" {% if block.settings.open_by_default %}style="display: block;"{% endif %}>
                        <div class="texture-gallery">
                          {% for texture in product.metafields.custom.gallery_textures.value %}
                            <div class="texture-item">
                              <a href="{{ texture | image_url: width: 2000 }}" class="texture-link" data-gallery-trigger>
                                {{ texture | image_url: width: 400 | image_tag:
                                    loading: 'lazy',
                                    class: 'texture-image',
                                    widths: '400, 500, 600, 700, 800',
                                    sizes: '(min-width: 990px) calc(33.33vw - 50px), (min-width: 750px) calc(50vw - 50px), calc(100vw - 50px)'
                                }}
                              </a>
                            </div>
                          {% endfor %}
                        </div>
                      </div>
                    </div>
                  {% endif %}

                {%- when 'application_technologies' -%}
                  {% if product.metafields.custom.apptech != blank %}
                    <div class="product-additional-specs js-additional-specs {% if block.settings.open_by_default %}additional-specs--active{% endif %}" {{ block.shopify_attributes }}>
                      <div class="additional-specs__header js-additional-specs-header">
                        <h3 class="additional-specs__title">
                          {{ block.settings.title | default: 'Application technologies' }}
                          <span class="additional-specs__icon"></span>
                        </h3>
                      </div>
                      <div class="additional-specs__content" {% if block.settings.open_by_default %}style="display: block;"{% endif %}>
                        <div class="rte">
                          {% assign apptech_content = product.metafields.custom.apptech.value %}
                          {% for para in apptech_content.children %}
                            {% if para.type == "paragraph" %}
                              <p>
                                {% for text in para.children %}
                                  {{ text.value }}
                                {% endfor %}
                              </p>
                            {% endif %}
                          {% endfor %}
                        </div>

                        {% if product.metafields.custom.applicationtech_images != blank %}
                          <div class="application-tech-images">
                            {% for image in product.metafields.custom.applicationtech_images.value %}
                              <div class="application-tech-image">
                                {{ image | image_url: width: 800 | image_tag:
                                   loading: 'lazy',
                                   class: 'tech-image',
                                   widths: '400, 500, 600, 700, 800',
                                   sizes: '(min-width: 990px) calc(33.33vw - 50px), (min-width: 750px) calc(50vw - 50px), calc(100vw - 50px)'
                                }}
                              </div>
                            {% endfor %}
                          </div>
                        {% endif %}
                      </div>
                    </div>
                  {% endif %}
                {% if product.metafields.custom.apptech != blank %}
                  <div class="accordion-container {% if block.settings.open_by_default %}is-active{% endif %}" {{ block.shopify_attributes }}>
                    <button class="accordion-trigger" aria-expanded="{% if block.settings.open_by_default %}true{% else %}false{% endif %}">
                      <div class="accordion-icon">
                        <span class="icon-plus">
                          <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <path d="M8 0V16" stroke="#D1B15A" stroke-width="1.5"/>
                            <path d="M16 8L0 8" stroke="#D1B15A" stroke-width="1.5"/>
                          </svg>
                        </span>
                      </div>
                      <h2 class="accordion-title">{{ block.settings.title | default: 'Application technologies' }}</h2>
                    </button>
                    <div class="accordion-content" {% if block.settings.open_by_default %}style="display: block;"{% endif %}>
                      <div class="rte">
                        {% assign apptech_content = product.metafields.custom.apptech.value %}
                        {% for para in apptech_content.children %}
                          {% if para.type == "paragraph" %}
                            <p>
                              {% for text in para.children %}
                                {{ text.value }}
                              {% endfor %}
                            </p>
                          {% endif %}
                        {% endfor %}
                      </div>

                      {% if product.metafields.custom.applicationtech_images != blank %}
                        <div class="application-tech-images">
                          {% for image in product.metafields.custom.applicationtech_images.value %}
                            <div class="application-tech-image">
                              {{ image | image_url: width: 800 | image_tag:
                                 loading: 'lazy',
                                 class: 'tech-image',
                                 widths: '400, 500, 600, 700, 800',
                                 sizes: '(min-width: 990px) calc(33.33vw - 50px), (min-width: 750px) calc(50vw - 50px), calc(100vw - 50px)'
                              }}
                            </div>
                          {% endfor %}
                        </div>
                      {% endif %}
                    </div>
                  </div>
                {% endif %}
              {%- when 'buy_buttons' -%}
                {%- render 'buy-buttons', block: block, product: product, product_form_id: product_form_id, section_id: section.id, show_pickup_availability: true -%}
              {%- when 'description' -%}
                {%- if product.description != blank -%}
                  <div class="product__description rte" {{ block.shopify_attributes }}>
                    {{ product.description }}
                  </div>
                {%- endif -%}
              {%- when 'product_metafields' -%}
                {% if product.metafields.custom.application != blank or product.metafields.custom.composition != blank or product.metafields.custom.consumption != blank %}
                  <div class="product-metafields" {{ block.shopify_attributes }}>
                    <div class="product-metafields__columns">
                      {% if product.metafields.custom.application != blank %}
                        <div class="metafield-column">
                          <div class="metafield-column__icon">
                            {% if block.settings.application_icon != blank %}
                              {{ block.settings.application_icon | image_url: width: 28 | image_tag:
                                  loading: 'lazy',
                                  width: 28,
                                  height: 28,
                                  alt: 'Application'
                              }}
                            {% else %}
                              {% render 'icon-application', class: 'icon-home', width: 30, height: 30 %}
                            {% endif %}
                          </div>
                          <h3 class="metafield-column__title">Application:</h3>
                          <div class="metafield-column__content">{{ product.metafields.custom.application }}</div>
                        </div>
                      {% endif %}

                      {% if product.metafields.custom.composition != blank %}
                        <div class="metafield-column">
                          <div class="metafield-column__icon">
                            {% if block.settings.composition_icon != blank %}
                              {{ block.settings.composition_icon | image_url: width: 28 | image_tag:
                                  loading: 'lazy',
                                  width: 28,
                                  height: 28,
                                  alt: 'Composition'
                              }}
                            {% else %}
                              {% render 'icon-composition', class: 'icon-category', width: 30, height: 30 %}
                            {% endif %}
                          </div>
                          <h3 class="metafield-column__title">Composition:</h3>
                          <div class="metafield-column__content">{{ product.metafields.custom.composition }}</div>
                        </div>
                      {% endif %}

                      {% if product.metafields.custom.consumption != blank %}
                        <div class="metafield-column">
                          <div class="metafield-column__icon">
                            {% if block.settings.consumption_icon != blank %}
                              {{ block.settings.consumption_icon | image_url: width: 28 | image_tag:
                                  loading: 'lazy',
                                  width: 28,
                                  height: 28,
                                  alt: 'Consumption'
                              }}
                            {% else %}
                              {% render 'icon-consumption', class: 'icon-m2', width: 30, height: 30 %}
                            {% endif %}
                          </div>
                          <h3 class="metafield-column__title">Consumption:</h3>
                          <div class="metafield-column__content">{{ product.metafields.custom.consumption }}</div>
                        </div>
                      {% endif %}
                    </div>

                    {% if product.metafields.custom.additional_specs != blank %}
                      <div class="product-additional-specs js-additional-specs">
                        <div class="additional-specs__header js-additional-specs-header">
                          <h3 class="additional-specs__title">
                            Additional Specifications
                            <span class="additional-specs__icon"></span>
                          </h3>
                        </div>
                        <div class="additional-specs__content">
                          <div class="rte">
                            {% assign specs_text = product.metafields.custom.additional_specs.value.children[0].children[0].value %}
                            {{ specs_text | newline_to_br }}
                          </div>
                        </div>
                      </div>
                    {% endif %}

                    {% comment %} Features list from metafields {% endcomment %}
                    {% if product.metafields.custom.newfeatures != blank %}
                      <div class="product-features">
                        <div class="features__header">
                          <h3 class="features__title">Key Features</h3>
                        </div>
                        <div class="features__content">
                          <ul class="features-list">
                            {% assign feature_items = product.metafields.custom.newfeatures.value.children[0].children %}
                            {% for item in feature_items %}
                              <li class="feature-item">
                                <div class="feature-item__icon">
                                  <svg viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                    <path d="M5 12L10 17L19 8" stroke="#d1b073" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                                  </svg>
                                </div>
                                <div class="feature-item__text">{{ item.children[0].value }}</div>
                              </li>
                            {% endfor %}
                          </ul>
                        </div>
                      </div>
                    {% endif %}
                  </div>
                {% endif %}
              {%- when 'collapsible_tab' -%}
                <div class="product__accordion accordion" {{ block.shopify_attributes }}>
                  <details id="Details-{{ block.id }}-{{ section.id }}">
                    <summary>
                      <div class="summary__title">
                        {% render 'icon-accordion', icon: block.settings.icon %}
                        <h2 class="accordion__title">
                          {{ block.settings.heading | default: block.settings.page.title }}
                        </h2>
                      </div>
                      {% render 'icon-caret' %}
                    </summary>
                    <div class="accordion__content rte">
                      {{ block.settings.content }}
                      {{ block.settings.page.content }}
                    </div>
                  </details>
                </div>
              {%- when 'share' -%}
                {% assign share_url = request.origin | append: product.url %}
                {% render 'share-button',
                  block: block,
                  share_link: share_url
                %}

              {%- when 'custom_liquid' -%}
                <div class="product__custom-liquid" {{ block.shopify_attributes }}>
                  {{ block.settings.custom_liquid }}
                </div>

              {%- when 'rating' -%}
                {%- if product.metafields.reviews.rating.value != blank -%}
                  <div
                    class="rating"
                    role="img"
                    aria-label="{{ 'accessibility.star_reviews_info' | t: rating_value: product.metafields.reviews.rating.value, rating_max: product.metafields.reviews.rating.value.scale_max }}"
                  >
                    <span
                      aria-hidden="true"
                      class="rating-star"
                      style="--rating: {{ product.metafields.reviews.rating.value.rating | floor }}; --rating-max: {{ product.metafields.reviews.rating.value.scale_max }};"
                    ></span>
                  </div>
                {%- endif -%}
              {%- when 'quantity_calculator'-%}
                {%- if product.metafields.custom.consumption != blank -%}
                  <div class="product-calculator" data-consumption="{{ product.metafields.custom.consumption }}" {{ block.shopify_attributes }}>
                    <button type="button" class="calculator-toggle" aria-expanded="false">
                      <span class="calculator-toggle__text">{{ block.settings.button_text }}</span>
                      <span class="calculator-toggle__icon">
                        <svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                          <rect x="3" y="3" width="18" height="18" rx="2" stroke="currentColor" stroke-width="2"/>
                          <line x1="8" y1="7" x2="16" y2="7" stroke="currentColor" stroke-width="2" stroke-linecap="round"/>
                          <line x1="8" y1="12" x2="10" y2="12" stroke="currentColor" stroke-width="2" stroke-linecap="round"/>
                          <line x1="14" y1="12" x2="16" y2="12" stroke="currentColor" stroke-width="2" stroke-linecap="round"/>
                          <line x1="8" y1="17" x2="10" y2="17" stroke="currentColor" stroke-width="2" stroke-linecap="round"/>
                          <line x1="14" y1="17" x2="16" y2="17" stroke="currentColor" stroke-width="2" stroke-linecap="round"/>
                        </svg>
                      </span>
                    </button>

                    <script type="application/json" id="ProductVariantData">
                    {
                      "variants": [
                        {% for variant in product.variants %}
                          {% if variant.available %}
                            {% assign variant_size = 0 %}
                            {% assign size_unit = "kg" %}

                            {% if variant.title contains "kg" or variant.title contains "Kg" or variant.title contains "KG" %}
                              {% assign size_parts = variant.title | remove: "kg" | remove: "Kg" | remove: "KG" | strip | split: " " %}
                              {% assign variant_size = size_parts[0] | plus: 0 %}
                              {% assign size_unit = "kg" %}
                            {% elsif variant.title contains "L" or variant.title contains "l" %}
                              {% assign size_parts = variant.title | remove: "L" | remove: "l" | strip | split: " " %}
                              {% assign variant_size = size_parts[0] | plus: 0 %}
                              {% assign size_unit = "L" %}
                            {% else %}
                              {% assign size_parts = variant.title | split: " " %}
                              {% assign variant_size = size_parts[0] | plus: 0 %}
                            {% endif %}

                            {
                              "id": {{ variant.id | json }},
                              "title": {{ variant.title | json }},
                              "size": {{ variant_size | json }},
                              "unit": {{ size_unit | json }},
                              "available": true,
                              "price": {{ variant.price | json }}
                            }{% unless forloop.last %},{% endunless %}
                          {% endif %}
                        {% endfor %}
                      ]
                    }
                    </script>
                  </div>
                {%- endif -%}

                {% if section.blocks contains block and block.type == 'quantity_calculator' and product.metafields.custom.consumption != blank %}
                  <div id="QuantityCalculator" class="quantity-calculator-drawer">
                    <div class="quantity-calculator-drawer__container">
                      <div class="quantity-calculator-drawer__header">
                        <h2 class="quantity-calculator-drawer__heading">Quantity Calculator</h2>
                        <button type="button" class="quantity-calculator-drawer__close" aria-label="Close">
                          <svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <path d="M18 6L6 18" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                            <path d="M6 6L18 18" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                          </svg>
                        </button>
                      </div>

                      <div class="quantity-calculator-drawer__content">
                        <h3 class="quantity-calculator__subheading">PLEASE ENTER YOUR DIMENSIONS</h3>

                        <div class="quantity-calculator__unit-selector">
                          <div class="quantity-calculator__unit-options">
                            <label class="quantity-calculator__unit-option">
                              <input type="radio" name="unit" value="meters" checked class="calculator-unit-input">
                              <span class="calculator-unit-label">METERS</span>
                            </label>
                            <label class="quantity-calculator__unit-option">
                              <input type="radio" name="unit" value="feet" class="calculator-unit-input">
                              <span class="calculator-unit-label">FEET</span>
                            </label>
                          </div>
                        </div>

                        <div class="quantity-calculator__walls" id="calculatorWalls">
                          <div class="quantity-calculator__wall" data-wall-index="1">
                            <div class="quantity-calculator__wall-header">
                              <span class="quantity-calculator__wall-title">WALL <span class="wall-number">1</span></span>
                              <button type="button" class="quantity-calculator__wall-remove" aria-label="Remove wall" style="display: none;">
                                <svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                  <path d="M18 6L6 18" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                                  <path d="M6 6L18 18" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                                </svg>
                              </button>
                            </div>
                            <div class="quantity-calculator__dimension-fields">
                              <div class="quantity-calculator__dimension">
                                <label for="wall-1-height" class="quantity-calculator__dimension-label">Height</label>
                                <input type="number" id="wall-1-height" class="quantity-calculator__dimension-input wall-height" min="0" step="0.1" value="">
                              </div>
                              <div class="quantity-calculator__dimension">
                                <label for="wall-1-width" class="quantity-calculator__dimension-label">Width</label>
                                <input type="number" id="wall-1-width" class="quantity-calculator__dimension-input wall-width" min="0" step="0.1" value="">
                              </div>
                            </div>
                          </div>
                        </div>

                        <div class="quantity-calculator__actions">
                          <button type="button" class="quantity-calculator__add-wall">ADD ANOTHER WALL</button>
                          <button type="button" class="quantity-calculator__reset">RESET</button>
                        </div>

                        <div class="quantity-calculator__results">
                          <h3 class="quantity-calculator__results-heading">Suggested Quantity</h3>

                          <div class="quantity-calculator__area">
                            <span class="quantity-calculator__area-label">Your total area:</span>
                            <span class="quantity-calculator__area-value">0 m²</span>
                          </div>

                          <div class="quantity-calculator__recommendation">
                            <span class="quantity-calculator__recommendation-label">for the perfect finish, we'd recommend:</span>
                            <span class="quantity-calculator__recommendation-value">0 kg</span>
                          </div>
                        </div>
                        <div class="quantity-calculator__disclaimer">
                          <p>This calculator gives a reliable estimate, but unique details like windows or doors might slightly change the amount you need. If you're unsure about quantities, feel free to get in touch — our friendly team is happy to help at <a href="mailto:<EMAIL>"><EMAIL></a></p>
                        </div>
                      </div>
                    </div>
                  </div>

                  <div id="CalculatorOverlay" class="quantity-calculator-overlay"></div>
                {% endif %}
            {%- endcase -%}
          {%- endfor -%}
        </product-info>
      </div>
      {%- if section.settings.media_position == 'right' -%}
        <div class="grid__item product__media-wrapper small-hide">
          {% render 'product-media-gallery', variant_images: variant_images, is_duplicate: true %}
        </div>
      {%- endif -%}
    </div>
  </div>

  <div id="TextureGalleryLightbox" class="simple-lightbox">
    <button type="button" class="lightbox-close" aria-label="Close">&times;</button>
    <button type="button" class="lightbox-nav lightbox-prev" aria-label="Previous image">
      <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
        <path d="M15 18L9 12L15 6" stroke="white" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
      </svg>
    </button>
    <div class="lightbox-container">
      <img class="lightbox-img" src="" alt="Texture detail" style="min-width: 85vw;">
      <div class="lightbox-counter">
        <span class="current-index">1</span> / <span class="total-count">1</span>
      </div>
    </div>
    <button type="button" class="lightbox-nav lightbox-next" aria-label="Next image">
      <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
        <path d="M9 6L15 12L9 18" stroke="white" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
      </svg>
    </button>
  </div>

  {%- if product.media.size > 0 -%}
    <script src="{{ 'product-modal.js' | asset_url }}" defer="defer"></script>
    <script src="{{ 'media-gallery.js' | asset_url }}" defer="defer"></script>
  {%- endif -%}
</section>

<script>
  // Subscribe to variant change events to update inventory data
  function initVariantChangeListener() {
    if (typeof subscribe === 'function' && typeof PUB_SUB_EVENTS !== 'undefined') {
      subscribe(PUB_SUB_EVENTS.variantChange, (event) => {
        // Only update if this is the current section
        if (event.data.sectionId === '{{ section.id }}') {
          console.log('Variant change event received for variant:', event.data.variant.id);
          const quantityInput = document.querySelector(`#Quantity-{{ section.id }}`);
          if (quantityInput) {
            console.log('Current quantity before update:', quantityInput.value);
          }
          updateInventoryDataAttributes(event.data.variant);
        }
      });
    }
  }

  document.addEventListener('DOMContentLoaded', function() {
    initCartDataFetch();
    initAdditionalSpecsAccordion();
    fixAddToCartButtons();
    enhanceVariantSelection();
    enhanceQuantitySelector();
    initAnimations();
    initTextureGallery();
    initAccordionToggle();
    initVariantChangeListener();
  });

  function initTextureGallery() {
    var galleryTriggers = document.querySelectorAll('[data-gallery-trigger]');
    var lightbox = document.getElementById('TextureGalleryLightbox');

    if (galleryTriggers.length === 0 || !lightbox) return;

    var lightboxImg = lightbox.querySelector('.lightbox-img');
    var closeBtn = lightbox.querySelector('.lightbox-close');
    var prevBtn = lightbox.querySelector('.lightbox-prev');
    var nextBtn = lightbox.querySelector('.lightbox-next');
    var currentIndexEl = lightbox.querySelector('.current-index');
    var totalCountEl = lightbox.querySelector('.total-count');

    var galleryImages = [];
    var currentIndex = 0;
    var imageCache = {};
    var preloadedImages = {};

    var loadingIndicator = document.createElement('div');
    loadingIndicator.className = 'lightbox-loading';
    loadingIndicator.innerHTML = `
      <div class="loading-spinner">
        <div class="spinner-circle"></div>
      </div>
    `;
    loadingIndicator.style.position = 'absolute';
    loadingIndicator.style.top = '50%';
    loadingIndicator.style.left = '50%';
    loadingIndicator.style.transform = 'translate(-50%, -50%)';
    loadingIndicator.style.zIndex = '5';
    loadingIndicator.style.display = 'none';
    lightbox.querySelector('.lightbox-container').appendChild(loadingIndicator);

    var style = document.createElement('style');
    style.textContent = `
      .lightbox-loading {
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        z-index: 5;
      }
      .loading-spinner {
        width: 50px;
        height: 50px;
        display: flex;
        justify-content: center;
        align-items: center;
      }
      .spinner-circle {
        width: 40px;
        height: 40px;
        border: 4px solid rgba(255, 255, 255, 0.3);
        border-radius: 50%;
        border-top-color: #fff;
        animation: spin 1s ease-in-out infinite;
      }
      @keyframes spin {
        to {
          transform: rotate(360deg);
        }
      }
    `;
    document.head.appendChild(style);

    galleryTriggers.forEach(function(trigger) {
      galleryImages.push(trigger.getAttribute('href'));
    });

    if (totalCountEl) {
      totalCountEl.textContent = galleryImages.length;
    }

    function preloadImage(url) {
      if (!url || preloadedImages[url]) return Promise.resolve(preloadedImages[url]);

      return new Promise((resolve) => {
        var img = new Image();
        img.onload = function() {
          preloadedImages[url] = img;
          resolve(img);
        };
        img.onerror = function() {
          console.error('Failed to preload image:', url);
          resolve(null);
        };
        img.src = url;
      });
    }

    function preloadAdjacentImages(currentIdx) {
      if (galleryImages.length <= 1) return;

      var nextIdx = (currentIdx + 1) % galleryImages.length;
      var prevIdx = (currentIdx - 1 + galleryImages.length) % galleryImages.length;

      preloadImage(galleryImages[nextIdx]);
      preloadImage(galleryImages[prevIdx]);

      if (galleryImages.length > 4) {
        var nextIdx2 = (currentIdx + 2) % galleryImages.length;
        var prevIdx2 = (currentIdx - 2 + galleryImages.length) % galleryImages.length;

        setTimeout(() => {
          preloadImage(galleryImages[nextIdx2]);
          preloadImage(galleryImages[prevIdx2]);
        }, 100);
      }
    }

    function getImageElement(url) {
      if (imageCache[url]) return imageCache[url];

      var img = new Image();
      img.src = url;
      img.className = 'lightbox-img';
      img.style.minWidth = '85vw'; // Ensure image is large enough on mobile
      img.style.minHeight = '50vh'; // Ensure image has good height on mobile
      imageCache[url] = img;
      return img;
    }

    async function showImage(index) {
      if (index < 0) index = galleryImages.length - 1;
      if (index >= galleryImages.length) index = 0;

      currentIndex = index;
      var imageUrl = galleryImages[currentIndex];

      lightboxImg.style.opacity = '0.3';
      loadingIndicator.style.display = 'block';

      if (preloadedImages[imageUrl]) {
        var imageElement = getImageElement(imageUrl);

        if (lightboxImg.parentNode) {
          lightboxImg.parentNode.replaceChild(imageElement, lightboxImg);
          lightboxImg = imageElement;
        }

        lightboxImg.style.opacity = '1';
        loadingIndicator.style.display = 'none';
      } else {
        await preloadImage(imageUrl);

        var imageElement = getImageElement(imageUrl);

        if (lightboxImg.parentNode) {
          lightboxImg.parentNode.replaceChild(imageElement, lightboxImg);
          lightboxImg = imageElement;
        }

        lightboxImg.style.opacity = '1';
        loadingIndicator.style.display = 'none';
      }

      if (currentIndexEl) {
        currentIndexEl.textContent = currentIndex + 1;
      }

      preloadAdjacentImages(currentIndex);
    }

    function preloadAllImages() {
      for (var i = 0; i < Math.min(5, galleryImages.length); i++) {
        preloadImage(galleryImages[i]);
      }

      if (galleryImages.length > 5) {
        setTimeout(() => {
          for (var i = 5; i < galleryImages.length; i++) {
            preloadImage(galleryImages[i]);
          }
        }, 1000);
      }
    }

    preloadAllImages();

    galleryTriggers.forEach(function(trigger, index) {
      trigger.addEventListener('click', function(e) {
        e.preventDefault();

        lightbox.style.display = 'flex';
        document.body.style.overflow = 'hidden';
        currentIndex = index;
        showImage(currentIndex);
      });
    });

    if (prevBtn) {
      prevBtn.addEventListener('click', function() {
        showImage(currentIndex - 1);
      });
    }

    if (nextBtn) {
      nextBtn.addEventListener('click', function() {
        showImage(currentIndex + 1);
      });
    }

    if (closeBtn) {
      closeBtn.addEventListener('click', function() {
        lightbox.style.display = 'none';
        document.body.style.overflow = '';
      });
    }

    lightbox.addEventListener('click', function(e) {
      if (e.target === lightbox) {
        lightbox.style.display = 'none';
        document.body.style.overflow = '';
      }
    });

    document.addEventListener('keydown', function(e) {
      if (lightbox.style.display !== 'flex') return;

      switch (e.key) {
        case 'ArrowLeft':
          showImage(currentIndex - 1);
          break;
        case 'ArrowRight':
          showImage(currentIndex + 1);
          break;
        case 'Escape':
          lightbox.style.display = 'none';
          document.body.style.overflow = '';
          break;
      }
    });
  }

  function initAdditionalSpecsAccordion() {
    const accordionHeaders = document.querySelectorAll('.js-additional-specs-header');

    accordionHeaders.forEach(header => {
      header.addEventListener('click', function() {
        const parent = this.closest('.js-additional-specs');
        parent.classList.toggle('additional-specs--active');
        const content = parent.querySelector('.additional-specs__content');
      });
    });
  }

  function fixAddToCartButtons() {
    const atcForms = document.querySelectorAll('form[action="/cart/add"]');

    atcForms.forEach(form => {
      form.addEventListener('submit', function(e) {
        e.preventDefault();

        const formData = new FormData(form);
        const variantId = formData.get('id');
        const quantity = parseInt(formData.get('quantity')) || 1;

        if (!validateInventoryBeforeAdd(variantId, quantity)) {
          return;
        }

        // Add sections parameter to get cart drawer HTML
        formData.append('sections', 'cart-drawer');
        formData.append('sections_url', window.location.pathname);

        fetch('/cart/add.js', {
          method: 'POST',
          body: formData
        })
        .then(response => response.json())
        .then(data => {
          if (data.status) {
            showInventoryError(data.description || 'Unable to add item to cart.');
            return;
          }

          updateCartCount();
          initCartDataFetch();

          // Open the cart drawer
          const cartDrawer = document.querySelector('cart-drawer');
          if (cartDrawer) {
            // Update cart drawer contents
            if (data.sections && data.sections['cart-drawer']) {
              const cartDrawerElement = document.querySelector('#CartDrawer');
              if (cartDrawerElement) {
                const parser = new DOMParser();
                const htmlDoc = parser.parseFromString(data.sections['cart-drawer'], 'text/html');
                const newDrawerContent = htmlDoc.querySelector('.drawer__inner');
                if (newDrawerContent) {
                  const currentDrawerContent = cartDrawerElement.querySelector('.drawer__inner');
                  if (currentDrawerContent) {
                    currentDrawerContent.innerHTML = newDrawerContent.innerHTML;
                  }
                }
              }
            }

            // Open the drawer
            cartDrawer.classList.add('animate', 'active');
            document.body.classList.add('overflow-hidden');

            // Re-attach event listeners
            const overlay = cartDrawer.querySelector('#CartDrawer-Overlay');
            if (overlay) {
              overlay.addEventListener('click', function() {
                cartDrawer.classList.remove('active');
                document.body.classList.remove('overflow-hidden');
              });
            }
          } else {
            // Fallback to showing message if drawer not found
            showAddedToCartMessage();
          }
        })
        .catch(error => {
          console.error('Error adding to cart:', error);
          showInventoryError('Error adding to cart. Please try again.');
        });
      });
    });
  }

  function validateInventoryBeforeAdd(variantId, quantity) {
    const atcButton = document.querySelector(`[data-variant-id="${variantId}"][name="add"]`);
    if (!atcButton) return true;

    // Check if the add to cart button is disabled (sold out)
    if (atcButton.hasAttribute('disabled') || atcButton.getAttribute('aria-disabled') === 'true') {
      showInventoryError('This variant is sold out.');
      return false;
    }

    const inventoryManagement = atcButton.dataset.inventoryManagement;
    const inventoryPolicy = atcButton.dataset.inventoryPolicy;
    // Use default location inventory instead of total inventory
    const inventoryQuantity = atcButton.dataset.inventoryQuantity;

    if (inventoryManagement === 'shopify' &&
        inventoryPolicy !== 'continue' &&
        inventoryQuantity !== null &&
        inventoryQuantity !== 'null' &&
        inventoryQuantity !== '') {

      const defaultLocationInventory = parseInt(inventoryQuantity);
      if (isNaN(defaultLocationInventory)) return true;

      // Check if inventory is 0 or negative
      if (defaultLocationInventory <= 0) {
        showInventoryError('This variant is out of stock.');
        return false;
      }

      const currentCartQuantity = getCurrentCartQuantity(variantId);
      const totalQuantity = currentCartQuantity + quantity;

      if (totalQuantity > defaultLocationInventory) {
        const availableStock = Math.max(0, defaultLocationInventory - currentCartQuantity);
        showInventoryError(`Only ${availableStock} available in stock.`);
        return false;
      }
    }

    return true;
  }

  function showInventoryError(message) {
    const errorDiv = document.createElement('div');
    errorDiv.className = 'inventory-error';
    errorDiv.style.position = 'fixed';
    errorDiv.style.top = '20px';
    errorDiv.style.right = '20px';
    errorDiv.style.backgroundColor = '#ff6b6b';
    errorDiv.style.color = 'white';
    errorDiv.style.padding = '12px 20px';
    errorDiv.style.borderRadius = '8px';
    errorDiv.style.boxShadow = '0 4px 12px rgba(0,0,0,0.15)';
    errorDiv.style.zIndex = '9999';
    errorDiv.style.transform = 'translateY(-20px)';
    errorDiv.style.opacity = '0';
    errorDiv.style.transition = 'all 0.3s ease';
    errorDiv.textContent = message;

    document.body.appendChild(errorDiv);

    setTimeout(() => {
      errorDiv.style.transform = 'translateY(0)';
      errorDiv.style.opacity = '1';
    }, 10);

    setTimeout(() => {
      errorDiv.style.transform = 'translateY(-20px)';
      errorDiv.style.opacity = '0';

      setTimeout(() => {
        if (document.body.contains(errorDiv)) {
          document.body.removeChild(errorDiv);
        }
      }, 300);
    }, 4000);
  }

  function updateCartCount() {
    fetch('/cart.js')
      .then(response => response.json())
      .then(cart => {
        document.querySelectorAll('.cart-count-bubble').forEach(el => {
          const cartCountBubble = el.querySelector('span');
          if (cartCountBubble) {
            cartCountBubble.textContent = cart.item_count;
          }

          if (cart.item_count > 0) {
            el.classList.remove('hidden');
          }
        });
      });
  }

  function showAddedToCartMessage() {
    const message = document.createElement('div');
    message.className = 'cart-notification';
    message.style.position = 'fixed';
    message.style.top = '20px';
    message.style.right = '20px';
    message.style.backgroundColor = '#4a90e2';
    message.style.color = 'white';
    message.style.padding = '12px 20px';
    message.style.borderRadius = '8px';
    message.style.boxShadow = '0 4px 12px rgba(0,0,0,0.15)';
    message.style.zIndex = '9999';
    message.style.transform = 'translateY(-20px)';
    message.style.opacity = '0';
    message.style.transition = 'all 0.3s ease';
    message.textContent = 'Added to cart';

    document.body.appendChild(message);

    setTimeout(() => {
      message.style.transform = 'translateY(0)';
      message.style.opacity = '1';
    }, 10);

    setTimeout(() => {
      message.style.transform = 'translateY(-20px)';
      message.style.opacity = '0';

      setTimeout(() => {
        document.body.removeChild(message);
      }, 300);
    }, 3000);
  }

  function enhanceVariantSelection() {
    const variantSelects = document.querySelectorAll('.select__select');

    variantSelects.forEach(select => {
      if (!select.hasAttribute('aria-label')) {
        select.setAttribute('aria-label', 'Select product variant');
      }

      select.addEventListener('focus', function() {
        this.closest('.select').classList.add('select--focused');
      });

      select.addEventListener('blur', function() {
        this.closest('.select').classList.remove('select--focused');
      });

      select.addEventListener('change', function() {
        const wrapper = this.closest('.select');
        wrapper.classList.add('select--changed');

        setTimeout(() => {
          wrapper.classList.remove('select--changed');
        }, 500);
      });
    });

    const variantRadios = document.querySelectorAll('.form__radio');

    variantRadios.forEach(radio => {
      radio.addEventListener('change', function() {
        if (this.checked) {
          const label = this.nextElementSibling;

          const ripple = document.createElement('span');
          ripple.classList.add('radio-ripple');
          label.appendChild(ripple);

          setTimeout(() => {
            ripple.remove();
          }, 500);
        }
      });
    });
  }

  function enhanceQuantitySelector() {
    // The global QuantityInput class handles all quantity controls
    // Just ensure ATC button states are updated when quantities change
    const quantityInputs = document.querySelectorAll('quantity-input .quantity__input');

    quantityInputs.forEach(input => {
      if (input.dataset.quantityVariantId) {
        // Initial ATC button state update
        updateATCButtonState(input);

        // Listen for changes to update ATC button state
        input.addEventListener('change', function() {
          updateATCButtonState(this);
        });

        input.addEventListener('input', function() {
          updateATCButtonState(this);
        });
      }
    });
  }

  function updateATCButtonState(input) {
    const variantId = input.dataset.quantityVariantId;
    if (!variantId) return;

    const atcButton = document.querySelector(`[data-variant-id="${variantId}"][name="add"]`);
    if (!atcButton) return;

    const currentQuantity = parseInt(input.value) || 0;
    const inventoryLimit = getInventoryLimit(input);
    const currentCartQuantity = getCurrentCartQuantity(variantId);
    const totalQuantity = currentCartQuantity + currentQuantity;

    if (inventoryLimit !== null && totalQuantity > inventoryLimit) {
      atcButton.disabled = true;
      atcButton.setAttribute('aria-disabled', 'true');
      const buttonText = atcButton.querySelector('span');
      if (buttonText) {
        buttonText.textContent = 'Not enough stock';
      }
    } else {
      atcButton.disabled = false;
      atcButton.removeAttribute('aria-disabled');
      const buttonText = atcButton.querySelector('span');
      if (buttonText) {
        buttonText.textContent = 'Add to cart';
      }
    }
  }

  function getInventoryLimit(input) {
    const inventoryManagement = input.dataset.inventoryManagement;
    const inventoryPolicy = input.dataset.inventoryPolicy;
    // Use default location inventory instead of total inventory
    const inventoryQuantity = input.dataset.inventoryQuantity;

    if (inventoryManagement === 'shopify' &&
        inventoryPolicy !== 'continue' &&
        inventoryQuantity !== null &&
        inventoryQuantity !== 'null' &&
        inventoryQuantity !== '') {

      const defaultLocationInventory = parseInt(inventoryQuantity);
      return isNaN(defaultLocationInventory) ? null : defaultLocationInventory;
    }

    return null;
  }

  function getCurrentCartQuantity(variantId) {
    if (!variantId || !window.cartData) return 0;

    const cartItem = window.cartData.items.find(item => item.variant_id == variantId);
    return cartItem ? cartItem.quantity : 0;
  }

  // Function to update Add to Cart button data attributes
  function updateAddToCartButtonAttributes(variantData, inventoryData) {
    const addToCartButton = document.querySelector(`#ProductSubmitButton-{{ section.id }}`);
    if (!addToCartButton || !variantData) return;

    // Update button data attributes
    addToCartButton.dataset.variantId = variantData.id;
    addToCartButton.dataset.inventoryManagement = variantData.inventory_management;
    addToCartButton.dataset.inventoryPolicy = variantData.inventory_policy;

    if (inventoryData) {
      addToCartButton.dataset.inventoryQuantity = inventoryData.defaultLocationInventory;
    } else {
      addToCartButton.dataset.inventoryQuantity = variantData.inventory_quantity;
    }

    // Update button state based on availability
    const buttonText = addToCartButton.querySelector('span');
    if (!variantData.available) {
      addToCartButton.setAttribute('disabled', 'disabled');
      addToCartButton.setAttribute('aria-disabled', 'true');
      if (buttonText) {
        buttonText.textContent = 'Sold out';
      }
    } else {
      addToCartButton.removeAttribute('disabled');
      addToCartButton.removeAttribute('aria-disabled');
      if (buttonText) {
        buttonText.textContent = 'Add to cart';
      }
    }

    console.log(`Updated Add to Cart button for variant ${variantData.id}, available: ${variantData.available}`);
  }

  // Function to update inventory data attributes when variant changes
  function updateInventoryDataAttributes(variantData) {
    const quantityInput = document.querySelector(`#Quantity-{{ section.id }}`);
    if (!quantityInput || !variantData) return;

    // Reset quantity immediately when variant changes (before async operations)
    const minValue = parseInt(quantityInput.getAttribute('min')) || 1;

    // Use multiple timeouts to ensure this runs after any other event handlers
    setTimeout(() => {
      quantityInput.value = minValue;
      console.log(`First reset: quantity to ${minValue} for variant ${variantData.id}`);
    }, 0);

    setTimeout(() => {
      quantityInput.value = minValue;
      // Trigger change event to ensure all listeners are notified
      quantityInput.dispatchEvent(new Event('change', { bubbles: true }));
      console.log(`Second reset: quantity to ${minValue} for variant ${variantData.id}`);
    }, 50);

    // Fetch variant-specific inventory data from the page
    fetch(`{{ product.url }}?variant=${variantData.id}&section_id={{ section.id }}`)
      .then(response => response.text())
      .then(html => {
        const parser = new DOMParser();
        const doc = parser.parseFromString(html, 'text/html');
        const newQuantityInput = doc.querySelector(`#Quantity-{{ section.id }}`);
        const newAddToCartButton = doc.querySelector(`#ProductSubmitButton-{{ section.id }}`);

        if (newQuantityInput) {
          // Get the calculated inventory data from the server-rendered HTML
          const defaultLocationInventory = newQuantityInput.dataset.inventoryQuantity;
          const totalInventory = newQuantityInput.dataset.totalInventoryQuantity;
          const maxAttribute = newQuantityInput.getAttribute('max');

          // Update quantity input data attributes
          quantityInput.dataset.quantityVariantId = variantData.id;
          quantityInput.dataset.inventoryManagement = variantData.inventory_management;
          quantityInput.dataset.inventoryPolicy = variantData.inventory_policy;
          quantityInput.dataset.inventoryQuantity = defaultLocationInventory;
          quantityInput.dataset.totalInventoryQuantity = totalInventory;

          // Update max attribute
          if (maxAttribute && maxAttribute !== 'null') {
            quantityInput.setAttribute('max', maxAttribute);
          } else {
            quantityInput.removeAttribute('max');
          }

          // Update Add to Cart button with fresh data from server
          if (newAddToCartButton) {
            const currentAddToCartButton = document.querySelector(`#ProductSubmitButton-{{ section.id }}`);
            if (currentAddToCartButton) {
              // Copy all data attributes from the fresh button
              Array.from(newAddToCartButton.attributes).forEach(attr => {
                if (attr.name.startsWith('data-')) {
                  currentAddToCartButton.setAttribute(attr.name, attr.value);
                }
              });

              // Copy disabled state and button text
              if (newAddToCartButton.hasAttribute('disabled')) {
                currentAddToCartButton.setAttribute('disabled', 'disabled');
                currentAddToCartButton.setAttribute('aria-disabled', 'true');
              } else {
                currentAddToCartButton.removeAttribute('disabled');
                currentAddToCartButton.removeAttribute('aria-disabled');
              }

              const newButtonText = newAddToCartButton.querySelector('span');
              const currentButtonText = currentAddToCartButton.querySelector('span');
              if (newButtonText && currentButtonText) {
                currentButtonText.textContent = newButtonText.textContent;
              }
            }
          }

          // Trigger change event to update any dependent UI
          quantityInput.dispatchEvent(new Event('change', { bubbles: true }));

          console.log(`Updated RCL inventory for variant ${variantData.id}: ${defaultLocationInventory} (was ${totalInventory} total), available: ${variantData.available}`);
        }
      })
      .catch(error => {
        console.error('Error fetching variant inventory data:', error);
        // Fallback to basic variant data
        quantityInput.dataset.quantityVariantId = variantData.id;
        quantityInput.dataset.inventoryManagement = variantData.inventory_management;
        quantityInput.dataset.inventoryPolicy = variantData.inventory_policy;
        quantityInput.dataset.inventoryQuantity = variantData.inventory_quantity;
        quantityInput.dataset.totalInventoryQuantity = variantData.inventory_quantity;

        if (variantData.inventory_management === 'shopify' &&
            variantData.inventory_policy !== 'continue' &&
            variantData.inventory_quantity !== null) {
          quantityInput.setAttribute('max', variantData.inventory_quantity);
        } else {
          quantityInput.removeAttribute('max');
        }

        // Update button with fallback data
        updateAddToCartButtonAttributes(variantData);
      });
  }

  function showInventoryWarning(availableStock) {
    const message = document.createElement('div');
    message.className = 'inventory-warning';
    message.style.position = 'fixed';
    message.style.top = '20px';
    message.style.right = '20px';
    message.style.backgroundColor = '#ff6b6b';
    message.style.color = 'white';
    message.style.padding = '12px 20px';
    message.style.borderRadius = '8px';
    message.style.boxShadow = '0 4px 12px rgba(0,0,0,0.15)';
    message.style.zIndex = '9999';
    message.style.transform = 'translateY(-20px)';
    message.style.opacity = '0';
    message.style.transition = 'all 0.3s ease';
    message.textContent = `Only ${availableStock} available in stock`;

    document.body.appendChild(message);

    setTimeout(() => {
      message.style.transform = 'translateY(0)';
      message.style.opacity = '1';
    }, 10);

    setTimeout(() => {
      message.style.transform = 'translateY(-20px)';
      message.style.opacity = '0';

      setTimeout(() => {
        if (document.body.contains(message)) {
          document.body.removeChild(message);
        }
      }, 300);
    }, 3000);
  }

  function initCartDataFetch() {
    if (!window.Shopify || !window.Shopify.routes || !window.Shopify.routes.root) {
      return;
    }

    fetch(window.Shopify.routes.root + 'cart.js')
      .then(response => response.json())
      .then(cart => {
        window.cartData = cart;
      })
      .catch(() => {
        window.cartData = { items: [] };
      });
  }

  function initAnimations() {
    const productElements = document.querySelectorAll(
      '.product__title, .price, .product-form__input, .product__description, .product-form__buttons, .product-metafields, .product-additional-specs, .product-features'
    );

    if ('IntersectionObserver' in window) {
      const observerOptions = {
        threshold: 0.1,
        rootMargin: '0px 0px -50px 0px'
      };

      const observer = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
          if (entry.isIntersecting) {
            entry.target.style.opacity = "1";
            entry.target.style.transform = "translateY(0)";
            observer.unobserve(entry.target);
          }
        });
      }, observerOptions);

      productElements.forEach((element, index) => {
        element.style.opacity = "0";
        element.style.transform = "translateY(20px)";
        element.style.transition = `opacity 0.5s ease ${index * 0.1}s, transform 0.5s ease ${index * 0.1}s`;

        observer.observe(element);
      });
    } else {
      productElements.forEach(element => {
        element.style.opacity = "1";
        element.style.transform = "translateY(0)";
      });
    }

    const metafieldColumns = document.querySelectorAll('.metafield-column');

    metafieldColumns.forEach(column => {
      column.addEventListener('mouseenter', function() {
        const iconImg = this.querySelector('.metafield-column__icon img');
        const iconSvg = this.querySelector('.metafield-column__icon svg');

        if (iconImg) iconImg.classList.add('icon-pulse');
        if (iconSvg) iconSvg.classList.add('icon-pulse');
      });

      column.addEventListener('mouseleave', function() {
        const iconImg = this.querySelector('.metafield-column__icon img');
        const iconSvg = this.querySelector('.metafield-column__icon svg');

        if (iconImg) iconImg.classList.remove('icon-pulse');
        if (iconSvg) iconSvg.classList.remove('icon-pulse');
      });
    });

    const addToCartBtn = document.querySelector('.product-form__submit');
    if (addToCartBtn) {
      setTimeout(() => {
        addToCartBtn.classList.add('button-pulse');

        setTimeout(() => {
          addToCartBtn.classList.remove('button-pulse');
        }, 1000);
      }, 2000);
    }
  }
</script>

{% schema %}
  {
    "name": "Product information",
    "tag": "section",
    "class": "section",
    "settings": [
      {
        "type": "checkbox",
        "id": "enable_sticky_info",
        "default": true,
        "label": "t:sections.main-product.settings.enable_sticky_info.label"
      },
      {
        "type": "color_scheme",
        "id": "color_scheme",
        "label": "t:sections.all.colors.label",
        "default": "scheme-1"
      },
      {
        "type": "header",
        "content": "Media"
      },
      {
        "type": "select",
        "id": "media_size",
        "options": [
          {
            "value": "small",
            "label": "t:sections.main-product.settings.media_size.options__1.label"
          },
          {
            "value": "medium",
            "label": "t:sections.main-product.settings.media_size.options__2.label"
          },
          {
            "value": "large",
            "label": "t:sections.main-product.settings.media_size.options__3.label"
          }
        ],
        "default": "large",
        "label": "t:sections.main-product.settings.media_size.label"
      },
      {
        "type": "select",
        "id": "gallery_layout",
        "options": [
          {
            "value": "stacked",
            "label": "t:sections.main-product.settings.gallery_layout.options__1.label"
          },
          {
            "value": "thumbnail",
            "label": "t:sections.main-product.settings.gallery_layout.options__2.label"
          },
          {
            "value": "thumbnail_slider",
            "label": "t:sections.main-product.settings.gallery_layout.options__3.label"
          }
        ],
        "default": "thumbnail_slider",
        "label": "t:sections.main-product.settings.gallery_layout.label"
      },
      {
        "type": "select",
        "id": "media_position",
        "options": [
          {
            "value": "left",
            "label": "t:sections.main-product.settings.media_position.options__1.label"
          },
          {
            "value": "right",
            "label": "t:sections.main-product.settings.media_position.options__2.label"
          }
        ],
        "default": "left",
        "label": "t:sections.main-product.settings.media_position.label"
      },
      {
        "type": "select",
        "id": "image_zoom",
        "options": [
          {
            "value": "lightbox",
            "label": "Lightbox"
          },
          {
            "value": "hover",
            "label": "Hover"
          },
          {
            "value": "none",
            "label": "None"
          }
        ],
        "default": "lightbox",
        "label": "Image zoom"
      },
      {
        "type": "select",
        "id": "mobile_thumbnails",
        "options": [
          {
            "value": "show",
            "label": "Show"
          },
          {
            "value": "hide",
            "label": "Hide"
          }
        ],
        "default": "hide",
        "label": "Mobile thumbnails"
      },
      {
        "type": "checkbox",
        "id": "enable_video_looping",
        "default": false,
        "label": "t:sections.main-product.settings.enable_video_looping.label"
      },
      {
        "type": "header",
        "content": "t:sections.all.padding.section_padding_heading"
      },
      {
        "type": "range",
        "id": "padding_top",
        "min": 0,
        "max": 100,
        "step": 4,
        "unit": "px",
        "label": "t:sections.all.padding.padding_top",
        "default": 36
      },
      {
        "type": "range",
        "id": "padding_bottom",
        "min": 0,
        "max": 100,
        "step": 4,
        "unit": "px",
        "label": "t:sections.all.padding.padding_bottom",
        "default": 36
      }
    ],
    "blocks": [
      {
        "type": "@app"
      },
      {
        "type": "text",
        "name": "t:sections.main-product.blocks.text.name",
        "settings": [
          {
            "type": "text",
            "id": "text",
            "default": "Text block",
            "label": "t:sections.main-product.blocks.text.settings.text.label"
          },
          {
            "type": "select",
            "id": "text_style",
            "options": [
              {
                "value": "body",
                "label": "t:sections.main-product.blocks.text.settings.text_style.options__1.label"
              },
              {
                "value": "subtitle",
                "label": "t:sections.main-product.blocks.text.settings.text_style.options__2.label"
              },
              {
                "value": "uppercase",
                "label": "t:sections.main-product.blocks.text.settings.text_style.options__3.label"
              }
            ],
            "default": "body",
            "label": "t:sections.main-product.blocks.text.settings.text_style.label"
          }
        ]
      },
      {
        "type": "title",
        "name": "t:sections.main-product.blocks.title.name",
        "limit": 1
      },
      {
        "type": "price",
        "name": "t:sections.main-product.blocks.price.name",
        "limit": 1
      },
      {
        "type": "quantity_selector",
        "name": "t:sections.main-product.blocks.quantity_selector.name",
        "limit": 1
      },
      {
        "type": "variant_picker",
        "name": "t:sections.main-product.blocks.variant_picker.name",
        "limit": 1,
        "settings": [
          {
            "type": "select",
            "id": "picker_type",
            "options": [
              {
                "value": "dropdown",
                "label": "t:sections.main-product.blocks.variant_picker.settings.picker_type.options__1.label"
              },
              {
                "value": "button",
                "label": "t:sections.main-product.blocks.variant_picker.settings.picker_type.options__2.label"
              }
            ],
            "default": "dropdown",
            "label": "t:sections.main-product.blocks.variant_picker.settings.picker_type.label"
          }
        ]
      },
      {
        "type": "texture_gallery",
        "name": "Gallery of Textures",
        "limit": 1,
        "settings": [
          {
            "type": "text",
            "id": "title",
            "label": "Title",
            "default": "Gallery of textures"
          },
          {
            "type": "checkbox",
            "id": "show_count",
            "label": "Show count of textures",
            "default": true
          },
          {
            "type": "checkbox",
            "id": "open_by_default",
            "label": "Open by default",
            "default": false
          }
        ]
      },
      {
        "type": "application_technologies",
        "name": "Application Technologies",
        "limit": 1,
        "settings": [
          {
            "type": "text",
            "id": "title",
            "label": "Title",
            "default": "Application technologies"
          },
          {
            "type": "checkbox",
            "id": "open_by_default",
            "label": "Open by default",
            "default": true
          }
        ]
      },
      {
        "type": "buy_buttons",
        "name": "t:sections.main-product.blocks.buy_buttons.name",
        "limit": 1,
        "settings": [
          {
            "type": "checkbox",
            "id": "show_dynamic_checkout",
            "default": true,
            "label": "t:sections.main-product.blocks.buy_buttons.settings.show_dynamic_checkout.label",
            "info": "t:sections.main-product.blocks.buy_buttons.settings.show_dynamic_checkout.info"
          }
        ]
      },
      {
        "type": "description",
        "name": "t:sections.main-product.blocks.description.name",
        "limit": 1
      },
      {
        "type": "product_metafields",
        "name": "Product Metafields",
        "limit": 1,
        "settings": [
          {
            "type": "header",
            "content": "Icons for Metafields"
          },
          {
            "type": "image_picker",
            "id": "application_icon",
            "label": "Application Icon",
            "info": "28 x 28px recommended"
          },
          {
            "type": "image_picker",
            "id": "composition_icon",
            "label": "Composition Icon",
            "info": "28 x 28px recommended"
          },
          {
            "type": "image_picker",
            "id": "consumption_icon",
            "label": "Consumption Icon",
            "info": "28 x 28px recommended"
          }
        ]
      },
      {
        "type": "collapsible_tab",
        "name": "t:sections.main-product.blocks.collapsible_tab.name",
        "settings": [
          {
            "type": "text",
            "id": "heading",
            "default": "Collapsible tab",
            "label": "t:sections.main-product.blocks.collapsible_tab.settings.heading.label"
          },
          {
            "type": "select",
            "id": "icon",
            "options": [
              {
                "value": "none",
                "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__1.label"
              },
              {
                "value": "box",
                "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__2.label"
              },
              {
                "value": "chat_bubble",
                "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__3.label"
              },
              {
                "value": "check_mark",
                "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__4.label"
              },
              {
                "value": "leaf",
                "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__9.label"
              },
              {
                "value": "truck",
                "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__19.label"
              }
            ],
            "default": "check_mark",
            "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.label"
          },
          {
            "type": "richtext",
            "id": "content",
            "label": "t:sections.main-product.blocks.collapsible_tab.settings.content.label"
          },
          {
            "type": "page",
            "id": "page",
            "label": "t:sections.main-product.blocks.collapsible_tab.settings.page.label"
          }
        ]
      },
      {
        "type": "rating",
        "name": "t:sections.main-product.blocks.rating.name",
        "limit": 1,
        "settings": [
          {
            "type": "paragraph",
            "content": "t:sections.main-product.blocks.rating.settings.paragraph.content"
          }
        ]
      },
      {
        "type": "share",
        "name": "t:sections.main-product.blocks.share.name",
        "limit": 1,
        "settings": [
          {
            "type": "text",
            "id": "share_label",
            "label": "t:sections.main-product.blocks.share.settings.text.label",
            "default": "Share"
          }
        ]
      },
      {
        "type": "quantity_calculator",
        "name": "Quantity Calculator",
        "limit": 1,
        "settings": [
          {
            "type": "text",
            "id": "button_text",
            "label": "Calculator button text",
            "default": "Calculate quantity"
          },
          {
            "type": "text",
            "id": "modal_heading",
            "label": "Calculator modal heading",
            "default": "Quantity Calculator"
          }
        ]
      },
      {
        "type": "custom_liquid",
        "name": "t:sections.main-product.blocks.custom_liquid.name",
        "settings": [
          {
            "type": "liquid",
            "id": "custom_liquid",
            "label": "Custom Liquid"
          }
        ]
      }
    ],
    "presets": [
      {
        "name": "Product information",
        "blocks": [
          {
            "type": "title"
          },
          {
            "type": "price"
          },
          {
            "type": "variant_picker"
          },
          {
            "type": "quantity_selector"
          },
          {
            "type": "buy_buttons"
          },
          {
            "type": "description"
          },
          {
            "type": "product_metafields"
          },
          {
            "type": "texture_gallery"
          },
          {
            "type": "application_technologies"
          },
          {
            "type": "share"
          }
        ]
      }
    ]
  }
  {% endschema %}