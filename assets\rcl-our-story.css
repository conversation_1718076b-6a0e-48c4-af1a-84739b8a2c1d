/* Base Styles with Modern Reset */
.our-story {
  --shadow-sm: 0 2px 8px rgba(0, 0, 0, 0.08);
  --shadow-md: 0 12px 24px rgba(0, 0, 0, 0.12);
  --shadow-lg: 0 25px 50px rgba(0, 0, 0, 0.18);
  --transition-slow: 0.7s cubic-bezier(0.22, 1, 0.36, 1);
  --transition-base: 0.4s cubic-bezier(0.22, 1, 0.36, 1);
  --transition-fast: 0.2s cubic-bezier(0.22, 1, 0.36, 1);
  overflow: hidden;
  position: relative;
}

.our-story__container {
  display: flex;
  align-items: center;
  gap: 80px;
  position: relative;
  z-index: 1;
}

.our-story__container.image-right {
  flex-direction: row-reverse;
}

/* Image Styling with Glass Morphism */
.our-story__image-container {
  flex: 1;
  position: relative;
  z-index: 2;
  transform: perspective(1000px) rotateY(3deg) rotateX(2deg);
  transition: transform var(--transition-base);
  will-change: transform;
}

.our-story__container.image-right .our-story__image-container {
  transform: perspective(1000px) rotateY(-3deg) rotateX(2deg);
}

.our-story__image-container:hover {
  transform: perspective(1000px) rotateY(1deg) rotateX(1deg) translateY(-10px);
}

.our-story__container.image-right .our-story__image-container:hover {
  transform: perspective(1000px) rotateY(-1deg) rotateX(1deg) translateY(-10px);
}

.our-story__image,
.our-story__placeholder {
  width: 100%;
  height: auto;
  border-radius: 16px;
  box-shadow: var(--shadow-md);
  overflow: hidden;
  transition: all var(--transition-base);
  transform: translateZ(0);
  filter: brightness(1.02) contrast(1.05);
  position: relative;
}

.our-story__image-container:after {
  content: '';
  position: absolute;
  inset: 0;
  border-radius: 16px;
  box-shadow: inset 0 0 0 1px rgba(255, 255, 255, 0.1);
  background: linear-gradient(
    135deg,
    rgba(255, 255, 255, 0.12) 0%,
    rgba(255, 255, 255, 0) 50%
  );
  pointer-events: none;
  z-index: 2;
}

.our-story__image-container:before {
  content: '';
  position: absolute;
  inset: -5px;
  border-radius: 20px;
  background: linear-gradient(
    135deg,
    rgba(255, 255, 255, 0.2) 0%,
    rgba(255, 255, 255, 0) 60%,
    rgba(0, 0, 0, 0.1) 100%
  );
  z-index: 0;
  opacity: 0;
  transition: opacity var(--transition-base);
}

.our-story__image-container:hover:before {
  opacity: 1;
}

.our-story__image-container:hover .our-story__image {
  transform: scale(1.035);
  box-shadow: var(--shadow-lg);
  filter: brightness(1.05) contrast(1.08);
}

.our-story__placeholder {
  aspect-ratio: 4/3;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(
    135deg, 
    rgba(240, 240, 240, 0.6) 0%,
    rgba(255, 255, 255, 0.9) 100%
  );
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
}

.our-story__placeholder .placeholder-svg {
  width: 35%;
  height: 35%;
  opacity: 0.7;
  filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.1));
  transition: all var(--transition-base);
}

.our-story__placeholder:hover .placeholder-svg {
  opacity: 0.9;
  transform: scale(1.1) rotate(5deg);
}

/* Year Badge with Modern Style */
.our-story__year-badge {
  position: absolute;
  bottom: -20px;
  right: -10px;
  color: white;
  padding: 14px 28px;
  font-weight: 800;
  border-radius: 12px;
  letter-spacing: 0.05em;
  box-shadow: var(--shadow-md);
  z-index: 5;
  transform: translateZ(30px);
  transition: all var(--transition-base);
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
  backdrop-filter: blur(5px);
  -webkit-backdrop-filter: blur(5px);
}

.our-story__image-container:hover .our-story__year-badge {
  transform: translateZ(50px) translateY(-8px) translateX(-5px);
  box-shadow: var(--shadow-lg);
}

/* Text Content with Elegant Typography */
.our-story__text-container {
  flex: 1;
  position: relative;
  z-index: 2;
}

.our-story__text-inner {
  position: relative;
}

.our-story__heading {
  font-weight: 900;
  margin-bottom: 35px;
  position: relative;
  padding-left: 24px;
  line-height: 1.15;
  letter-spacing: -0.03em;
}

@keyframes textShimmer {
  0% { background-position: 0% 50%; }
  100% { background-position: 100% 50%; }
}

.our-story__heading-line {
  position: absolute;
  left: 0;
  top: 10px;
  bottom: 10px;
  width: 8px;
  border-radius: 20px;
  transform-origin: bottom;
  animation: lineGrow 0.6s var(--transition-slow) forwards;
  background-size: 200% 200% !important;
  animation: lineGrow 0.6s var(--transition-slow) forwards, gradientFlow 8s ease infinite;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.15);
}

@keyframes lineGrow {
  0% { transform: scaleY(0); }
  100% { transform: scaleY(1); }
}

@keyframes gradientFlow {
  0% { background-position: 0% 50%; }
  50% { background-position: 100% 50%; }
  100% { background-position: 0% 50%; }
}

.our-story__text {
  line-height: 1.9;
  animation: textFadeIn 0.8s ease-out 0.3s forwards;
  opacity: 0;
  transform: translateY(20px);
  position: relative;
}

@keyframes textFadeIn {
  0% { opacity: 0; transform: translateY(20px); }
  100% { opacity: 1; transform: translateY(0); }
}

.our-story__text p {
  margin-bottom: 20px;
  position: relative;
  /*text-shadow: 0 1px 1px rgba(255, 255, 255, 0.8);*/
}

.our-story__text p:last-child {
  margin-bottom: 0;
}

/* First letter emphasis removed as requested */

.our-story__text a {
  position: relative;
  text-decoration: none;
  font-weight: 600;
  display: inline-block;
  transition: all var(--transition-fast);
  padding: 0 4px;
  z-index: 1;
}

.our-story__text a::before {
  content: '';
  position: absolute;
  z-index: -1;
  bottom: 2px;
  left: 0;
  width: 100%;
  height: 30%;
  opacity: 0.3;
  transition: height var(--transition-fast), opacity var(--transition-fast);
}

.our-story__text a:hover::before {
  height: 100%;
  opacity: 0.15;
}

/* Background Shapes for Visual Interest */
.our-story::before,
.our-story::after {
  content: '';
  position: absolute;
  border-radius: 50%;
  z-index: 0;
  opacity: 0.05;
  filter: blur(50px);
}

.our-story::before {
  width: 40vw;
  height: 40vw;
  background: linear-gradient(135deg, #6e8efb 0%, #a777e3 100%);
  top: -20vw;
  right: -20vw;
  animation: floatBlob 15s ease-in-out infinite alternate;
}

.our-story::after {
  width: 30vw;
  height: 30vw;
  background: linear-gradient(135deg, #58caa9 0%, #41b882 100%);
  bottom: -15vw;
  left: -15vw;
  animation: floatBlob 20s ease-in-out 2s infinite alternate-reverse;
}

@keyframes floatBlob {
  0% { transform: translate(0, 0) scale(1); }
  100% { transform: translate(5%, 5%) scale(1.1); }
}

/* Responsive Design */
@media screen and (max-width: 989px) {
  .our-story__container,
  .our-story__container.image-right {
    flex-direction: column;
    gap: 60px;
  }
  
  .our-story__image-container,
  .our-story__container.image-right .our-story__image-container {
    transform: perspective(1000px) rotateY(0) rotateX(0);
    width: 90%;
    margin: 0 auto;
  }
  
  .our-story__heading {
    text-align: left;
  }
  
  .our-story__year-badge {
    bottom: -15px;
    right: 20px;
    padding: 12px 24px;
  }
}

@media screen and (max-width: 749px) {
  .our-story {
    overflow: hidden;
  }
  
  .our-story__image-container {
    width: 100%;
  }
  
  .our-story__heading {
    padding-left: 20px;
  }
  
  .our-story__heading-line {
    width: 6px;
  }
  
  .our-story__text {
    line-height: 1.7;
  }
  
  .our-story__year-badge {
    padding: 10px 20px;
    right: 10px;
  }
  
  .our-story::before,
  .our-story::after {
    opacity: 0.03;
  }
}