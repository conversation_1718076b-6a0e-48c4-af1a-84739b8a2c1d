{{ 'rcl-niche-market.css' | asset_url | stylesheet_tag: preload: true }}

{% style %}
  .section-{{ section.id }}.market-presence-section {
    padding-top: {{ section.settings.padding_top }}px;
    padding-bottom: {{ section.settings.padding_bottom }}px;
    background-color: {{ section.settings.background_color }};
    overflow: hidden;
  }
{% endstyle %}

<div class="section-{{ section.id }} market-presence-section">
  <div class="market-presence-container">
    <div class="section-header text-center">
      <h2 class="section-title {% if section.settings.enable_animations %}animate slide-up{% endif %}" style="color: {{ section.settings.title_color }};">{{ section.settings.title }}</h2>
    </div>
    
    <div class="market-presence-content {{ section.settings.layout }} {% if section.settings.equal_height %}equal-height{% endif %}">
      {% case section.settings.media_type %}
        {% when 'single_image' %}
          <div class="market-presence-media {{ section.settings.image_ratio }} {{ section.settings.image_style }} {% if section.settings.enable_animations %}animate {% if section.settings.layout == 'image_left' %}slide-right{% else %}slide-left{% endif %}{% endif %}">
            {%- if section.settings.main_image -%}
              {{ section.settings.main_image | image_url: width: 1000 | image_tag: 
                loading: 'lazy',
                class: 'main-image',
                alt: section.settings.title,
                sizes: '(min-width: 992px) 50vw, 100vw'
              }}
            {%- else -%}
              {{ 'image' | placeholder_svg_tag: 'main-image' }}
            {%- endif -%}
          </div>
        
        {% when 'stacked_images' %}
          <div class="market-presence-media {{ section.settings.image_style }} {% if section.settings.enable_animations %}animate {% if section.settings.layout == 'image_left' %}slide-right{% else %}slide-left{% endif %}{% endif %}">
            <div class="stacked-images-container">
              {%- if section.settings.main_image -%}
                {{ section.settings.main_image | image_url: width: 1000 | image_tag: 
                  loading: 'lazy',
                  class: 'stacked-image',
                  alt: section.settings.title,
                  sizes: '(min-width: 992px) 50vw, 100vw'
                }}
              {%- else -%}
                {{ 'image' | placeholder_svg_tag: 'stacked-image' }}
              {%- endif -%}
              
              {%- if section.settings.secondary_image -%}
                <div class="secondary-image-container">
                  {{ section.settings.secondary_image | image_url: width: 600 | image_tag: 
                    loading: 'lazy',
                    class: 'secondary-image',
                    alt: section.settings.title,
                    sizes: '(min-width: 992px) 25vw, 40vw'
                  }}
                </div>
              {%- endif -%}
              
              {%- if section.settings.tertiary_image -%}
                <div class="tertiary-image-container">
                  {{ section.settings.tertiary_image | image_url: width: 600 | image_tag: 
                    loading: 'lazy',
                    class: 'tertiary-image',
                    alt: section.settings.title,
                    sizes: '(min-width: 992px) 25vw, 40vw'
                  }}
                </div>
              {%- endif -%}
            </div>
          </div>
        
        {% when 'image_collage' %}
          <div class="market-presence-media {% if section.settings.enable_animations %}animate {% if section.settings.layout == 'image_left' %}slide-right{% else %}slide-left{% endif %}{% endif %}">
            <div class="image-collage">
              <div class="collage-image collage-main">
                {%- if section.settings.main_image -%}
                  {{ section.settings.main_image | image_url: width: 800 | image_tag: 
                    loading: 'lazy',
                    alt: section.settings.title
                  }}
                {%- else -%}
                  {{ 'image' | placeholder_svg_tag }}
                {%- endif -%}
              </div>
              
              <div class="collage-image collage-secondary">
                {%- if section.settings.secondary_image -%}
                  {{ section.settings.secondary_image | image_url: width: 600 | image_tag: 
                    loading: 'lazy',
                    alt: section.settings.title
                  }}
                {%- else -%}
                  {{ 'image' | placeholder_svg_tag }}
                {%- endif -%}
              </div>
              
              <div class="collage-image collage-tertiary">
                {%- if section.settings.tertiary_image -%}
                  {{ section.settings.tertiary_image | image_url: width: 600 | image_tag: 
                    loading: 'lazy',
                    alt: section.settings.title
                  }}
                {%- else -%}
                  {{ 'image' | placeholder_svg_tag }}
                {%- endif -%}
              </div>
            </div>
          </div>
        
        {% when 'video' %}
          <div class="market-presence-media {% if section.settings.enable_animations %}animate {% if section.settings.layout == 'image_left' %}slide-right{% else %}slide-left{% endif %}{% endif %}">
            <div class="video-container {{ section.settings.image_style }}">
              <div class="video-aspect-ratio">
                <div id="videoPlayer" data-url="{{ section.settings.video_url }}" data-autoplay="{{ section.settings.autoplay }}" data-loop="{{ section.settings.loop }}"></div>
                
                {%- if section.settings.video_cover_image and section.settings.autoplay != true -%}
                  <div id="videoCover" class="video-cover" style="background-image: url('{{ section.settings.video_cover_image | image_url: width: 1000 }}');">
                    <div class="play-button">
                      <svg viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                        <path d="M8 5v14l11-7z"/>
                      </svg>
                    </div>
                  </div>
                {%- endif -%}
              </div>
            </div>
          </div>
          
        {% when 'self_hosted_video' %}
          <div class="market-presence-media {% if section.settings.enable_animations %}animate {% if section.settings.layout == 'image_left' %}slide-right{% else %}slide-left{% endif %}{% endif %}">
            <div class="self-hosted-video-container {{ section.settings.image_style }} {{ section.settings.self_hosted_video_color }}">
              {%- if section.settings.self_hosted_video != blank -%}
                <video
                  {% if section.settings.autoplay %}autoplay muted{% endif %}
                  {% if section.settings.loop %}loop{% endif %}
                  controls
                  preload="metadata"
                  {% if section.settings.video_cover_image %}poster="{{ section.settings.video_cover_image | image_url: width: 1000 }}"{% endif %}
                  playsinline
                >
                  <source src="{{ section.settings.self_hosted_video | file_url }}" type="video/mp4">
                  Your browser does not support the video tag.
                </video>
              {%- else -%}
                <div class="video-aspect-ratio" style="background-color: #f4f4f4;">
                  <div style="position: absolute; top: 50%; left: 50%; transform: translate(-50%, -50%); text-align: center; color: #888;">
                    <svg width="48" height="48" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" style="margin: 0 auto 10px;">
                      <path d="M21 12C21 16.9706 16.9706 21 12 21C7.02944 21 3 16.9706 3 12C3 7.02944 7.02944 3 12 3C16.9706 3 21 7.02944 21 12Z" stroke="#888" stroke-width="2"/>
                      <path d="M15 12L10 15V9L15 12Z" stroke="#888" stroke-width="2" stroke-linejoin="round"/>
                    </svg>
                    <p>Please upload a video file</p>
                  </div>
                </div>
              {%- endif -%}
            </div>
          </div>
      {% endcase %}
      
      <div class="market-presence-text {% if section.settings.enable_animations %}animate {% if section.settings.layout == 'image_left' %}slide-left{% else %}slide-right{% endif %}{% endif %}">
        <div class="market-presence-main-text">
          {{ section.settings.text }}
        </div>
        
        <div class="market-segments">
          {%- for block in section.blocks -%}
            {%- if block.type == 'market_segment' -%}
              <div class="market-segment-item {% if section.settings.enable_animations %}animate fade-in{% endif %}" {{ block.shopify_attributes }} style="animation-delay: {{ forloop.index | times: 0.15 }}s">
                <div class="segment-icon {{ block.settings.icon }}"></div>
                <h3 class="segment-title">{{ block.settings.title }}</h3>
                <div class="segment-description">{{ block.settings.content }}</div>
              </div>
            {%- endif -%}
          {%- endfor -%}
        </div>
      </div>
    </div>
  </div>
</div>

{% if section.settings.media_type == 'video' %}
  <script>
    document.addEventListener('DOMContentLoaded', function() {
      const videoPlayer = document.getElementById('videoPlayer');
      const videoCover = document.getElementById('videoCover');
      const videoUrl = videoPlayer.getAttribute('data-url');
      const autoplay = videoPlayer.getAttribute('data-autoplay') === 'true';
      const loop = videoPlayer.getAttribute('data-loop') === 'true';
      
      if (!videoUrl) return;
      
      let videoType, videoId;
      
      if (videoUrl.includes('youtube.com') || videoUrl.includes('youtu.be')) {
        videoType = 'youtube';
        
        if (videoUrl.includes('youtu.be/')) {
          videoId = videoUrl.split('youtu.be/')[1];
        } else if (videoUrl.includes('youtube.com/watch?v=')) {
          videoId = videoUrl.split('v=')[1];
        } else if (videoUrl.includes('youtube.com/embed/')) {
          videoId = videoUrl.split('embed/')[1];
        }
        
        if (videoId && videoId.includes('&')) {
          videoId = videoId.split('&')[0];
        }
        
      } else if (videoUrl.includes('vimeo.com')) {
        videoType = 'vimeo';
        
        if (videoUrl.includes('vimeo.com/')) {
          videoId = videoUrl.split('vimeo.com/')[1];
        }
        
        if (videoId && videoId.includes('?')) {
          videoId = videoId.split('?')[0];
        }
      }
      
      if (!videoId) return;
      
      let iframe = document.createElement('iframe');
      iframe.setAttribute('frameborder', '0');
      iframe.setAttribute('allowfullscreen', '');
      iframe.setAttribute('allow', 'accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture');
      
      if (videoType === 'youtube') {
        iframe.src = `https://www.youtube.com/embed/${videoId}?rel=0&showinfo=0&modestbranding=1${autoplay ? '&autoplay=1&mute=1' : ''}${loop ? '&loop=1&playlist=' + videoId : ''}`;
      } else if (videoType === 'vimeo') {
        iframe.src = `https://player.vimeo.com/video/${videoId}?title=0&byline=0&portrait=0${autoplay ? '&autoplay=1&muted=1' : ''}${loop ? '&loop=1' : ''}`;
      }
      
      if (autoplay && videoCover) {
        videoCover.classList.add('hidden');
      }
      
      videoPlayer.appendChild(iframe);
      
      if (videoCover) {
        videoCover.addEventListener('click', function() {
          videoCover.classList.add('hidden');
          
          if (videoType === 'youtube') {
            iframe.src = `https://www.youtube.com/embed/${videoId}?rel=0&showinfo=0&modestbranding=1&autoplay=1${loop ? '&loop=1&playlist=' + videoId : ''}`;
          } else if (videoType === 'vimeo') {
            iframe.src = `https://player.vimeo.com/video/${videoId}?title=0&byline=0&portrait=0&autoplay=1${loop ? '&loop=1' : ''}`;
          }
        });
      }
    });
  </script>
{% endif %}

{% if section.settings.enable_animations %}
  <script>
    document.addEventListener('DOMContentLoaded', function() {
      let observer = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
          if (entry.isIntersecting) {
            entry.target.classList.add('active', 'fade-in');
            observer.unobserve(entry.target);
          }
        });
      }, {
        root: null, 
        threshold: 0.1, 
        rootMargin: '0px 0px -50px 0px' 
      });
      
      const animElements = document.querySelectorAll('.animate');
      animElements.forEach(el => {
        observer.observe(el);
      });
    });
  </script>
{% endif %}

{% schema %}
  {
    "name": "Our Market Presence",
    "class": "market-presence-section",
    "settings": [
      {
        "type": "text",
        "id": "title",
        "label": "Section Title",
        "default": "Our Market Presence"
      },
      {
        "type": "color",
        "id": "title_color",
        "label": "Title Color",
        "default": "#d1b073"
      },
      {
        "type": "select",
        "id": "layout",
        "label": "Section Layout",
        "options": [
          {
            "value": "image_left",
            "label": "Media Left"
          },
          {
            "value": "image_right",
            "label": "Media Right"
          }
        ],
        "default": "image_left"
      },
      {
        "type": "select",
        "id": "media_type",
        "label": "Media Type",
        "options": [
          {
            "value": "single_image",
            "label": "Single Image"
          },
          {
            "value": "stacked_images",
            "label": "Stacked Images"
          },
          {
            "value": "video",
            "label": "Video (YouTube/Vimeo)"
          },
          {
            "value": "self_hosted_video",
            "label": "Self-hosted Video"
          },
          {
            "value": "image_collage",
            "label": "Image Collage"
          }
        ],
        "default": "single_image"
      },
      {
        "type": "checkbox",
        "id": "equal_height",
        "label": "Equal height for media and text",
        "default": false,
        "info": "Makes media container same height as text content"
      },
      {
        "type": "header",
        "content": "Media Content"
      },
      {
        "type": "image_picker",
        "id": "main_image",
        "label": "Main Image",
        "info": "Used for Single Image and as primary image in stacked and collage layouts"
      },
      {
        "type": "image_picker",
        "id": "secondary_image",
        "label": "Secondary Image",
        "info": "Used in stacked images layout and collage"
      },
      {
        "type": "image_picker",
        "id": "tertiary_image",
        "label": "Tertiary Image",
        "info": "Used in stacked images layout and collage"
      },
      {
        "type": "url",
        "id": "video_url",
        "label": "Video URL",
        "info": "YouTube or Vimeo URL (for 'Video' media type)"
      },
      {
        "type": "video",
        "id": "self_hosted_video",
        "label": "Self-hosted Video",
        "info": "Upload a video file directly (for 'Self-hosted Video' media type)"
      },
      {
        "type": "select",
        "id": "self_hosted_video_color",
        "label": "Video Controls Color",
        "options": [
          {
            "value": "dark",
            "label": "Dark"
          },
          {
            "value": "light",
            "label": "Light"
          }
        ],
        "default": "dark"
      },
      {
        "type": "image_picker",
        "id": "video_cover_image",
        "label": "Video Cover Image",
        "info": "Displayed before video plays"
      },
      {
        "type": "checkbox",
        "id": "autoplay",
        "label": "Autoplay video",
        "default": false,
        "info": "Video will autoplay with muted audio"
      },
      {
        "type": "checkbox",
        "id": "loop",
        "label": "Loop video",
        "default": false
      },
      {
        "type": "select",
        "id": "image_ratio",
        "label": "Image Ratio",
        "options": [
          {
            "value": "adapt",
            "label": "Adapt to image"
          },
          {
            "value": "portrait",
            "label": "Portrait"
          },
          {
            "value": "square",
            "label": "Square"
          },
          {
            "value": "landscape",
            "label": "Landscape"
          }
        ],
        "default": "landscape"
      },
      {
        "type": "select",
        "id": "image_style",
        "label": "Image Style",
        "options": [
          {
            "value": "default",
            "label": "Default"
          },
          {
            "value": "shadow",
            "label": "Shadow"
          },
          {
            "value": "border",
            "label": "Border"
          },
          {
            "value": "rounded",
            "label": "Rounded"
          },
          {
            "value": "floating",
            "label": "Floating"
          }
        ],
        "default": "shadow"
      },
      {
        "type": "header",
        "content": "Content Settings"
      },
      {
        "type": "richtext",
        "id": "text",
        "label": "Main Text",
        "default": "<p>Over the past years, we have taken our niche in all market segments: from professional author's interiors to more budgetary tender facilities and democratic repairs. All materials are made from the best, environmentally friendly premium-class raw materials.</p>"
      },
      {
        "type": "color",
        "id": "background_color",
        "label": "Background Color",
        "default": "#ffffff"
      },
      {
        "type": "range",
        "id": "padding_top",
        "min": 0,
        "max": 100,
        "step": 10,
        "unit": "px",
        "label": "Padding Top",
        "default": 60
      },
      {
        "type": "range",
        "id": "padding_bottom",
        "min": 0,
        "max": 100,
        "step": 10,
        "unit": "px",
        "label": "Padding Bottom",
        "default": 60
      },
      {
        "type": "header",
        "content": "Animation Settings"
      },
      {
        "type": "checkbox",
        "id": "enable_animations",
        "label": "Enable animations",
        "default": true
      },
      {
        "type": "select",
        "id": "animation_style",
        "label": "Animation Style",
        "options": [
          {
            "value": "fade",
            "label": "Fade In"
          },
          {
            "value": "slide_up",
            "label": "Slide Up"
          },
          {
            "value": "slide_right",
            "label": "Slide Right"
          },
          {
            "value": "slide_left",
            "label": "Slide Left"
          }
        ],
        "default": "fade"
      }
    ],
    "blocks": [
      {
        "type": "market_segment",
        "name": "Market Segment",
        "settings": [
          {
            "type": "text",
            "id": "title",
            "label": "Segment Title",
            "default": "Professional Interiors"
          },
          {
            "type": "richtext",
            "id": "content",
            "label": "Segment Description",
            "default": "<p>Specialized solutions for professional designers and architects creating high-end author's interiors.</p>"
          },
          {
            "type": "select",
            "id": "icon",
            "label": "Icon",
            "options": [
              {
                "value": "icon-building",
                "label": "Building"
              },
              {
                "value": "icon-home",
                "label": "Home"
              },
              {
                "value": "icon-paint",
                "label": "Paint"
              },
              {
                "value": "icon-brush",
                "label": "Brush"
              },
              {
                "value": "icon-tool",
                "label": "Tool"
              },
              {
                "value": "icon-settings",
                "label": "Settings"
              }
            ],
            "default": "icon-building"
          }
        ]
      }
    ],
    "presets": [
      {
        "name": "Our Market Presence",
        "blocks": [
          {
            "type": "market_segment"
          },
          {
            "type": "market_segment"
          },
          {
            "type": "market_segment"
          }
        ]
      }
    ]
  }
{% endschema %}