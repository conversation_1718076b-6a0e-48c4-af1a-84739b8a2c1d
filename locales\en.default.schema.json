{"settings_schema": {"global": {"settings": {"header__border": {"content": "Border"}, "header__shadow": {"content": "Shadow"}, "blur": {"label": "Blur"}, "corner_radius": {"label": "Corner radius"}, "horizontal_offset": {"label": "Horizontal offset"}, "vertical_offset": {"label": "Vertical offset"}, "thickness": {"label": "<PERSON><PERSON><PERSON><PERSON>"}, "opacity": {"label": "Opacity"}, "image_padding": {"label": "Image padding"}, "text_alignment": {"options__1": {"label": "Left"}, "options__2": {"label": "Center"}, "options__3": {"label": "Right"}, "label": "Text alignment"}}}, "cards": {"name": "Product cards", "settings": {"style": {"options__1": {"label": "Standard"}, "options__2": {"label": "Card"}, "label": "Style"}}}, "collection_cards": {"name": "Collection cards", "settings": {"style": {"options__1": {"label": "Standard"}, "options__2": {"label": "Card"}, "label": "Style"}}}, "blog_cards": {"name": "Blog cards", "settings": {"style": {"options__1": {"label": "Standard"}, "options__2": {"label": "Card"}, "label": "Style"}}}, "badges": {"name": "Badges", "settings": {"position": {"options__1": {"label": "Bottom left"}, "options__2": {"label": "Bottom right"}, "options__3": {"label": "Top left"}, "options__4": {"label": "Top right"}, "label": "Position on cards"}, "sale_badge_color_scheme": {"label": "Sale badge color scheme"}, "sold_out_badge_color_scheme": {"label": "Sold out badge color scheme"}}}, "colors": {"name": "Colors", "settings": {"background": {"label": "Background"}, "background_gradient": {"label": "Background gradient", "info": "Background gradient replaces background where possible."}, "text": {"label": "Text"}, "button_background": {"label": "Solid button background"}, "button_label": {"label": "Solid button label"}, "secondary_button_label": {"label": "Outline button"}, "shadow": {"label": "Shadow"}}}, "logo": {"name": "Logo", "settings": {"logo_image": {"label": "Logo"}, "logo_width": {"label": "Desktop logo width", "info": "Logo width is automatically optimized for mobile."}, "favicon": {"label": "Favicon image", "info": "Will be scaled down to 32 x 32px"}}}, "brand_information": {"name": "Brand information", "settings": {"paragraph": {"content": "Add a brand description to your store's footer."}, "brand_headline": {"label": "Headline"}, "brand_description": {"label": "Description"}, "brand_image": {"label": "Image"}, "brand_image_width": {"label": "Image width"}}}, "typography": {"name": "Typography", "settings": {"type_header_font": {"label": "Font", "info": "Selecting a different font can affect the speed of your store. [Learn more about system fonts.](https://help.shopify.com/manual/online-store/os/store-speed/improving-speed#fonts)"}, "heading_scale": {"label": "Font size scale"}, "header__1": {"content": "Headings"}, "header__2": {"content": "Body"}, "type_body_font": {"label": "Font", "info": "Selecting a different font can affect the speed of your store. [Learn more about system fonts.](https://help.shopify.com/manual/online-store/os/store-speed/improving-speed#fonts)"}, "body_scale": {"label": "Font size scale"}}}, "buttons": {"name": "Buttons"}, "variant_pills": {"name": "Variant pills", "paragraph": "Variant pills are one way of displaying your product variants. [Learn more](https://help.shopify.com/en/manual/online-store/themes/theme-structure/page-types#variant-picker-block)"}, "inputs": {"name": "Inputs"}, "content_containers": {"name": "Content containers"}, "popups": {"name": "Dropdowns and pop-ups", "paragraph": "Affects areas like navigation dropdowns, pop-up modals, and cart pop-ups."}, "media": {"name": "Media"}, "drawers": {"name": "Drawers"}, "animations": {"name": "Animations", "settings": {"animations_reveal_on_scroll": {"label": "Reveal sections on scroll"}, "animations_hover_elements": {"options__1": {"label": "None"}, "options__2": {"label": "Vertical lift"}, "options__3": {"label": "3D lift"}, "label": "Hover effect", "info": "Affects cards and buttons."}}}, "social-media": {"name": "Social media", "settings": {"header": {"content": "Social accounts"}, "social_twitter_link": {"label": "Twitter", "info": "https://twitter.com/shopify"}, "social_facebook_link": {"label": "Facebook", "info": "https://facebook.com/shopify"}, "social_pinterest_link": {"label": "Pinterest", "info": "https://pinterest.com/shopify"}, "social_instagram_link": {"label": "Instagram", "info": "http://instagram.com/shopify"}, "social_tiktok_link": {"label": "TikTok", "info": "https://tiktok.com/@shopify"}, "social_tumblr_link": {"label": "Tumblr", "info": "https://shopify.tumblr.com"}, "social_snapchat_link": {"label": "Snapchat", "info": "https://www.snapchat.com/add/shopify"}, "social_youtube_link": {"label": "YouTube", "info": "https://www.youtube.com/shopify"}, "social_vimeo_link": {"label": "Vimeo", "info": "https://vimeo.com/shopify"}}}, "search_input": {"name": "Search behavior", "settings": {"header": {"content": "Search suggestions"}, "predictive_search_enabled": {"label": "Enable search suggestions"}, "predictive_search_show_vendor": {"label": "Show product vendor", "info": "Visible when search suggestions enabled."}, "predictive_search_show_price": {"label": "Show product price", "info": "Visible when search suggestions enabled."}}}, "currency_format": {"name": "Currency format", "settings": {"content": "Currency codes", "paragraph": "Cart and checkout prices always show currency codes. Example: $1.00 USD.", "currency_code_enabled": {"label": "Show currency codes"}}}, "cart": {"name": "<PERSON><PERSON>", "settings": {"cart_type": {"label": "Cart type", "drawer": {"label": "Drawer"}, "page": {"label": "Page"}, "notification": {"label": "Popup notification"}}, "show_vendor": {"label": "Show vendor"}, "show_cart_note": {"label": "Enable cart note"}, "cart_drawer": {"header": "Cart drawer", "collection": {"label": "Collection", "info": "Visible when cart drawer is empty."}}}}, "layout": {"name": "Layout", "settings": {"page_width": {"label": "Page width"}, "spacing_sections": {"label": "Space between template sections"}, "header__grid": {"content": "Grid"}, "paragraph__grid": {"content": "Affects areas with multiple columns or rows."}, "spacing_grid_horizontal": {"label": "Horizontal space"}, "spacing_grid_vertical": {"label": "Vertical space"}}}}, "sections": {"all": {"animation": {"content": "Animations", "image_behavior": {"options__1": {"label": "None"}, "options__2": {"label": "Ambient movement"}, "options__3": {"label": "Fixed background position"}, "options__4": {"label": "Zoom in on scroll"}, "label": "Image behavior"}}, "padding": {"section_padding_heading": "Section padding", "padding_top": "Top padding", "padding_bottom": "Bottom padding"}, "spacing": "Spacing", "colors": {"label": "Color scheme", "has_cards_info": "To change the card color scheme, update your theme settings."}, "heading_size": {"label": "Heading size", "options__1": {"label": "Small"}, "options__2": {"label": "Medium"}, "options__3": {"label": "Large"}, "options__4": {"label": "Extra large"}}, "image_shape": {"options__1": {"label": "<PERSON><PERSON><PERSON>"}, "options__2": {"label": "Arch"}, "options__3": {"label": "Blob"}, "options__4": {"label": "<PERSON><PERSON><PERSON> left"}, "options__5": {"label": "Chevron right"}, "options__6": {"label": "Diamond"}, "options__7": {"label": "Parallelogram"}, "options__8": {"label": "Round"}, "label": "Image shape", "info": "Standard-styled cards have no borders when an image shape is active."}}, "announcement-bar": {"name": "Announcement bar", "settings": {"auto_rotate": {"label": "Auto-rotate announcements"}, "change_slides_speed": {"label": "Change every"}, "header__1": {"content": "Social media icons", "info": "To display your social media accounts, link them in your [theme settings](/editor?context=theme&category=social%20media)."}, "header__2": {"content": "Announcements"}, "show_social": {"label": "Show icons on desktop"}, "header__3": {"content": "Country/region selector", "info": "To add a country/region, go to your [market settings.](/admin/settings/markets)"}, "enable_country_selector": {"label": "Enable country/region selector"}, "header__4": {"content": "Language selector", "info": "To add a language, go to your [language settings.](/admin/settings/languages)"}, "enable_language_selector": {"label": "Enable language selector"}}, "blocks": {"announcement": {"name": "Announcement", "settings": {"text": {"label": "Text"}, "text_alignment": {"label": "Text alignment", "options__1": {"label": "Left"}, "options__2": {"label": "Center"}, "options__3": {"label": "Right"}}, "link": {"label": "Link"}}}}, "presets": {"name": "Announcement bar"}}, "apps": {"name": "Apps", "settings": {"include_margins": {"label": "Make section margins the same as theme"}}, "presets": {"name": "Apps"}}, "collage": {"name": "Collage", "settings": {"heading": {"label": "Heading"}, "desktop_layout": {"label": "Desktop layout", "options__1": {"label": "Left large block"}, "options__2": {"label": "Right large block"}}, "mobile_layout": {"label": "Mobile layout", "options__1": {"label": "Collage"}, "options__2": {"label": "Column"}}, "card_styles": {"label": "Card style", "info": "Product, collection, and blog card styles can be updated in theme settings.", "options__1": {"label": "Use individual card styles"}, "options__2": {"label": "Style all as product cards"}}}, "blocks": {"image": {"name": "Image", "settings": {"image": {"label": "Image"}}}, "product": {"name": "Product", "settings": {"product": {"label": "Product"}, "secondary_background": {"label": "Show secondary background"}, "second_image": {"label": "Show second image on hover"}}}, "collection": {"name": "Collection", "settings": {"collection": {"label": "Collection"}}}, "video": {"name": "Video", "settings": {"cover_image": {"label": "Cover image"}, "video_url": {"label": "URL", "info": "Video plays in a pop-up if the section contains other blocks.", "placeholder": "Use a YouTube or Vimeo URL"}, "description": {"label": "Video alt text", "info": "Describe the video for customers using screen readers. [Learn more](https://help.shopify.com/manual/online-store/themes/theme-structure/theme-features#video-block)"}}}}, "presets": {"name": "Collage"}}, "collection-list": {"name": "Collection list", "settings": {"title": {"label": "Heading"}, "image_ratio": {"label": "Image ratio", "options__1": {"label": "Adapt to image"}, "options__2": {"label": "Portrait"}, "options__3": {"label": "Square"}, "info": "Add images by editing your collections. [Learn more](https://help.shopify.com/manual/products/collections)"}, "columns_desktop": {"label": "Number of columns on desktop"}, "show_view_all": {"label": "Enable \"View all\" button if list includes more collections than shown"}, "header_mobile": {"content": "Mobile Layout"}, "columns_mobile": {"label": "Number of columns on mobile", "options__1": {"label": "1 column"}, "options__2": {"label": "2 columns"}}, "swipe_on_mobile": {"label": "Enable swipe on mobile"}}, "blocks": {"featured_collection": {"name": "Collection", "settings": {"collection": {"label": "Collection"}}}}, "presets": {"name": "Collection list"}}, "contact-form": {"name": "Contact Form", "presets": {"name": "Contact form"}}, "custom-liquid": {"name": "Custom Liquid", "settings": {"custom_liquid": {"label": "Liquid code", "info": "Add app snippets or other code to create advanced customizations. [Learn more](https://shopify.dev/docs/api/liquid)"}}, "presets": {"name": "Custom Liquid"}}, "featured-blog": {"name": "Blog posts", "settings": {"heading": {"label": "Heading"}, "blog": {"label": "Blog"}, "post_limit": {"label": "Number of blog posts to show"}, "columns_desktop": {"label": "Number of columns on desktop"}, "show_view_all": {"label": "Enable \"View all\" button if blog includes more blog posts than shown"}, "show_image": {"label": "Show featured image", "info": "For best results, use an image with a 3:2 aspect ratio. [Learn more](https://help.shopify.com/manual/shopify-admin/productivity-tools/image-editor#understanding-image-aspect-ratio)"}, "show_date": {"label": "Show date"}, "show_author": {"label": "Show author"}}, "presets": {"name": "Blog posts"}}, "featured-collection": {"name": "Featured collection", "settings": {"title": {"label": "Heading"}, "description": {"label": "Description"}, "show_description": {"label": "Show collection description from the admin"}, "description_style": {"label": "Description style", "options__1": {"label": "Body"}, "options__2": {"label": "Subtitle"}, "options__3": {"label": "Uppercase"}}, "collection": {"label": "Collection"}, "products_to_show": {"label": "Maximum products to show"}, "columns_desktop": {"label": "Number of columns on desktop"}, "show_view_all": {"label": "Enable \"View all\" if collection has more products than shown"}, "view_all_style": {"label": "\"View all\" style", "options__1": {"label": "Link"}, "options__2": {"label": "Outline button"}, "options__3": {"label": "Solid button"}}, "enable_desktop_slider": {"label": "Enable carousel on desktop"}, "full_width": {"label": "Make products full width"}, "header": {"content": "Product card"}, "image_ratio": {"label": "Image ratio", "options__1": {"label": "Adapt to image"}, "options__2": {"label": "Portrait"}, "options__3": {"label": "Square"}}, "show_secondary_image": {"label": "Show second image on hover"}, "show_vendor": {"label": "Show vendor"}, "show_rating": {"label": "Show product rating", "info": "To display a rating, add a product rating app. [Learn more](https://help.shopify.com/manual/online-store/themes/theme-structure/theme-features#featured-collection-show-product-rating)"}, "enable_quick_buy": {"label": "Enable quick add button", "info": "Optimal with popup or drawer cart type."}, "header_mobile": {"content": "Mobile Layout"}, "columns_mobile": {"label": "Number of columns on mobile", "options__1": {"label": "1 column"}, "options__2": {"label": "2 columns"}}, "swipe_on_mobile": {"label": "Enable swipe on mobile"}}, "presets": {"name": "Featured collection"}}, "featured-product": {"name": "Featured product", "blocks": {"text": {"name": "Text", "settings": {"text": {"label": "Text"}, "text_style": {"label": "Text style", "options__1": {"label": "Body"}, "options__2": {"label": "Subtitle"}, "options__3": {"label": "Uppercase"}}}}, "title": {"name": "Title"}, "price": {"name": "Price"}, "quantity_selector": {"name": "Quantity selector"}, "variant_picker": {"name": "Variant picker", "settings": {"picker_type": {"label": "Style", "options__1": {"label": "Dropdown"}, "options__2": {"label": "Pills"}}, "swatch_shape": {"label": "Swatch", "info": "Enable [swatches](https://help.shopify.com/en/manual/online-store/themes/theme-structure/theme-settings#options-with-swatches) on product options.", "options__1": {"label": "Circle"}, "options__2": {"label": "Square"}, "options__3": {"label": "None"}}}}, "buy_buttons": {"name": "Buy buttons", "settings": {"show_dynamic_checkout": {"label": "Show dynamic checkout buttons", "info": "Using the payment methods available on your store, customers see their preferred option, like PayPal or Apple Pay. [Learn more](https://help.shopify.com/manual/using-themes/change-the-layout/dynamic-checkout)"}}}, "sku": {"name": "SKU", "settings": {"text_style": {"label": "Text style", "options__1": {"label": "Body"}, "options__2": {"label": "Subtitle"}, "options__3": {"label": "Uppercase"}}}}, "description": {"name": "Description"}, "share": {"name": "Share", "settings": {"text": {"label": "Text"}, "featured_image_info": {"content": "If you include a link in social media posts, the page’s featured image will be shown as the preview image. [Learn more](https://help.shopify.com/manual/online-store/images/showing-social-media-thumbnail-images)"}, "title_info": {"content": "A store title and description are included with the preview image. [Learn more](https://help.shopify.com/manual/promoting-marketing/seo/adding-keywords#set-a-title-and-description-for-your-online-store)"}}}, "rating": {"name": "Product rating", "settings": {"paragraph": {"content": "To display a rating, add a product rating app. [Learn more](https://help.shopify.com/manual/online-store/themes/theme-structure/theme-features#featured-product-rating)"}}}}, "settings": {"product": {"label": "Product"}, "secondary_background": {"label": "Show secondary background"}, "header": {"content": "Media", "info": "Learn more about [media types](https://help.shopify.com/manual/products/product-media)"}, "media_position": {"label": "Desktop media position", "info": "Position is automatically optimized for mobile.", "options__1": {"label": "Left"}, "options__2": {"label": "Right"}}, "hide_variants": {"label": "Hide unselected variants’ media on desktop"}, "enable_video_looping": {"label": "Enable video looping"}}, "presets": {"name": "Featured product"}}, "footer": {"name": "Footer", "blocks": {"link_list": {"name": "<PERSON><PERSON>", "settings": {"heading": {"label": "Heading"}, "menu": {"label": "<PERSON><PERSON>", "info": "Displays only top-level menu items."}}}, "brand_information": {"name": "Brand information", "settings": {"paragraph": {"content": "This block will display your brand information. [Edit brand information.](/editor?context=theme&category=brand%20information)"}, "header__1": {"content": "Social media icons"}, "show_social": {"label": "Show social media icons", "info": "To display your social media accounts, link them in your [theme settings](/editor?context=theme&category=social%20media)."}}}, "text": {"name": "Text", "settings": {"heading": {"label": "Heading"}, "subtext": {"label": "Subtext"}}}}, "settings": {"newsletter_enable": {"label": "Show email signup"}, "newsletter_heading": {"label": "Heading"}, "header__1": {"content": "<PERSON>ail Signup", "info": "Subscribers added automatically to your “accepted marketing” customer list. [Learn more](https://help.shopify.com/manual/customers/manage-customers)"}, "header__2": {"content": "Social media icons", "info": "To display your social media accounts, link them in your [theme settings](/editor?context=theme&category=social%20media)."}, "show_social": {"label": "Show social media icons"}, "header__3": {"content": "Country/region selector"}, "header__4": {"info": "To add a country/region, go to your [market settings.](/admin/settings/markets)"}, "enable_country_selector": {"label": "Enable country/region selector"}, "header__5": {"content": "Language selector"}, "header__6": {"info": "To add a language, go to your [language settings.](/admin/settings/languages)"}, "enable_language_selector": {"label": "Enable language selector"}, "header__7": {"content": "Payment methods"}, "payment_enable": {"label": "Show payment icons"}, "header__8": {"content": "Policy links", "info": "To add store policies, go to your [policy settings](/admin/settings/legal)."}, "show_policy": {"label": "Show policy links"}, "margin_top": {"label": "Top margin"}, "header__9": {"content": "Follow on Shop", "info": "To allow customers to follow your store on the Shop app from your storefront, Shop Pay must be enabled. [Learn more](https://help.shopify.com/manual/online-store/themes/customizing-themes/follow-on-shop)"}, "enable_follow_on_shop": {"label": "Enable Follow on Shop"}}}, "header": {"name": "Header", "settings": {"logo_help": {"content": "Edit your logo in [theme settings](/editor?context=theme&category=logo)."}, "logo_position": {"label": "Desktop logo position", "options__1": {"label": "Middle left"}, "options__2": {"label": "Top left"}, "options__3": {"label": "Top center"}, "options__4": {"label": "Middle center"}}, "menu": {"label": "<PERSON><PERSON>"}, "menu_type_desktop": {"label": "Desktop menu type", "info": "Menu type is automatically optimized for mobile.", "options__1": {"label": "Dropdown"}, "options__2": {"label": "Mega menu"}, "options__3": {"label": "Drawer"}}, "show_line_separator": {"label": "Show separator line"}, "header__1": {"content": "Color"}, "menu_color_scheme": {"label": "Menu color scheme"}, "sticky_header_type": {"label": "Sticky header", "options__1": {"label": "None"}, "options__2": {"label": "On scroll up"}, "options__3": {"label": "Always"}, "options__4": {"label": "Always, reduce logo size"}}, "header__3": {"content": "Country/region selector"}, "header__4": {"info": "To add a country/region, go to your [market settings.](/admin/settings/markets)"}, "enable_country_selector": {"label": "Enable country/region selector"}, "header__5": {"content": "Language selector"}, "header__6": {"info": "To add a language, go to your [language settings.](/admin/settings/languages)"}, "enable_language_selector": {"label": "Enable language selector"}, "margin_bottom": {"label": "Bottom margin"}, "mobile_layout": {"content": "Mobile layout"}, "mobile_logo_position": {"label": "Mobile logo position", "options__1": {"label": "Center"}, "options__2": {"label": "Left"}}}}, "image-banner": {"name": "Image banner", "settings": {"image": {"label": "First image"}, "image_2": {"label": "Second image"}, "image_overlay_opacity": {"label": "Image overlay opacity"}, "image_height": {"label": "Banner height", "options__1": {"label": "Adapt to first image"}, "options__2": {"label": "Small"}, "options__3": {"label": "Medium"}, "options__4": {"label": "Large"}, "info": "For best results, use an image with a 3:2 aspect ratio. [Learn more](https://help.shopify.com/manual/shopify-admin/productivity-tools/image-editor#understanding-image-aspect-ratio)"}, "desktop_content_position": {"options__1": {"label": "Top Left"}, "options__2": {"label": "Top Center"}, "options__3": {"label": "Top Right"}, "options__4": {"label": "Middle Left"}, "options__5": {"label": "Middle Center"}, "options__6": {"label": "Middle Right"}, "options__7": {"label": "Bottom Left"}, "options__8": {"label": "Bottom Center"}, "options__9": {"label": "Bottom Right"}, "label": "Desktop content position"}, "show_text_box": {"label": "Show container on desktop"}, "desktop_content_alignment": {"options__1": {"label": "Left"}, "options__2": {"label": "Center"}, "options__3": {"label": "Right"}, "label": "Desktop content alignment"}, "mobile": {"content": "Mobile Layout"}, "mobile_content_alignment": {"options__1": {"label": "Left"}, "options__2": {"label": "Center"}, "options__3": {"label": "Right"}, "label": "Mobile content alignment"}, "stack_images_on_mobile": {"label": "Stack images on mobile"}, "show_text_below": {"label": "Show container on mobile"}}, "blocks": {"heading": {"name": "Heading", "settings": {"heading": {"label": "Heading"}}}, "text": {"name": "Text", "settings": {"text": {"label": "Description"}, "text_style": {"options__1": {"label": "Body"}, "options__2": {"label": "Subtitle"}, "options__3": {"label": "Uppercase"}, "label": "Text style"}}}, "buttons": {"name": "Buttons", "settings": {"button_label_1": {"label": "First button label", "info": "Leave the label blank to hide the button."}, "button_link_1": {"label": "First button link"}, "button_style_secondary_1": {"label": "Use outline button style"}, "button_label_2": {"label": "Second button label", "info": "Leave the label blank to hide the button."}, "button_link_2": {"label": "Second button link"}, "button_style_secondary_2": {"label": "Use outline button style"}}}}, "presets": {"name": "Image banner"}}, "image-with-text": {"name": "Image with text", "settings": {"image": {"label": "Image"}, "height": {"options__1": {"label": "Adapt to image"}, "options__2": {"label": "Small"}, "options__3": {"label": "Medium"}, "options__4": {"label": "Large"}, "label": "Image height"}, "desktop_image_width": {"options__1": {"label": "Small"}, "options__2": {"label": "Medium"}, "options__3": {"label": "Large"}, "label": "Desktop image width", "info": "Image is automatically optimized for mobile."}, "layout": {"options__1": {"label": "Image first"}, "options__2": {"label": "Image second"}, "label": "Desktop image placement", "info": "Image first is the default mobile layout."}, "desktop_content_alignment": {"options__1": {"label": "Left"}, "options__2": {"label": "Center"}, "options__3": {"label": "Right"}, "label": "Desktop content alignment"}, "desktop_content_position": {"options__1": {"label": "Top"}, "options__2": {"label": "Middle"}, "options__3": {"label": "Bottom"}, "label": "Desktop content position"}, "content_layout": {"options__1": {"label": "No overlap"}, "options__2": {"label": "Overlap"}, "label": "Content layout"}, "mobile_content_alignment": {"options__1": {"label": "Left"}, "options__2": {"label": "Center"}, "options__3": {"label": "Right"}, "label": "Mobile content alignment"}}, "blocks": {"heading": {"name": "Heading", "settings": {"heading": {"label": "Heading"}}}, "caption": {"name": "Caption", "settings": {"text": {"label": "Text"}, "text_style": {"label": "Text style", "options__1": {"label": "Subtitle"}, "options__2": {"label": "Uppercase"}}, "caption_size": {"label": "Text size", "options__1": {"label": "Small"}, "options__2": {"label": "Medium"}, "options__3": {"label": "Large"}}}}, "text": {"name": "Text", "settings": {"text": {"label": "Content"}, "text_style": {"label": "Text style", "options__1": {"label": "Body"}, "options__2": {"label": "Subtitle"}}}}, "button": {"name": "<PERSON><PERSON>", "settings": {"button_label": {"label": "Button label", "info": "Leave the label blank to hide the button."}, "button_link": {"label": "Button link"}, "outline_button": {"label": "Use outline button style"}}}}, "presets": {"name": "Image with text"}}, "multirow": {"name": "<PERSON><PERSON>", "settings": {"image": {"label": "Image"}, "image_height": {"options__1": {"label": "Adapt to image"}, "options__2": {"label": "Small"}, "options__3": {"label": "Medium"}, "options__4": {"label": "Large"}, "label": "Image height"}, "desktop_image_width": {"options__1": {"label": "Small"}, "options__2": {"label": "Medium"}, "options__3": {"label": "Large"}, "label": "Desktop image width", "info": "Image is automatically optimized for mobile."}, "heading_size": {"options__1": {"label": "Small"}, "options__2": {"label": "Medium"}, "options__3": {"label": "Large"}, "label": "Heading size"}, "text_style": {"options__1": {"label": "Body"}, "options__2": {"label": "Subtitle"}, "label": "Text style"}, "button_style": {"options__1": {"label": "Solid button"}, "options__2": {"label": "Outline button"}, "label": "Button style"}, "desktop_content_alignment": {"options__1": {"label": "Left"}, "options__2": {"label": "Center"}, "options__3": {"label": "Right"}, "label": "Desktop content alignment"}, "desktop_content_position": {"options__1": {"label": "Top"}, "options__2": {"label": "Middle"}, "options__3": {"label": "Bottom"}, "label": "Desktop content position", "info": "Position is automatically optimized for mobile."}, "image_layout": {"options__1": {"label": "Alternate from left"}, "options__2": {"label": "Alternate from right"}, "options__3": {"label": "Aligned left"}, "options__4": {"label": "Aligned right"}, "label": "Desktop image placement", "info": "Placement is automatically optimized for mobile."}, "container_color_scheme": {"label": "Container color scheme"}, "mobile_content_alignment": {"options__1": {"label": "Left"}, "options__2": {"label": "Center"}, "options__3": {"label": "Right"}, "label": "Mobile content alignment"}, "header_mobile": {"content": "Mobile Layout"}}, "blocks": {"row": {"name": "Row", "settings": {"image": {"label": "Image"}, "caption": {"label": "Caption"}, "heading": {"label": "Heading"}, "text": {"label": "Text"}, "button_label": {"label": "Button label"}, "button_link": {"label": "Button link"}}}}, "presets": {"name": "<PERSON><PERSON>"}}, "main-account": {"name": "Account"}, "main-activate-account": {"name": "Account activation"}, "main-addresses": {"name": "Addresses"}, "main-article": {"name": "Blog post", "blocks": {"featured_image": {"name": "Featured image", "settings": {"image_height": {"label": "Featured image height", "options__1": {"label": "Adapt to image"}, "options__2": {"label": "Small"}, "options__3": {"label": "Medium"}, "options__4": {"label": "Large"}, "info": "For best results, use an image with a 16:9 aspect ratio. [Learn more](https://help.shopify.com/manual/shopify-admin/productivity-tools/image-editor#understanding-image-aspect-ratio)"}}}, "title": {"name": "Title", "settings": {"blog_show_date": {"label": "Show date"}, "blog_show_author": {"label": "Show author"}}}, "content": {"name": "Content"}, "share": {"name": "Share", "settings": {"text": {"label": "Text"}, "featured_image_info": {"content": "If you include a link in social media posts, the page’s featured image will be shown as the preview image. [Learn more](https://help.shopify.com/manual/online-store/images/showing-social-media-thumbnail-images)."}, "title_info": {"content": "A store title and description are included with the preview image. [Learn more](https://help.shopify.com/manual/promoting-marketing/seo/adding-keywords#set-a-title-and-description-for-your-online-store)."}}}}}, "main-blog": {"name": "Blog posts", "settings": {"header": {"content": "Blog post card"}, "show_image": {"label": "Show featured image"}, "show_date": {"label": "Show date"}, "show_author": {"label": "Show author"}, "paragraph": {"content": "Change excerpts by editing your blog posts. [Learn more](https://help.shopify.com/manual/online-store/blogs/writing-blogs#display-an-excerpt-from-a-blog-post)"}, "layout": {"label": "Desktop layout", "options__1": {"label": "Grid"}, "options__2": {"label": "Collage"}, "info": "Posts are stacked on mobile."}, "image_height": {"label": "Featured image height", "options__1": {"label": "Adapt to image"}, "options__2": {"label": "Small"}, "options__3": {"label": "Medium"}, "options__4": {"label": "Large"}, "info": "For best results, use an image with a 3:2 aspect ratio. [Learn more](https://help.shopify.com/manual/shopify-admin/productivity-tools/image-editor#understanding-image-aspect-ratio)"}}}, "main-cart-footer": {"name": "Subtotal", "blocks": {"subtotal": {"name": "Subtotal price"}, "buttons": {"name": "Checkout button"}}}, "main-cart-items": {"name": "Items"}, "main-collection-banner": {"name": "Collection banner", "settings": {"paragraph": {"content": "Add a description or image by editing your collection. [Learn more](https://help.shopify.com/manual/products/collections/collection-layout)"}, "show_collection_description": {"label": "Show collection description"}, "show_collection_image": {"label": "Show collection image", "info": "For best results, use an image with a 16:9 aspect ratio. [Learn more](https://help.shopify.com/manual/shopify-admin/productivity-tools/image-editor#understanding-image-aspect-ratio)"}}}, "main-collection-product-grid": {"name": "Product grid", "settings": {"products_per_page": {"label": "Products per page"}, "columns_desktop": {"label": "Number of columns on desktop"}, "enable_filtering": {"label": "Enable filtering", "info": "Customize filters with the Search & Discovery app. [Learn more](https://help.shopify.com/manual/online-store/search-and-discovery/filters)"}, "filter_type": {"label": "Desktop filter layout", "options__1": {"label": "Horizontal"}, "options__2": {"label": "Vertical"}, "options__3": {"label": "Drawer"}, "info": "Drawer is the default mobile layout."}, "enable_sorting": {"label": "Enable sorting"}, "image_ratio": {"label": "Image ratio", "options__1": {"label": "Adapt to image"}, "options__2": {"label": "Portrait"}, "options__3": {"label": "Square"}}, "show_secondary_image": {"label": "Show second image on hover"}, "show_vendor": {"label": "Show vendor"}, "show_rating": {"label": "Show product rating", "info": "To display a rating, add a product rating app. [Learn more](https://help.shopify.com/manual/online-store/themes/theme-structure/page-types#product-grid-section-settings)"}, "header__1": {"content": "Filtering and sorting"}, "header__3": {"content": "Product card"}, "enable_tags": {"label": "Enable filtering", "info": "Customize filters with the Search & Discovery app. [Learn more](https://help.shopify.com/manual/online-store/search-and-discovery/filters)"}, "enable_quick_buy": {"label": "Enable quick add button", "info": "Optimal with popup or drawer cart type."}, "header_mobile": {"content": "Mobile Layout"}, "columns_mobile": {"label": "Number of columns on mobile", "options__1": {"label": "1 column"}, "options__2": {"label": "2 columns"}}}}, "main-list-collections": {"name": "Collections list page", "settings": {"title": {"label": "Heading"}, "sort": {"label": "Sort collections by:", "options__1": {"label": "Alphabetically, A-Z"}, "options__2": {"label": "Alphabetically, Z-A"}, "options__3": {"label": "Date, new to old"}, "options__4": {"label": "Date, old to new"}, "options__5": {"label": "Product count, high to low"}, "options__6": {"label": "Product count, low to high"}}, "image_ratio": {"label": "Image ratio", "options__1": {"label": "Adapt to image"}, "options__2": {"label": "Portrait"}, "options__3": {"label": "Square"}, "info": "Add images by editing your collections. [Learn more](https://help.shopify.com/manual/products/collections)"}, "columns_desktop": {"label": "Number of columns on desktop"}, "header_mobile": {"content": "Mobile Layout"}, "columns_mobile": {"label": "Number of columns on mobile", "options__1": {"label": "1 column"}, "options__2": {"label": "2 columns"}}}}, "main-login": {"name": "<PERSON><PERSON>"}, "main-order": {"name": "Order"}, "main-page": {"name": "Page"}, "main-password-footer": {"name": "Password footer"}, "main-password-header": {"name": "Password header", "settings": {"logo_header": {"content": "Logo"}, "logo_help": {"content": "Edit your logo in theme settings."}}}, "main-product": {"name": "Product information", "blocks": {"text": {"name": "Text", "settings": {"text": {"label": "Text"}, "text_style": {"label": "Text style", "options__1": {"label": "Body"}, "options__2": {"label": "Subtitle"}, "options__3": {"label": "Uppercase"}}}}, "title": {"name": "Title"}, "price": {"name": "Price"}, "inventory": {"name": "Inventory status", "settings": {"text_style": {"label": "Text style", "options__1": {"label": "Body"}, "options__2": {"label": "Subtitle"}, "options__3": {"label": "Uppercase"}}, "inventory_threshold": {"label": "Low inventory threshold", "info": "Choose 0 to always show in stock if available."}, "show_inventory_quantity": {"label": "Show inventory count"}}}, "quantity_selector": {"name": "Quantity selector"}, "variant_picker": {"name": "Variant picker", "settings": {"picker_type": {"label": "Style", "options__1": {"label": "Dropdown"}, "options__2": {"label": "Pills"}}, "swatch_shape": {"label": "Swatch", "info": "Enable [swatches](https://help.shopify.com/en/manual/online-store/themes/theme-structure/theme-settings#options-with-swatches) on product options.", "options__1": {"label": "Circle"}, "options__2": {"label": "Square"}, "options__3": {"label": "None"}}}}, "buy_buttons": {"name": "Buy buttons", "settings": {"show_dynamic_checkout": {"label": "Show dynamic checkout buttons", "info": "Using the payment methods available on your store, customers see their preferred option, like PayPal or Apple Pay. [Learn more](https://help.shopify.com/manual/using-themes/change-the-layout/dynamic-checkout)"}, "show_gift_card_recipient": {"label": "Show recipient information form for gift cards", "info": "Allows buyers to send gift cards on a scheduled date along with a personal message. [Learn more](https://help.shopify.com/manual/online-store/themes/customizing-themes/add-gift-card-recipient-fields)"}}}, "pickup_availability": {"name": "Pickup availability"}, "description": {"name": "Description"}, "sku": {"name": "SKU", "settings": {"text_style": {"label": "Text style", "options__1": {"label": "Body"}, "options__2": {"label": "Subtitle"}, "options__3": {"label": "Uppercase"}}}}, "share": {"name": "Share", "settings": {"text": {"label": "Text"}, "featured_image_info": {"content": "If you include a link in social media posts, the page’s featured image will be shown as the preview image. [Learn more](https://help.shopify.com/manual/online-store/images/showing-social-media-thumbnail-images)."}, "title_info": {"content": "A store title and description are included with the preview image. [Learn more](https://help.shopify.com/manual/promoting-marketing/seo/adding-keywords#set-a-title-and-description-for-your-online-store)."}}}, "collapsible_tab": {"name": "Collapsible row", "settings": {"heading": {"info": "Include a heading that explains the content.", "label": "Heading"}, "content": {"label": "Row content"}, "page": {"label": "Row content from page"}, "icon": {"label": "Icon", "options__1": {"label": "None"}, "options__2": {"label": "Apple"}, "options__3": {"label": "Banana"}, "options__4": {"label": "<PERSON><PERSON>"}, "options__5": {"label": "Box"}, "options__6": {"label": "Carrot"}, "options__7": {"label": "Chat bubble"}, "options__8": {"label": "Check mark"}, "options__9": {"label": "Clipboard"}, "options__10": {"label": "Dairy"}, "options__11": {"label": "Dairy free"}, "options__12": {"label": "Dryer"}, "options__13": {"label": "Eye"}, "options__14": {"label": "Fire"}, "options__15": {"label": "Gluten free"}, "options__16": {"label": "Heart"}, "options__17": {"label": "Iron"}, "options__18": {"label": "Leaf"}, "options__19": {"label": "Leather"}, "options__20": {"label": "Lightning bolt"}, "options__21": {"label": "Lipstick"}, "options__22": {"label": "Lock"}, "options__23": {"label": "Map pin"}, "options__24": {"label": "Nut free"}, "options__25": {"label": "<PERSON>ts"}, "options__26": {"label": "Paw print"}, "options__27": {"label": "Pepper"}, "options__28": {"label": "Perfume"}, "options__29": {"label": "Plane"}, "options__30": {"label": "Plant"}, "options__31": {"label": "Price tag"}, "options__32": {"label": "Question mark"}, "options__33": {"label": "Recycle"}, "options__34": {"label": "Return"}, "options__35": {"label": "Ruler"}, "options__36": {"label": "Serving dish"}, "options__37": {"label": "Shirt"}, "options__38": {"label": "Shoe"}, "options__39": {"label": "<PERSON><PERSON><PERSON><PERSON>"}, "options__40": {"label": "Snowflake"}, "options__41": {"label": "Star"}, "options__42": {"label": "Stopwatch"}, "options__43": {"label": "Truck"}, "options__44": {"label": "Washing"}}}}, "popup": {"name": "Pop-up", "settings": {"link_label": {"label": "Link label"}, "page": {"label": "Page"}}}, "rating": {"name": "Product rating", "settings": {"paragraph": {"content": "To display a rating, add a product rating app. [Learn more](https://help.shopify.com/manual/online-store/themes/theme-structure/page-types#product-rating-block)"}}}, "complementary_products": {"name": "Complementary products", "settings": {"paragraph": {"content": "To select complementary products, add the Search & Discovery app. [Learn more](https://help.shopify.com/manual/online-store/search-and-discovery/product-recommendations)"}, "heading": {"label": "Heading"}, "make_collapsible_row": {"label": "Show as collapsible row"}, "icon": {"info": "Visible when collapsible row is displayed."}, "product_list_limit": {"label": "Maximum products to show"}, "products_per_page": {"label": "Number of products per page"}, "pagination_style": {"label": "Pagination style", "options": {"option_1": "Dots", "option_2": "Counter", "option_3": "Numbers"}}, "product_card": {"heading": "Product card"}, "image_ratio": {"label": "Image ratio", "options": {"option_1": "Portrait", "option_2": "Square"}}, "enable_quick_add": {"label": "Enable quick add button"}}}, "icon_with_text": {"name": "Icon with text", "settings": {"layout": {"label": "Layout", "options__1": {"label": "Horizontal"}, "options__2": {"label": "Vertical"}}, "content": {"label": "Content", "info": "Choose an icon or add an image for each column or row."}, "heading": {"info": "Leave the heading label blank to hide the icon column."}, "icon_1": {"label": "First icon"}, "image_1": {"label": "First image"}, "heading_1": {"label": "First heading"}, "icon_2": {"label": "Second icon"}, "image_2": {"label": "Second image"}, "heading_2": {"label": "Second heading"}, "icon_3": {"label": "Third icon"}, "image_3": {"label": "Third image"}, "heading_3": {"label": "Third heading"}}}}, "settings": {"header": {"content": "Media", "info": "Learn more about [media types.](https://help.shopify.com/manual/products/product-media)"}, "enable_sticky_info": {"label": "Enable sticky content on desktop"}, "gallery_layout": {"label": "Desktop layout", "options__1": {"label": "Stacked"}, "options__2": {"label": "2 columns"}, "options__3": {"label": "Thumbnails"}, "options__4": {"label": "Thumbnail carousel"}}, "constrain_to_viewport": {"label": "Constrain media to screen height"}, "media_size": {"label": "Desktop media width", "info": "Media is automatically optimized for mobile.", "options__1": {"label": "Small"}, "options__2": {"label": "Medium"}, "options__3": {"label": "Large"}}, "image_zoom": {"label": "Image zoom", "info": "Click and hover defaults to open lightbox on mobile.", "options__1": {"label": "Open lightbox"}, "options__2": {"label": "Click and hover"}, "options__3": {"label": "No zoom"}}, "media_position": {"label": "Desktop media position", "info": "Position is automatically optimized for mobile.", "options__1": {"label": "Left"}, "options__2": {"label": "Right"}}, "media_fit": {"label": "Media fit", "options__1": {"label": "Original"}, "options__2": {"label": "Fill"}}, "mobile_thumbnails": {"label": "Mobile layout", "options__1": {"label": "2 columns"}, "options__2": {"label": "Show thumbnails"}, "options__3": {"label": "Hide thumbnails"}}, "hide_variants": {"label": "Hide other variants’ media after selecting a variant"}, "enable_video_looping": {"label": "Enable video looping"}}}, "main-register": {"name": "Registration"}, "main-reset-password": {"name": "Password reset"}, "main-search": {"name": "Search results", "settings": {"columns_desktop": {"label": "Number of columns on desktop"}, "image_ratio": {"label": "Image ratio", "options__1": {"label": "Adapt to image"}, "options__2": {"label": "Portrait"}, "options__3": {"label": "Square"}}, "show_secondary_image": {"label": "Show second image on hover"}, "show_vendor": {"label": "Show vendor"}, "show_rating": {"label": "Show product rating", "info": "To display a rating, add a product rating app. [Learn more](https://help.shopify.com/manual/online-store/themes/theme-structure/page-types#search-results-section-settings)"}, "header__1": {"content": "Product card"}, "header__2": {"content": "Blog card", "info": "Blog card styles also apply to page cards in search results. To change card styles update your theme settings."}, "article_show_date": {"label": "Show date"}, "article_show_author": {"label": "Show author"}, "header_mobile": {"content": "Mobile Layout"}, "columns_mobile": {"label": "Number of columns on mobile", "options__1": {"label": "1 column"}, "options__2": {"label": "2 columns"}}}}, "multicolumn": {"name": "Multicolumn", "settings": {"title": {"label": "Heading"}, "image_width": {"label": "Image width", "options__1": {"label": "One-third width of column"}, "options__2": {"label": "Half width of column"}, "options__3": {"label": "Full width of column"}}, "image_ratio": {"label": "Image ratio", "options__1": {"label": "Adapt to image"}, "options__2": {"label": "Portrait"}, "options__3": {"label": "Square"}, "options__4": {"label": "Circle"}}, "columns_desktop": {"label": "Number of columns on desktop"}, "column_alignment": {"label": "Column alignment", "options__1": {"label": "Left"}, "options__2": {"label": "Center"}}, "background_style": {"label": "Secondary background", "options__1": {"label": "None"}, "options__2": {"label": "Show as column background"}}, "button_label": {"label": "Button label"}, "button_link": {"label": "Button link"}, "header_mobile": {"content": "Mobile Layout"}, "columns_mobile": {"label": "Number of columns on mobile", "options__1": {"label": "1 column"}, "options__2": {"label": "2 columns"}}, "swipe_on_mobile": {"label": "Enable swipe on mobile"}}, "blocks": {"column": {"name": "Column", "settings": {"image": {"label": "Image"}, "title": {"label": "Heading"}, "text": {"label": "Description"}, "link_label": {"label": "Link label"}, "link": {"label": "Link"}}}}, "presets": {"name": "Multicolumn"}}, "newsletter": {"name": "Email signup", "settings": {"full_width": {"label": "Make section full width"}, "paragraph": {"content": "Each email subscription creates a customer account. [Learn more](https://help.shopify.com/manual/customers)"}}, "blocks": {"heading": {"name": "Heading", "settings": {"heading": {"label": "Heading"}}}, "paragraph": {"name": "Subheading", "settings": {"paragraph": {"label": "Description"}}}, "email_form": {"name": "Email form"}}, "presets": {"name": "Email signup"}}, "email-signup-banner": {"name": "Email signup banner", "settings": {"paragraph": {"content": "Each email subscription creates a customer account. [Learn more](https://help.shopify.com/manual/customers)"}, "image": {"label": "Background image"}, "image_overlay_opacity": {"label": "Image overlay opacity"}, "show_background_image": {"label": "Show background image"}, "show_text_box": {"label": "Show container on desktop"}, "desktop_content_position": {"options__1": {"label": "Top Left"}, "options__2": {"label": "Top Center"}, "options__3": {"label": "Top Right"}, "options__4": {"label": "Middle Left"}, "options__5": {"label": "Middle Center"}, "options__6": {"label": "Middle Right"}, "options__7": {"label": "Bottom Left"}, "options__8": {"label": "Bottom Center"}, "options__9": {"label": "Bottom Right"}, "label": "Desktop content position"}, "color_scheme": {"info": "Visible when container displayed."}, "image_height": {"label": "Banner height", "options__1": {"label": "Adapt to image"}, "options__2": {"label": "Small"}, "options__3": {"label": "Medium"}, "options__4": {"label": "Large"}, "info": "For best results, use an image with a 16:9 aspect ratio. [Learn more](https://help.shopify.com/manual/shopify-admin/productivity-tools/image-editor#understanding-image-aspect-ratio)"}, "desktop_content_alignment": {"options__1": {"label": "Left"}, "options__2": {"label": "Center"}, "options__3": {"label": "Right"}, "label": "Desktop content alignment"}, "header": {"content": "Mobile Layout"}, "mobile_content_alignment": {"options__1": {"label": "Left"}, "options__2": {"label": "Center"}, "options__3": {"label": "Right"}, "label": "Mobile content alignment"}, "show_text_below": {"label": "Show content below image on mobile", "info": "For best results, use an image with a 16:9 aspect ratio. [Learn more](https://help.shopify.com/manual/shopify-admin/productivity-tools/image-editor#understanding-image-aspect-ratio)"}}, "blocks": {"heading": {"name": "Heading", "settings": {"heading": {"label": "Heading"}}}, "paragraph": {"name": "Paragraph", "settings": {"paragraph": {"label": "Description"}, "text_style": {"options__1": {"label": "Body"}, "options__2": {"label": "Subtitle"}, "label": "Text style"}}}, "email_form": {"name": "Email form"}}, "presets": {"name": "Email signup banner"}}, "page": {"name": "Page", "settings": {"page": {"label": "Page"}}, "presets": {"name": "Page"}}, "quick-order-list": {"name": "Quick order list", "settings": {"show_image": {"label": "Show images"}, "show_sku": {"label": "Show SKUs"}}, "presets": {"name": "Quick order list"}}, "related-products": {"name": "Related products", "settings": {"heading": {"label": "Heading"}, "products_to_show": {"label": "Maximum products to show"}, "columns_desktop": {"label": "Number of columns on desktop"}, "paragraph__1": {"content": "Dynamic recommendations use order and product information to change and improve over time. [Learn more](https://help.shopify.com/themes/development/recommended-products)"}, "header__2": {"content": "Product card"}, "image_ratio": {"label": "Image ratio", "options__1": {"label": "Adapt to image"}, "options__2": {"label": "Portrait"}, "options__3": {"label": "Square"}}, "show_secondary_image": {"label": "Show second image on hover"}, "show_vendor": {"label": "Show vendor"}, "show_rating": {"label": "Show product rating", "info": "To display a rating, add a product rating app. [Learn more](https://help.shopify.com/manual/online-store/themes/theme-structure/page-types#product-recommendations-section-settings)"}, "header_mobile": {"content": "Mobile Layout"}, "columns_mobile": {"label": "Number of columns on mobile", "options__1": {"label": "1 column"}, "options__2": {"label": "2 columns"}}}}, "rich-text": {"name": "Rich text", "settings": {"desktop_content_position": {"options__1": {"label": "Left"}, "options__2": {"label": "Center"}, "options__3": {"label": "Right"}, "label": "Desktop content position", "info": "Position is automatically optimized for mobile."}, "content_alignment": {"options__1": {"label": "Left"}, "options__2": {"label": "Center"}, "options__3": {"label": "Right"}, "label": "Content alignment"}, "full_width": {"label": "Make section full width"}}, "blocks": {"heading": {"name": "Heading", "settings": {"heading": {"label": "Heading"}}}, "caption": {"name": "Caption", "settings": {"text": {"label": "Text"}, "text_style": {"label": "Text style", "options__1": {"label": "Subtitle"}, "options__2": {"label": "Uppercase"}}, "caption_size": {"label": "Text size", "options__1": {"label": "Small"}, "options__2": {"label": "Medium"}, "options__3": {"label": "Large"}}}}, "text": {"name": "Text", "settings": {"text": {"label": "Description"}}}, "buttons": {"name": "Buttons", "settings": {"button_label_1": {"label": "First button label", "info": "Leave the label blank to hide the button."}, "button_link_1": {"label": "First button link"}, "button_style_secondary_1": {"label": "Use outline button style"}, "button_label_2": {"label": "Second button label", "info": "Leave the label blank to hide the button."}, "button_link_2": {"label": "Second button link"}, "button_style_secondary_2": {"label": "Use outline button style"}}}}, "presets": {"name": "Rich text"}}, "video": {"name": "Video", "settings": {"heading": {"label": "Heading"}, "cover_image": {"label": "Cover image"}, "video": {"label": "Video"}, "enable_video_looping": {"label": "Play video on loop"}, "header__1": {"content": "Shopify-hosted video"}, "header__2": {"content": "Or embed video from URL"}, "header__3": {"content": "Style"}, "paragraph": {"content": "Shows when no Shopify-hosted video is selected."}, "video_url": {"label": "URL", "info": "Use a YouTube or Vimeo URL"}, "description": {"label": "Video alt text", "info": "Describe the video for customers using screen readers. [Learn more](https://help.shopify.com/manual/online-store/themes/theme-structure/theme-features#video)"}, "image_padding": {"label": "Add image padding", "info": "Select image padding if you don't want your cover image to be cropped."}, "full_width": {"label": "Make section full width"}}, "presets": {"name": "Video"}}, "slideshow": {"name": "Slideshow", "settings": {"layout": {"label": "Layout", "options__1": {"label": "Full width"}, "options__2": {"label": "Grid"}}, "slide_height": {"label": "Slide height", "options__1": {"label": "Adapt to first image"}, "options__2": {"label": "Small"}, "options__3": {"label": "Medium"}, "options__4": {"label": "Large"}}, "slider_visual": {"label": "Pagination style", "options__1": {"label": "Counter"}, "options__2": {"label": "Dots"}, "options__3": {"label": "Numbers"}}, "auto_rotate": {"label": "Auto-rotate slides"}, "change_slides_speed": {"label": "Change slides every"}, "mobile": {"content": "Mobile layout"}, "show_text_below": {"label": "Show content below images on mobile"}, "accessibility": {"content": "Accessibility", "label": "Slideshow description", "info": "Describe the slideshow for customers using screen readers."}}, "blocks": {"slide": {"name": "Slide", "settings": {"image": {"label": "Image"}, "heading": {"label": "Heading"}, "subheading": {"label": "Subheading"}, "button_label": {"label": "Button label", "info": "Leave the label blank to hide the button."}, "link": {"label": "Button link"}, "secondary_style": {"label": "Use outline button style"}, "box_align": {"label": "Desktop content position", "info": "Position is automatically optimized for mobile.", "options__1": {"label": "Top left"}, "options__2": {"label": "Top center"}, "options__3": {"label": "Top right"}, "options__4": {"label": "Middle left"}, "options__5": {"label": "Middle center"}, "options__6": {"label": "Middle right"}, "options__7": {"label": "Bottom left"}, "options__8": {"label": "Bottom center"}, "options__9": {"label": "Bottom right"}}, "show_text_box": {"label": "Show container on desktop"}, "text_alignment": {"label": "Desktop content alignment", "option_1": {"label": "Left"}, "option_2": {"label": "Center"}, "option_3": {"label": "Right"}}, "image_overlay_opacity": {"label": "Image overlay opacity"}, "text_alignment_mobile": {"label": "Mobile content alignment", "options__1": {"label": "Left"}, "options__2": {"label": "Center"}, "options__3": {"label": "Right"}}}}}, "presets": {"name": "Slideshow"}}, "collapsible_content": {"name": "Collapsible content", "settings": {"caption": {"label": "Caption"}, "heading": {"label": "Heading"}, "heading_alignment": {"label": "Heading alignment", "options__1": {"label": "Left"}, "options__2": {"label": "Center"}, "options__3": {"label": "Right"}}, "layout": {"label": "Layout", "options__1": {"label": "No container"}, "options__2": {"label": "Row container"}, "options__3": {"label": "Section container"}}, "container_color_scheme": {"label": "Container color scheme", "info": "Visible when Layout is set to Row or Section container."}, "open_first_collapsible_row": {"label": "Open first collapsible row"}, "header": {"content": "Image layout"}, "image": {"label": "Image"}, "image_ratio": {"label": "Image ratio", "options__1": {"label": "Adapt to image"}, "options__2": {"label": "Small"}, "options__3": {"label": "Large"}}, "desktop_layout": {"label": "Desktop layout", "options__1": {"label": "Image first"}, "options__2": {"label": "Image second"}, "info": "Image is always first on mobile."}}, "blocks": {"collapsible_row": {"name": "Collapsible row", "settings": {"heading": {"info": "Include a heading that explains the content.", "label": "Heading"}, "row_content": {"label": "Row content"}, "page": {"label": "Row content from page"}, "icon": {"label": "Icon", "options__1": {"label": "None"}, "options__2": {"label": "Apple"}, "options__3": {"label": "Banana"}, "options__4": {"label": "<PERSON><PERSON>"}, "options__5": {"label": "Box"}, "options__6": {"label": "Carrot"}, "options__7": {"label": "Chat bubble"}, "options__8": {"label": "Check mark"}, "options__9": {"label": "Clipboard"}, "options__10": {"label": "Dairy"}, "options__11": {"label": "Dairy free"}, "options__12": {"label": "Dryer"}, "options__13": {"label": "Eye"}, "options__14": {"label": "Fire"}, "options__15": {"label": "Gluten free"}, "options__16": {"label": "Heart"}, "options__17": {"label": "Iron"}, "options__18": {"label": "Leaf"}, "options__19": {"label": "Leather"}, "options__20": {"label": "Lightning bolt"}, "options__21": {"label": "Lipstick"}, "options__22": {"label": "Lock"}, "options__23": {"label": "Map pin"}, "options__24": {"label": "Nut free"}, "options__25": {"label": "<PERSON>ts"}, "options__26": {"label": "Paw print"}, "options__27": {"label": "Pepper"}, "options__28": {"label": "Perfume"}, "options__29": {"label": "Plane"}, "options__30": {"label": "Plant"}, "options__31": {"label": "Price tag"}, "options__32": {"label": "Question mark"}, "options__33": {"label": "Recycle"}, "options__34": {"label": "Return"}, "options__35": {"label": "Ruler"}, "options__36": {"label": "Serving dish"}, "options__37": {"label": "Shirt"}, "options__38": {"label": "Shoe"}, "options__39": {"label": "<PERSON><PERSON><PERSON><PERSON>"}, "options__40": {"label": "Snowflake"}, "options__41": {"label": "Star"}, "options__42": {"label": "Stopwatch"}, "options__43": {"label": "Truck"}, "options__44": {"label": "Washing"}}}}}, "presets": {"name": "Collapsible content"}}}}