/*
 * ------------------------------------------------------------
 * IMPORTANT: The contents of this file are auto-generated.
 *
 * This file may be updated by the Shopify admin theme editor
 * or related systems. Please exercise caution as any changes
 * made to this file may be overwritten.
 * ------------------------------------------------------------
 */
{
  "sections": {
    "rcl_hero_section_Bmrbay": {
      "type": "rcl-hero-section",
      "blocks": {
        "heading_YcAVQ7": {
          "type": "heading",
          "settings": {
            "heading": "Our Collections",
            "heading_size": "h1",
            "heading_color": "#ffffff"
          }
        },
        "subheading_4NiTFr": {
          "type": "subheading",
          "settings": {
            "subheading": "Elf Decor Premium Products",
            "subheading_color": "#d1b073"
          }
        }
      },
      "block_order": [
        "heading_YcAVQ7",
        "subheading_4NiTFr"
      ],
      "settings": {
        "hero_image": "shopify://shop_images/7260c61656015d288167c3d198e11fc7_8c7f8c4f-7f0a-4361-8b53-3c776955f54c.jpg",
        "text_alignment": "text-center",
        "desktop_height": 400,
        "mobile_height": 300,
        "overlay_color": "#222228",
        "overlay_opacity": 70,
        "parallax_effect": true,
        "enable_image_filter": false,
        "filter_opacity": 50
      }
    },
    "main": {
      "type": "main-list-collections",
      "custom_css": [
        "h1 {display: none;}",
        ".collection-list {margin: 3rem 0;}"
      ],
      "settings": {
        "title": "Collections",
        "sort": "date_reversed",
        "image_ratio": "square",
        "columns_desktop": 3,
        "columns_mobile": "1"
      }
    }
  },
  "order": [
    "rcl_hero_section_Bmrbay",
    "main"
  ]
}
