{"general": {"password_page": {"login_form_heading": "<PERSON><PERSON><PERSON> toko den<PERSON> sandi:", "login_password_button": "<PERSON><PERSON><PERSON> den<PERSON> sandi", "login_form_password_label": "<PERSON><PERSON>", "login_form_password_placeholder": "<PERSON><PERSON>", "login_form_error": "<PERSON>i salah!", "login_form_submit": "<PERSON><PERSON><PERSON>", "admin_link_html": "<PERSON><PERSON> pemilik toko? <a href=\"/admin\" class=\"link underlined-link\">Login di sini</a>", "powered_by_shopify_html": "Toko ini didukung oleh {{ shopify }}"}, "social": {"alt_text": {"share_on_facebook": "Bagikan di Facebook", "share_on_twitter": "Tweet di Twitter", "share_on_pinterest": "Pin <PERSON> Pinterest"}, "links": {"twitter": "Twitter", "facebook": "Facebook", "pinterest": "Pinterest", "instagram": "Instagram", "tumblr": "Tumblr", "snapchat": "Snapchat", "youtube": "YouTube", "vimeo": "Vimeo", "tiktok": "TikTok"}}, "continue_shopping": "Lanju<PERSON><PERSON> belanja", "pagination": {"label": "<PERSON><PERSON><PERSON><PERSON>", "page": "<PERSON>aman {{ number }}", "next": "Halaman berik<PERSON>", "previous": "Halaman sebelumnya"}, "search": {"search": "<PERSON><PERSON>", "reset": "<PERSON>pus kata kunci pencarian"}, "cart": {"view": "<PERSON><PERSON> keranjang ({{ count }})", "item_added": "Item ditambahkan ke keranjang Anda", "view_empty_cart": "<PERSON>hat keranjang"}, "share": {"copy_to_clipboard": "<PERSON><PERSON>an", "share_url": "Tautan", "success_message": "Tautan disalin ke clipboard", "close": "<PERSON><PERSON><PERSON>ikan"}, "slider": {"of": "dari", "next_slide": "<PERSON><PERSON><PERSON>", "previous_slide": "<PERSON><PERSON><PERSON> kiri", "name": "Slide<PERSON>"}}, "newsletter": {"label": "Email", "success": "<PERSON><PERSON> kasih sudah be<PERSON>an", "button_label": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "accessibility": {"skip_to_text": "Langsung ke konten", "close": "<PERSON><PERSON><PERSON>", "unit_price_separator": "per", "vendor": "Vendor:", "error": "<PERSON><PERSON><PERSON>", "refresh_page": "<PERSON>ka memilih salah satu, selu<PERSON>h halaman akan dimuat ulang.", "link_messages": {"new_window": "Membuka di jendela baru.", "external": "Membuka situs web eksternal."}, "loading": "Memuat...", "skip_to_product_info": "Langsung ke informasi produk", "total_reviews": "<PERSON><PERSON><PERSON> k<PERSON>", "star_reviews_info": "{{ rating_value }} dari {{ rating_max }} bintang", "collapsible_content_title": "<PERSON><PERSON>n yang dapat ditutup", "complementary_products": "Produk pelengkap"}, "blogs": {"article": {"blog": "Blog", "read_more_title": "Baca selengkapnya: {{ title }}", "comments": {"one": "{{ count }} komentar", "other": "{{ count }} komentar"}, "moderated": "Ingat, komentar perlu disetujui sebelum dipublikasikan.", "comment_form_title": "<PERSON><PERSON> k<PERSON>", "name": "<PERSON><PERSON>", "email": "Email", "message": "Komentar", "post": "Posting komentar", "back_to_blog": "Ke<PERSON><PERSON> ke blog", "share": "Bagikan artikel ini", "success": "<PERSON><PERSON><PERSON> Anda berhasil diposting! <PERSON><PERSON> kasih!", "success_moderated": "Komentar Anda berhasil diposting. Ka<PERSON> akan memublikasikan<PERSON> sebentar lagi, blog kami sedang dimoderasi."}}, "onboarding": {"product_title": "<PERSON><PERSON><PERSON> judul produk", "collection_title": "<PERSON><PERSON>"}, "products": {"product": {"add_to_cart": "Tambahkan ke keranjang", "description": "<PERSON><PERSON><PERSON><PERSON>", "on_sale": "Obral", "product_variants": "Varian produk", "quantity": {"label": "<PERSON><PERSON><PERSON>", "input_label": "<PERSON><PERSON><PERSON> {{ product }}", "increase": "Tambah jumlah untuk {{ product }}", "decrease": "<PERSON><PERSON><PERSON> jumlah untuk {{ product }}", "minimum_of": "Minimum {{ quantity }}", "maximum_of": "Ma<PERSON><PERSON>um {{ quantity }}", "multiples_of": "<PERSON><PERSON> pen<PERSON> {{ quantity }}", "in_cart_html": "<span class=\"quantity-cart\">{{ quantity }}</span> di keranjang", "note": "<PERSON><PERSON> at<PERSON>n k<PERSON>"}, "price": {"from_price_html": "Dar<PERSON> {{ price }}", "regular_price": "<PERSON><PERSON> reguler", "sale_price": "Harga obral", "unit_price": "<PERSON><PERSON> satuan"}, "share": "Bagikan produk ini", "sold_out": "Habis", "unavailable": "Tidak Tersedia", "vendor": "<PERSON><PERSON><PERSON>", "video_exit_message": "{{ title }} membuka video layar penuh di jendela yang sama.", "xr_button": "Lihat di lokasi Anda", "xr_button_label": "<PERSON>hat di lokasi <PERSON>, muat item di jendela realitas tertambah", "pickup_availability": {"view_store_info": "<PERSON><PERSON> informasi toko", "check_other_stores": "<PERSON>ik<PERSON> ketersed<PERSON> di toko la<PERSON>", "pick_up_available": "Pengambilan tersedia", "pick_up_available_at_html": "Pengambilan dapat di<PERSON>ukan di <span class=\"color-foreground\">{{ location_name }}</span>", "pick_up_unavailable_at_html": "<PERSON><PERSON> ini, pengambilan tidak dapat dilakukan di <span class=\"color-foreground\">{{ location_name }}</span>", "unavailable": "Tidak dapat memuat ketersediaan pengambilan", "refresh": "<PERSON><PERSON> ul<PERSON>"}, "media": {"open_media": "Buka media {{ index }} di modal", "play_model": "Putar Penampil 3D", "play_video": "Putar video", "gallery_viewer": "<PERSON><PERSON><PERSON>", "load_image": "Muat gambar {{ index }} di tampilan galeri", "load_model": "Muat Model 3D {{ index }} di tampilan galeri", "load_video": "Putar video {{ index }} di tampilan galeri", "image_available": "Gambar {{ index }} kini tersedia di tampilan galeri"}, "view_full_details": "Lihat detail lengkap", "include_taxes": "Termasuk pajak.", "shipping_policy_html": "<a href=\"{{ link }}\"><PERSON><PERSON><PERSON></a> dihitung saat checkout.", "choose_options": "<PERSON><PERSON>h opsi", "choose_product_options": "<PERSON><PERSON>h opsi untuk {{ product_name }}", "value_unavailable": "{{ option_value }} - Tidak tersedia", "variant_sold_out_or_unavailable": "Varian terjual habis atau tidak tersedia", "inventory_in_stock": "Tersedia", "inventory_in_stock_show_count": "{{ quantity }} tersedia", "inventory_low_stock": "Stok sedikit", "inventory_low_stock_show_count": "Stok sedikit: {{ quantity }} tersisa", "inventory_out_of_stock": "Habis", "inventory_out_of_stock_continue_selling": "Tersedia", "sku": "SKU", "volume_pricing": {"title": "<PERSON>rga <PERSON>", "note": "Harga volume tersedia", "minimum": "{{ quantity }}+", "price_at_each": "harga {{ price }}/satuan", "price_range": "{{ minimum }} - {{ maximum }}"}}, "modal": {"label": "Galeri media"}, "facets": {"apply": "<PERSON><PERSON>", "clear": "Hapus", "clear_all": "<PERSON><PERSON> semua", "from": "<PERSON><PERSON>", "filter_and_sort": "Filter dan urutkan", "filter_by_label": "Filter:", "filter_button": "Filter", "filters_selected": {"one": "{{ count }} dipilih", "other": "{{ count }} dipilih"}, "max_price": "Harga tertinggi adalah {{ price }}", "product_count": {"one": "{{ product_count }} dari {{ count }} produk", "other": "{{ product_count }} dari {{ count }} produk"}, "product_count_simple": {"one": "{{ count }} produk", "other": "{{ count }} produk"}, "reset": "Reset", "sort_button": "Urut<PERSON>", "sort_by_label": "Urutkan berdasarkan:", "to": "Sampai", "clear_filter": "Hapus filter", "filter_selected_accessibility": "{{ type }} ({{ count }} filter dipilih)", "show_more": "Selengkapnya", "show_less": "Semb<PERSON><PERSON><PERSON> la<PERSON>ya", "filter_and_operator_subtitle": "Cocokkan semua"}}, "templates": {"search": {"no_results": "Tidak ada hasil ditemukan untuk “{{ terms }}”. <PERSON><PERSON><PERSON> ejaan atau gunakan kata atau frasa yang berbeda.", "results_with_count": {"one": "{{ count }} hasil", "other": "{{ count }} hasil"}, "title": "<PERSON><PERSON>", "page": "<PERSON><PERSON>", "products": "Produk", "search_for": "<PERSON>i “{{ terms }}”", "results_with_count_and_term": {"one": "{{ count }} hasil di<PERSON>ukan untuk “{{ terms }}”", "other": "{{ count }} hasil di<PERSON>ukan untuk “{{ terms }}”"}, "results_pages_with_count": {"one": "{{ count }} halaman", "other": "{{ count }} halaman"}, "results_products_with_count": {"one": "{{ count }} produk", "other": "{{ count }} produk"}, "suggestions": "Saran", "pages": "<PERSON><PERSON>", "results_suggestions_with_count": {"one": "{{ count }} saran", "other": "{{ count }} saran"}}, "cart": {"cart": "Keranjang"}, "contact": {"form": {"name": "<PERSON><PERSON>", "email": "Email", "phone": "Nomor telepon", "comment": "Komentar", "send": "<PERSON><PERSON>", "post_success": "<PERSON><PERSON> kasih sudah menghubungi kami. Kami akan segera menghubungi Anda.", "error_heading": "<PERSON><PERSON>:", "title": "<PERSON><PERSON><PERSON> k<PERSON>"}}, "404": {"title": "Halaman tidak ditemukan", "subtext": "404"}}, "sections": {"header": {"announcement": "<PERSON><PERSON><PERSON>", "menu": "<PERSON><PERSON>", "cart_count": {"one": "{{ count }} item", "other": "{{ count }} item"}}, "cart": {"title": "<PERSON><PERSON><PERSON><PERSON>", "caption": "<PERSON><PERSON> di k<PERSON>g", "remove_title": "Ha<PERSON> {{ title }}", "note": "<PERSON><PERSON><PERSON><PERSON> k<PERSON> p<PERSON>an", "checkout": "Check out", "empty": "Keranjang Anda kosong", "cart_error": "<PERSON><PERSON><PERSON><PERSON> kesalahan saat memperbarui keranjang. <PERSON>lakan coba lagi.", "cart_quantity_error_html": "<PERSON>ya dapat menambahkan {{ quantity }} item ini ke keranjang Anda.", "taxes_and_shipping_policy_at_checkout_html": "<PERSON><PERSON>, <PERSON><PERSON><PERSON> dan <a href=\"{{ link }}\">bi<PERSON></a> dihitung saat checkout", "taxes_included_but_shipping_at_checkout": "<PERSON><PERSON> yang berlaku dan biaya pengiriman serta diskon dihitung saat checkout", "taxes_included_and_shipping_policy_html": "Termasuk pajak. <a href=\"{{ link }}\"><PERSON><PERSON><PERSON></a> dan diskon dihitung saat checkout.", "taxes_and_shipping_at_checkout": "<PERSON><PERSON>, diskon dan biaya pengiriman dihitung saat checkout", "headings": {"product": "Produk", "price": "<PERSON><PERSON>", "total": "Total", "quantity": "<PERSON><PERSON><PERSON>", "image": "Gambar produk"}, "update": "<PERSON><PERSON><PERSON>", "login": {"title": "Sudah punya akun?", "paragraph_html": "<a href=\"{{ link }}\" class=\"link underlined-link\">Login</a> untuk checkout lebih cepat."}, "estimated_total": "Estimasi total", "new_estimated_total": "Estimasi total baru"}, "footer": {"payment": "<PERSON><PERSON>"}, "featured_blog": {"view_all": "<PERSON><PERSON>a", "onboarding_title": "Postingan blog", "onboarding_content": "Berikan pelanggan <PERSON>kasan postingan blog Anda"}, "featured_collection": {"view_all": "<PERSON><PERSON>a", "view_all_label": "<PERSON><PERSON> semua produk dalam koleksi {{ collection_name }}"}, "collection_list": {"view_all": "<PERSON><PERSON>a"}, "collection_template": {"title": "<PERSON><PERSON><PERSON><PERSON>", "empty": "Tidak ada produk yang di<PERSON>ukan", "use_fewer_filters_html": "<PERSON><PERSON><PERSON> lebih sedikit filter atau <a class=\"{{ class }}\" href=\"{{ link }}\">hapus semua</a>"}, "video": {"load_video": "Muat video: {{ description }}"}, "slideshow": {"load_slide": "Muat slide", "previous_slideshow": "Slide sebelumnya", "next_slideshow": "Slide berikutnya", "pause_slideshow": "Jeda slideshow", "play_slideshow": "Putar slideshow", "carousel": "Carousel", "slide": "<PERSON><PERSON><PERSON>"}, "page": {"title": "<PERSON><PERSON><PERSON>"}, "announcements": {"previous_announcement": "<PERSON><PERSON><PERSON> sebelumnya", "next_announcement": "<PERSON><PERSON><PERSON>", "carousel": "Carousel", "announcement": "<PERSON><PERSON><PERSON>", "announcement_bar": "<PERSON><PERSON><PERSON>"}, "quick_order_list": {"product_total": "Subtotal produk", "view_cart": "<PERSON>hat keranjang", "each": "{{ money }}/satuan", "product": "Produk", "variant": "<PERSON><PERSON>", "variant_total": "Total varian", "items_added": {"one": "{{ quantity }} item ditambahkan", "other": "{{ quantity }} item ditambahkan"}, "items_removed": {"one": "{{ quantity }} item dihapus", "other": "{{ quantity }} item dihapus"}, "product_variants": "Varian produk", "total_items": "Total item", "remove_all_items_confirmation": "Hapus semua item yang berjumlah {{ quantity }} dari keranjang <PERSON>?", "remove_all": "<PERSON><PERSON>", "cancel": "Batalkan", "remove_all_single_item_confirmation": "Hapus 1 item dari keranjang Anda?"}}, "localization": {"country_label": "Negara/Wilayah", "language_label": "Bahasa", "update_language": "<PERSON><PERSON><PERSON> bahasa", "update_country": "Perbarui negara/wilayah", "search": "<PERSON><PERSON>", "popular_countries_regions": "Negara/wilayah populer", "country_results_count": "{{ count }} negara/wilayah ditemukan"}, "customer": {"account": {"title": "<PERSON><PERSON><PERSON>", "details": "Detail akun", "view_addresses": "<PERSON><PERSON>", "return": "Kembali ke detail Akun"}, "account_fallback": "<PERSON><PERSON><PERSON>", "activate_account": {"title": "Aktifkan akun", "subtext": "Buat sandi untuk mengaktifkan akunmu.", "password": "<PERSON><PERSON>", "password_confirm": "<PERSON><PERSON><PERSON><PERSON><PERSON> sandi", "submit": "Aktifkan akun", "cancel": "<PERSON><PERSON>n"}, "addresses": {"title": "<PERSON><PERSON><PERSON>", "default": "<PERSON><PERSON><PERSON>", "add_new": "Tambah<PERSON> alamat baru", "edit_address": "<PERSON><PERSON>", "first_name": "<PERSON><PERSON> de<PERSON>", "last_name": "<PERSON><PERSON>", "company": "<PERSON><PERSON><PERSON><PERSON>", "address1": "Alamat 1", "address2": "Alamat 2", "city": "Kota", "country": "Negara/Wilayah", "province": "<PERSON><PERSON><PERSON>", "zip": "<PERSON><PERSON> pos", "phone": "Telepon", "set_default": "Atur sebagai alamat default", "add": "<PERSON><PERSON><PERSON> al<PERSON>", "update": "<PERSON><PERSON><PERSON>", "cancel": "<PERSON><PERSON>", "edit": "Edit", "delete": "Hapus", "delete_confirm": "Yakin ingin menghapus alamat ini?"}, "log_in": "<PERSON><PERSON>", "log_out": "Logout", "login_page": {"cancel": "<PERSON><PERSON>", "create_account": "Buat A<PERSON>n", "email": "Email", "forgot_password": "<PERSON><PERSON> sandi?", "guest_continue": "Lanjutkan", "guest_title": "Lanjutkan sebagai tamu", "password": "<PERSON><PERSON>", "title": "<PERSON><PERSON>", "sign_in": "<PERSON><PERSON><PERSON>", "submit": "<PERSON><PERSON>"}, "orders": {"title": "Riwayat pesanan", "order_number": "<PERSON><PERSON><PERSON>", "order_number_link": "<PERSON>mor pesanan {{ number }}", "date": "Tanggal", "payment_status": "Status pembayaran", "fulfillment_status": "Status pesanan", "total": "Total", "none": "<PERSON><PERSON> belum membuat pesanan."}, "recover_password": {"title": "<PERSON><PERSON> sandi", "subtext": "<PERSON><PERSON> akan men<PERSON> email untuk mereset sandi", "success": "<PERSON><PERSON> telah men<PERSON> Anda email berisi tautan untuk memperbarui sandi."}, "register": {"title": "Buat A<PERSON>n", "first_name": "<PERSON><PERSON> de<PERSON>", "last_name": "<PERSON><PERSON>", "email": "Email", "password": "<PERSON><PERSON>", "submit": "Buat"}, "reset_password": {"title": "Reset sandi akun", "subtext": "<PERSON><PERSON><PERSON><PERSON> sandi baru", "password": "<PERSON><PERSON>", "password_confirm": "<PERSON><PERSON><PERSON><PERSON><PERSON> sandi", "submit": "<PERSON><PERSON> sandi"}, "order": {"title": "<PERSON><PERSON><PERSON> {{ name }}", "date_html": "Dibuat pada {{ date }}", "cancelled_html": "<PERSON><PERSON><PERSON> pada {{ date }}", "cancelled_reason": "Alasan: {{ reason }}", "billing_address": "<PERSON><PERSON><PERSON>", "payment_status": "Status Pembayaran", "shipping_address": "<PERSON><PERSON><PERSON>", "fulfillment_status": "<PERSON> Pesanan", "discount": "Diskon", "shipping": "Pen<PERSON><PERSON>", "tax": "<PERSON><PERSON>", "product": "Produk", "sku": "SKU", "price": "<PERSON><PERSON>", "quantity": "<PERSON><PERSON><PERSON>", "total": "Total", "fulfilled_at_html": "<PERSON><PERSON><PERSON> pada {{ date }}", "track_shipment": "<PERSON><PERSON>", "tracking_url": "Tautan pela<PERSON>", "tracking_company": "<PERSON><PERSON><PERSON>", "tracking_number": "Nomor pela<PERSON>", "subtotal": "Subtotal", "total_duties": "<PERSON><PERSON> cu<PERSON>", "total_refunded": "<PERSON>"}}, "gift_cards": {"issued": {"title": "Ini dia voucher senilai {{ value }} Anda untuk {{ shop }}!", "subtext": "Voucher <PERSON>a", "gift_card_code": "Kode voucher", "shop_link": "Kunjungi toko online", "add_to_apple_wallet": "Tambahkan ke Apple Wallet", "qr_image_alt": "Kode QR — pindai untuk menukarkan voucher", "copy_code": "<PERSON>in kode voucher", "expired": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "copy_code_success": "<PERSON><PERSON> ber<PERSON><PERSON> disalin", "how_to_use_gift_card": "Gunakan kode voucher secara online atau kode QR di toko", "expiration_date": "<PERSON><PERSON><PERSON><PERSON><PERSON> pada {{ expires_on }}"}}, "recipient": {"form": {"checkbox": "Saya ingin mengirim ini sebagai hadiah", "email_label": "<PERSON><PERSON>", "email": "Email", "name_label": "<PERSON><PERSON> (opsional)", "name": "<PERSON><PERSON>", "message_label": "<PERSON><PERSON> (opsional)", "message": "<PERSON><PERSON>", "max_characters": "Maks<PERSON>um {{ max_chars }} karakter", "email_label_optional_for_no_js_behavior": "<PERSON><PERSON> (opsional)", "send_on": "TTTT-BB-HH", "send_on_label": "<PERSON><PERSON> (opsional)", "expanded": "Formulir penerima voucher dibuka", "collapsed": "Formulir penerima voucher ditutup"}}}