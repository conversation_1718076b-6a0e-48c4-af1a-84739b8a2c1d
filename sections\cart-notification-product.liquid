{%- if cart != empty -%}
  {%- for item in cart.items -%}
    <div id="cart-notification-product-{{ item.key }}" class="cart-item">
      {%- if item.image -%}
        <div class="cart-notification-product__image global-media-settings">
          <img
            src="{{ item.image | image_url: width: 140 }}"
            alt="{{ item.image.alt | escape }}"
            width="70"
            height="{{ 70 | divided_by: item.image.aspect_ratio | ceil }}"
            loading="lazy"
          >
        </div>
      {%- endif -%}
      <div>
        {%- if settings.show_vendor -%}
          <p class="caption-with-letter-spacing light">{{ item.product.vendor }}</p>
        {%- endif -%}
        <h3 class="cart-notification-product__name h4">{{ item.product.title | escape }}</h3>
        <dl>
          {%- unless item.product.has_only_default_variant -%}
            {%- for option in item.options_with_values -%}
              <div class="product-option">
                <dt>{{ option.name }}:</dt>
                <dd>{{ option.value }}</dd>
              </div>
            {%- endfor -%}
          {%- endunless -%}
          {%- for property in item.properties -%}
            {%- assign property_first_char = property.first | slice: 0 -%}
            {%- if property.last != blank and property_first_char != '_' -%}
              <div class="product-option">
                <dt>{{ property.first }}:</dt>
                <dd>
                  {%- if property.last contains '/uploads/' -%}
                    <a href="{{ property.last }}" class="link" target="_blank">
                      {{ property.last | split: '/' | last }}
                    </a>
                  {%- else -%}
                    {{ property.last }}
                  {%- endif -%}
                </dd>
              </div>
            {%- endif -%}
          {%- endfor -%}
        </dl>
        {%- if item.selling_plan_allocation != null -%}
          <p class="product-option">{{ item.selling_plan_allocation.selling_plan.name }}</p>
        {%- endif -%}
      </div>
    </div>
  {%- endfor -%}
{%- endif -%}
