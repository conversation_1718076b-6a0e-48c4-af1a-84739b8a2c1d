/* Calculator CSS - Add this to your theme assets */
.product-calculator {
  margin-top: 1.5rem;
  position: relative;
}

.calculator-toggle {
  display: inline-flex;
  align-items: center;
  background-color: transparent;
  border: none;
  padding: 0.5rem 0;
  color: rgba(var(--color-base-accent-1), 1);
  text-decoration: underline;
  cursor: pointer;
  transition: opacity 0.2s ease-in-out;
}

.calculator-toggle:hover {
  opacity: 0.7;
}

.calculator-toggle__icon {
  display: inline-flex;
  margin-left: 0.5rem;
}

.quantity-calculator-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  background-color: rgba(0, 0, 0, 0.5);
  backdrop-filter: blur(4px);
  -webkit-backdrop-filter: blur(4px);
  z-index: 1001;
  opacity: 0;
  visibility: hidden;
  transition: opacity 0.3s ease-in-out, visibility 0.3s ease-in-out;
}

.quantity-calculator-overlay.active {
  opacity: 1;
  visibility: visible;
}

.quantity-calculator-overlay.active:empty {
  display: block;
}

body.has-calculator-open {
  overflow: hidden;
}

body.has-calculator-open .shopify-section-header-sticky,
body.has-calculator-open .header-wrapper,
body.has-calculator-open .section-header,
body.has-calculator-open .shopify-section-group-header-group {
  z-index: 0 !important;
}

.quantity-calculator-drawer {
  position: fixed;
  top: 0;
  right: 0;
  width: 100%;
  height: 100vh;
  max-width: 420px;
  background-color: #ffffff;
  z-index: 1002;
  transform: translateX(100%);
  transition: transform 0.3s ease-in-out;
  overflow-y: auto;
  box-shadow: -5px 0 15px rgba(0, 0, 0, 0.1);
  margin: 0!important;
}

.quantity-calculator-drawer.active {
  transform: translateX(0);
}

.quantity-calculator-drawer__container {
  display: flex;
  flex-direction: column;
  height: 100%;
  padding: 0;
}

.quantity-calculator-drawer__header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 1.5rem;
  border-bottom: 1px solid rgba(var(--color-foreground), 0.1);
}

.quantity-calculator-drawer__heading {
  margin: 0;
  font-size: 1.5rem;
}

.quantity-calculator-drawer__close {
  background-color: transparent;
  border: none;
  padding: 0.5rem;
  cursor: pointer;
  color: rgba(var(--color-foreground), 0.75);
}

.quantity-calculator-drawer__content {
  flex-grow: 1;
  padding: 1.5rem;
  overflow-y: auto;
}

.quantity-calculator__subheading {
  font-size: 0.875rem;
  font-weight: 600;
  margin-top: 0;
  margin-bottom: 1.5rem;
  color: rgba(var(--color-foreground), 0.75);
}

.quantity-calculator__unit-selector {
  margin-bottom: 2rem;
}

.quantity-calculator__unit-options {
  display: flex;
  gap: 1.5rem;
}

.quantity-calculator__unit-option {
  display: flex;
  align-items: center;
  cursor: pointer;
}

.calculator-unit-input {
  margin-right: 0.5rem;
}

.calculator-unit-label {
  font-size: 0.875rem;
  font-weight: 500;
}

.quantity-calculator__walls {
  margin-bottom: 1.5rem;
}

.quantity-calculator__wall {
  margin-bottom: 1.5rem;
  padding-bottom: 1.5rem;
  border-bottom: 1px solid rgba(var(--color-foreground), 0.1);
}

.quantity-calculator__wall-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
}

.quantity-calculator__wall-title {
  font-size: 0.875rem;
  font-weight: 500;
  color: rgba(var(--color-foreground), 0.75);
}

.quantity-calculator__wall-remove {
  background-color: transparent;
  border: none;
  padding: 0.25rem;
  color: rgba(var(--color-foreground), 0.5);
  cursor: pointer;
}

.quantity-calculator__dimension-fields {
  display: flex;
  gap: 1rem;
}

.quantity-calculator__dimension {
  flex: 1;
}

.quantity-calculator__dimension-label {
  display: block;
  margin-bottom: 0.5rem;
  font-size: 0.875rem;
  color: rgba(var(--color-foreground), 0.75);
}

.quantity-calculator__dimension-input {
  width: 100%;
  padding: 0.75rem 1rem;
  border: 1px solid rgba(var(--color-foreground), 0.1);
  border-radius: 2rem;
  background-color: transparent;
}

.quantity-calculator__actions {
  display: flex;
  flex-wrap: wrap;
  gap: 1rem;
  margin-bottom: 2rem;
}

.quantity-calculator__add-wall,
.quantity-calculator__reset {
  background-color: transparent;
  border: none;
  padding: 0;
  color: rgba(var(--color-foreground), 0.75);
  text-decoration: underline;
  cursor: pointer;
  font-size: 0.875rem;
}

.quantity-calculator__results {
  padding: 1.5rem;
  background-color: rgba(var(--color-foreground), 0.03);
  border-radius: 0.5rem;
}

.quantity-calculator__results-heading {
  font-size: 1.25rem;
  margin-top: 0;
  margin-bottom: 1.5rem;
}

.quantity-calculator__area,
.quantity-calculator__recommendation {
  margin-bottom: 1rem;
}

.quantity-calculator__area-label,
.quantity-calculator__recommendation-label {
  display: block;
  color: rgba(var(--color-foreground), 0.75);
  font-size: 0.875rem;
  margin-bottom: 0.25rem;
}

.quantity-calculator__area-value,
.quantity-calculator__recommendation-value {
  display: block;
  font-size: 1.25rem;
  font-weight: 600;
}

.quantity-calculator__add-to-cart {
  margin-top: 1.5rem;
  width: 100%;
  background-color: rgba(var(--color-button), 1);
  color: rgba(var(--color-button-text), 1);
  border: none;
  padding: 0.75rem 1.5rem;
  border-radius: 0.25rem;
  font-size: 1rem;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.2s ease-in-out;
}

.quantity-calculator__add-to-cart:hover {
  background-color: rgba(var(--color-button), 0.8);
}

@media screen and (max-width: 749px) {
  .quantity-calculator-drawer {
    max-width: 100%;
  }
}

.quantity-calculator__variant-suggestions {
  margin-top: 2rem;
  padding: 1.5rem;
  background-color: rgba(var(--color-foreground), 0.03);
  border-radius: 0.5rem;
}

.quantity-calculator__suggestions-heading {
  font-size: 1.25rem;
  margin-top: 0;
  margin-bottom: 1.5rem;
  font-weight: 600;
}

.quantity-calculator__suggestion-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.75rem 0;
  border-bottom: 1px solid rgba(var(--color-foreground), 0.1);
}

.quantity-calculator__suggestion-item:last-of-type {
  border-bottom: none;
  margin-bottom: 1rem;
}

.suggestion-item__details {
  display: flex;
  align-items: center;
}

.suggestion-item__count {
  font-weight: 600;
  margin-right: 0.5rem;
}

.suggestion-item__title {
  font-size: 0.95rem;
}

.suggestion-item__add-btn {
  background-color: transparent;
  color: rgba(var(--color-button), 1);
  border: 1px solid rgba(var(--color-button), 1);
  padding: 0.5rem 1rem;
  border-radius: 0.25rem;
  font-size: 0.875rem;
  cursor: pointer;
  transition: background-color 0.2s ease-in-out, color 0.2s ease-in-out;
}

.suggestion-item__add-btn:hover {
  background-color: rgba(var(--color-button), 1);
  color: rgba(var(--color-button-text), 1);
}

.quantity-calculator__add-all-btn {
  width: 100%;
  margin-top: 1.5rem;
  background-color: rgba(var(--color-button), 1);
  color: rgba(var(--color-button-text), 1);
  border: none;
  padding: 0.75rem 1.5rem;
  border-radius: 0.25rem;
  font-size: 1rem;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.2s ease-in-out;
}

.quantity-calculator__add-all-btn:hover {
  background-color: rgba(var(--color-button), 0.8);
}

.quantity-calculator__disclaimer {
  margin-top: 20px;
  padding-top: 15px;
  border-top: 1px solid rgba(0, 0, 0, 0.1);
  font-size: 14px;
  color: #666;
  line-height: 1.5;
  text-align: center;
}

.quantity-calculator__disclaimer p {
  margin: 0;
  text-align: left;
  font-size: small;
}

.quantity-calculator__disclaimer a {
  color: inherit;
  text-decoration: underline;
}

.quantity-calculator__disclaimer a:hover {
  color: #000;
}