{"settings_schema": {"colors": {"name": "Cores", "settings": {"background": {"label": "Plano de fundo"}, "background_gradient": {"label": "Gradiente de plano de fundo", "info": "O gradiente de plano de fundo substitui o plano de fundo sempre que possível."}, "text": {"label": "Texto"}, "button_background": {"label": "Plano de fundo do botão sólido"}, "button_label": {"label": "Etiqueta de botão sólido"}, "secondary_button_label": {"label": "Botão com contorno"}, "shadow": {"label": "Sombra"}}}, "typography": {"name": "Tipografia", "settings": {"type_header_font": {"label": "Fonte", "info": "A seleção de uma fonte diferente pode afetar a velocidade da loja. [Saiba mais sobre as fontes do sistema.](https://help.shopify.com/manual/online-store/os/store-speed/improving-speed#fonts)"}, "header__1": {"content": "<PERSON><PERSON><PERSON><PERSON>"}, "header__2": {"content": "Corpo"}, "type_body_font": {"label": "Fonte", "info": "A seleção de uma fonte diferente pode afetar a velocidade da loja. [Saiba mais sobre as fontes do sistema.](https://help.shopify.com/manual/online-store/os/store-speed/improving-speed#fonts)"}, "heading_scale": {"label": "<PERSON><PERSON><PERSON> <PERSON> da fonte"}, "body_scale": {"label": "<PERSON><PERSON><PERSON> <PERSON> da fonte"}}}, "social-media": {"name": "Redes sociais", "settings": {"social_twitter_link": {"label": "Twitter", "info": "https://twitter.com/shopify"}, "social_facebook_link": {"label": "Facebook", "info": "https://facebook.com/shopify"}, "social_pinterest_link": {"label": "Pinterest", "info": "https://pinterest.com/shopify"}, "social_instagram_link": {"label": "Instagram", "info": "http://instagram.com/shopify"}, "social_tiktok_link": {"label": "TikTok", "info": "https://tiktok.com/@shopify"}, "social_tumblr_link": {"label": "Tumblr", "info": "http://shopify.tumblr.com"}, "social_snapchat_link": {"label": "Snapchat", "info": "https://www.snapchat.com/add/shopify"}, "social_youtube_link": {"label": "YouTube", "info": "https://www.youtube.com/shopify"}, "social_vimeo_link": {"label": "Vimeo", "info": "https://vimeo.com/shopify"}, "header": {"content": "Contas de redes sociais"}}}, "currency_format": {"name": "Formato de moeda", "settings": {"content": "Códigos de moeda", "currency_code_enabled": {"label": "<PERSON><PERSON><PERSON> de moeda"}, "paragraph": "Os preços do carrinho e do checkout sempre mostram os códigos de moeda. Exemplo: 1,00 USD."}}, "layout": {"name": "Layout", "settings": {"page_width": {"label": "<PERSON><PERSON><PERSON> da página"}, "spacing_sections": {"label": "Espaço entre as seções do modelo"}, "header__grid": {"content": "Grade"}, "paragraph__grid": {"content": "Afeta áreas com várias colunas ou linhas."}, "spacing_grid_horizontal": {"label": "Espaço horizontal"}, "spacing_grid_vertical": {"label": "Espaço vertical"}}}, "search_input": {"name": "Comportamento da pesquisa", "settings": {"header": {"content": "Sugestões de pesquisa"}, "predictive_search_enabled": {"label": "Ativar as sugestões de pesquisa"}, "predictive_search_show_vendor": {"label": "Exibir o fornecedor do produto", "info": "<PERSON><PERSON><PERSON><PERSON> quando as sugestões de pesquisa estão habilitadas."}, "predictive_search_show_price": {"label": "Exibir o preço do produto", "info": "<PERSON><PERSON><PERSON><PERSON> quando as sugestões de pesquisa estão habilitadas."}}}, "global": {"settings": {"header__border": {"content": "<PERSON><PERSON>"}, "header__shadow": {"content": "Sombra"}, "blur": {"label": "Desfoque"}, "corner_radius": {"label": "Raio dos cantos"}, "horizontal_offset": {"label": "Compensação horizontal"}, "vertical_offset": {"label": "Compensação vertical"}, "thickness": {"label": "E<PERSON><PERSON><PERSON>"}, "opacity": {"label": "Opacidade"}, "image_padding": {"label": "Preenchimento de imagem"}, "text_alignment": {"options__1": {"label": "E<PERSON>rda"}, "options__2": {"label": "Centro"}, "options__3": {"label": "<PERSON><PERSON><PERSON>"}, "label": "Alinhamento do texto"}}}, "badges": {"name": "<PERSON><PERSON>", "settings": {"position": {"options__1": {"label": "Canto inferior esquerdo"}, "options__2": {"label": "Canto inferior direito"}, "options__3": {"label": "Canto superior esquerdo"}, "options__4": {"label": "Canto superior direito"}, "label": "Posição em cartões"}, "sale_badge_color_scheme": {"label": "Esquema de cores do selo de promoção"}, "sold_out_badge_color_scheme": {"label": "Esquema de cores do selo de esgotado"}}}, "buttons": {"name": "<PERSON><PERSON><PERSON><PERSON>"}, "variant_pills": {"name": "Pílulas de variantes", "paragraph": "Pílulas de variante são uma forma de apresentar suas variantes do produto. [<PERSON><PERSON> mais](https://help.shopify.com/en/manual/online-store/themes/theme-structure/page-types#variant-picker-block)"}, "inputs": {"name": "Entradas"}, "content_containers": {"name": "Contêineres de conteúdo"}, "popups": {"name": "Menus suspensos e pop-ups", "paragraph": "Afeta áreas como menus suspensos de navegação, janelas modais de pop-up e pop-ups de carrinho."}, "media": {"name": "Mí<PERSON>"}, "drawers": {"name": "<PERSON><PERSON>"}, "cart": {"name": "<PERSON><PERSON><PERSON>", "settings": {"cart_type": {"label": "<PERSON><PERSON><PERSON> <PERSON>", "drawer": {"label": "Deslizante"}, "page": {"label": "<PERSON><PERSON><PERSON><PERSON>"}, "notification": {"label": "Notificação pop-up"}}, "show_vendor": {"label": "<PERSON><PERSON><PERSON>"}, "show_cart_note": {"label": "Habilitar observação do carrinho"}, "cart_drawer": {"header": "<PERSON><PERSON><PERSON> de compras deslizante", "collection": {"label": "Coleção", "info": "Exibido quando o carrinho de compras deslizante está vazio."}}}}, "cards": {"name": "Cartões de produtos", "settings": {"style": {"options__1": {"label": "Padrão"}, "options__2": {"label": "Cartão"}, "label": "<PERSON><PERSON><PERSON>"}}}, "collection_cards": {"name": "Cartões de coleção", "settings": {"style": {"options__1": {"label": "Padrão"}, "options__2": {"label": "Cartão"}, "label": "<PERSON><PERSON><PERSON>"}}}, "blog_cards": {"name": "<PERSON><PERSON><PERSON><PERSON>", "settings": {"style": {"options__1": {"label": "Padrão"}, "options__2": {"label": "Cartão"}, "label": "<PERSON><PERSON><PERSON>"}}}, "logo": {"name": "Logo", "settings": {"logo_image": {"label": "Logo"}, "logo_width": {"label": "Largura do logo no desktop", "info": "A largura do logo é otimizada automaticamente para dispositivos móveis."}, "favicon": {"label": "<PERSON><PERSON> favi<PERSON>", "info": "Será reduzida para 32 x 32 pixels"}}}, "brand_information": {"name": "Informações da marca", "settings": {"brand_headline": {"label": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "brand_description": {"label": "Descrição"}, "brand_image": {"label": "Imagem"}, "brand_image_width": {"label": "<PERSON><PERSON><PERSON> da <PERSON>"}, "paragraph": {"content": "Adicione uma descrição da marca ao rodapé da loja."}}}, "animations": {"name": "Animações", "settings": {"animations_reveal_on_scroll": {"label": "Revelar seções durante a rolagem"}, "animations_hover_elements": {"options__1": {"label": "<PERSON><PERSON><PERSON>"}, "options__2": {"label": "Elevação vertical"}, "label": "Efeito ao passar o mouse", "info": "Afeta cartões e botões.", "options__3": {"label": "Elevação 3D"}}}}}, "sections": {"all": {"padding": {"section_padding_heading": "Preenchimento da seção", "padding_top": "Preenchimento superior", "padding_bottom": "Preenchimento inferior"}, "spacing": "Espaçamento", "colors": {"label": "Esquema de cores", "has_cards_info": "Atualize as configurações do tema para alterar o esquema de cores do cartão."}, "heading_size": {"label": "Tamanho do título", "options__1": {"label": "Pequeno"}, "options__2": {"label": "Médio"}, "options__3": {"label": "Grande"}, "options__4": {"label": "Extra grande"}}, "image_shape": {"options__1": {"label": "Padrão"}, "options__2": {"label": "Arco"}, "options__3": {"label": "Bol<PERSON>"}, "options__4": {"label": "Chevron para esquerda"}, "options__5": {"label": "Chevron para direita"}, "options__6": {"label": "Diamante"}, "options__7": {"label": "Paralelogramo"}, "options__8": {"label": "<PERSON><PERSON><PERSON><PERSON>"}, "label": "Formato da imagem", "info": "Cartões estilo-padrão não têm bordas quando uma forma de imagem está ativa."}, "animation": {"content": "Animações", "image_behavior": {"options__1": {"label": "<PERSON><PERSON><PERSON><PERSON>"}, "options__2": {"label": "Movimentação do ambiente"}, "label": "Comportamento da imagem", "options__3": {"label": "Posição fixa do plano de fundo"}, "options__4": {"label": "Aumentar o zoom na rolagem"}}}}, "announcement-bar": {"name": "Barra de avisos", "blocks": {"announcement": {"name": "Comunicado", "settings": {"text": {"label": "Texto"}, "text_alignment": {"label": "Alinhamento do texto", "options__1": {"label": "E<PERSON>rda"}, "options__2": {"label": "Centro"}, "options__3": {"label": "<PERSON><PERSON><PERSON>"}}, "link": {"label": "Link"}}}}, "settings": {"auto_rotate": {"label": "Girar automaticamente os comunicados"}, "change_slides_speed": {"label": "Mudar a cada"}, "header__1": {"content": "Ícones de redes sociais", "info": "Para exibir suas contas em redes sociais, crie links nas [configurações do tema](/editor?context=theme&category=social%20media)."}, "header__2": {"content": "Comunicados"}, "show_social": {"label": "Mostrar ícones no desktop"}, "header__3": {"content": "Se<PERSON>or de país/região", "info": "Adicione um país/uma região às [configurações de mercado.](/admin/settings/markets)"}, "enable_country_selector": {"label": "Habilitar seletor de país/região"}, "header__4": {"content": "Se<PERSON>or de idioma", "info": "Adicione um idioma às [configurações de idioma.](/admin/settings/languages)"}, "enable_language_selector": {"label": "Habilitar seletor de idioma"}}, "presets": {"name": "Barra de comunicados"}}, "collage": {"name": "Colagem", "settings": {"heading": {"label": "<PERSON><PERSON><PERSON><PERSON>"}, "desktop_layout": {"label": "Layout para desktop", "options__1": {"label": "Bloco grande esquerdo"}, "options__2": {"label": "Bloco grande direito"}}, "mobile_layout": {"label": "Layout para dispositivo móvel", "options__1": {"label": "Colagem"}, "options__2": {"label": "Coluna"}}, "card_styles": {"label": "Estilo do cartão", "info": "É possível atualizar os estilos de cartões de produto, coleção e blog nas configurações do tema.", "options__1": {"label": "Usar estilos de cartão individuais"}, "options__2": {"label": "Definir o estilo de todos como cartões de produto"}}}, "blocks": {"image": {"name": "Imagem", "settings": {"image": {"label": "Imagem"}}}, "product": {"name": "Produ<PERSON>", "settings": {"product": {"label": "Produ<PERSON>"}, "secondary_background": {"label": "Exibir plano de fundo secundário"}, "second_image": {"label": "Exibir segunda imagem ao passar o cursor"}}}, "collection": {"name": "Coleção", "settings": {"collection": {"label": "Coleção"}}}, "video": {"name": "Vídeo", "settings": {"cover_image": {"label": "<PERSON><PERSON> de capa"}, "video_url": {"label": "URL", "info": "Os vídeos serão reproduzidos em uma janela pop-up se a seção tiver outros blocos.", "placeholder": "Usar um URL do YouTube ou do Vimeo"}, "description": {"label": "Texto alternativo do vídeo", "info": "Descreva o vídeo para clientes que usam leitores de tela. [<PERSON><PERSON> ma<PERSON>](https://help.shopify.com/manual/online-store/themes/theme-structure/theme-features#video-block)"}}}}, "presets": {"name": "Colagem"}}, "collection-list": {"name": "Lista de coleções", "settings": {"title": {"label": "<PERSON><PERSON><PERSON><PERSON>"}, "image_ratio": {"label": "Proporção da imagem", "options__1": {"label": "Ada<PERSON>r à imagem"}, "options__2": {"label": "Retrato"}, "options__3": {"label": "Quadrada"}, "info": "<PERSON>e as coleções para adicionar imagens. [<PERSON><PERSON> ma<PERSON>](https://help.shopify.com/manual/products/collections)"}, "swipe_on_mobile": {"label": "Habilitar gesto de deslizar em dispositivos móveis"}, "show_view_all": {"label": "Habilitar o botão \"Ver tudo\" se a lista incluir mais coleções que as mostradas"}, "columns_desktop": {"label": "Número de colunas no desktop"}, "header_mobile": {"content": "Layout para dispositivos móveis"}, "columns_mobile": {"label": "Número de colunas em dispositivos móveis", "options__1": {"label": "1 coluna"}, "options__2": {"label": "2 colunas"}}}, "blocks": {"featured_collection": {"name": "Coleção", "settings": {"collection": {"label": "Coleção"}}}}, "presets": {"name": "Lista de coleções"}}, "contact-form": {"name": "Formulário de contato", "presets": {"name": "Formulário de contato"}}, "custom-liquid": {"name": "Liquid personalizado", "settings": {"custom_liquid": {"label": "Código Liquid", "info": "Adicione snippets de app ou outros códigos do Liquid para criar personalizações avançadas. [Saiba mais](https://shopify.dev/docs/api/liquid)"}}, "presets": {"name": "Liquid personalizado"}}, "featured-blog": {"name": "Posts do blog", "settings": {"heading": {"label": "<PERSON><PERSON><PERSON><PERSON>"}, "blog": {"label": "Blog"}, "post_limit": {"label": "Número de <PERSON> do blog para mostrar"}, "show_view_all": {"label": "Habilitar o botão \"Ver tudo\" se o blog tiver mais posts que os mostrados"}, "show_image": {"info": "Use uma imagem com proporção 3:2 para alcançar os melhores resultados. [<PERSON><PERSON> ma<PERSON>](https://help.shopify.com/manual/shopify-admin/productivity-tools/image-editor#understanding-image-aspect-ratio)", "label": "<PERSON><PERSON><PERSON> imagem em destaque"}, "show_date": {"label": "Exibir data"}, "show_author": {"label": "<PERSON><PERSON><PERSON> autor"}, "columns_desktop": {"label": "Número de colunas no desktop"}}, "presets": {"name": "Posts do blog"}}, "featured-collection": {"name": "Coleção em destaque", "settings": {"title": {"label": "<PERSON><PERSON><PERSON><PERSON>"}, "collection": {"label": "Coleção"}, "products_to_show": {"label": "Máximo de produtos a serem mostrados"}, "show_view_all": {"label": "Habilitar a opção \"Ver tudo\" se a coleção tiver mais produtos que os mostrados"}, "header": {"content": "Cartão de produto"}, "image_ratio": {"label": "Proporção da imagem", "options__1": {"label": "Ada<PERSON>r à imagem"}, "options__2": {"label": "Retrato"}, "options__3": {"label": "Quadrada"}}, "show_secondary_image": {"label": "Exibir segunda imagem ao passar o cursor"}, "show_vendor": {"label": "<PERSON><PERSON><PERSON>"}, "show_rating": {"label": "Exibir avaliações do produto", "info": "Para exibir uma avaliação, adicione um app com essa funcionalidade específica. [<PERSON><PERSON> mais](https://help.shopify.com/manual/online-store/themes/theme-structure/theme-features#featured-collection-show-product-rating)"}, "enable_quick_buy": {"label": "Habilitar botão para adicionar rapidamente", "info": "Ideal para o tipo de carrinho deslizante ou pop-up."}, "columns_desktop": {"label": "Número de colunas no desktop"}, "description": {"label": "Descrição"}, "show_description": {"label": "Mostrar a descrição da coleção do admin"}, "description_style": {"label": "Estilo da descrição", "options__1": {"label": "Corpo"}, "options__2": {"label": "<PERSON>a"}, "options__3": {"label": "Letras maiúsculas"}}, "view_all_style": {"label": "<PERSON><PERSON><PERSON> \"Ver tudo\"", "options__1": {"label": "Link"}, "options__2": {"label": "Botão com contorno"}, "options__3": {"label": "Botão sólido"}}, "enable_desktop_slider": {"label": "Habilitar carrossel no desktop"}, "full_width": {"label": "Deixar os produtos na largura total"}, "header_mobile": {"content": "Layout para dispositivos móveis"}, "columns_mobile": {"label": "Número de colunas em dispositivos móveis", "options__1": {"label": "1 coluna"}, "options__2": {"label": "2 colunas"}}, "swipe_on_mobile": {"label": "Habilitar gesto de deslizar em dispositivos móveis"}}, "presets": {"name": "Coleção em destaque"}}, "footer": {"name": "Rodapé", "blocks": {"link_list": {"name": "<PERSON><PERSON>", "settings": {"heading": {"label": "<PERSON><PERSON><PERSON><PERSON>"}, "menu": {"label": "<PERSON><PERSON>", "info": "Mostra somente itens de menu de nível superior."}}}, "text": {"name": "Texto", "settings": {"heading": {"label": "<PERSON><PERSON><PERSON><PERSON>"}, "subtext": {"label": "Subtexto"}}}, "brand_information": {"name": "Informações da marca", "settings": {"paragraph": {"content": "Este bloco mostrará s informações da marca. [Editar informações da marca.](/editor?context=theme&category=brand%20information)"}, "header__1": {"content": "Ícones de redes sociais"}, "show_social": {"label": "<PERSON><PERSON>r <PERSON> de redes sociais", "info": "Para exibir suas contas em redes sociais, crie links nas [configurações do tema](/editor?context=theme&category=social%20media)."}}}}, "settings": {"newsletter_enable": {"label": "Exibir assinante de e-mail"}, "newsletter_heading": {"label": "<PERSON><PERSON><PERSON><PERSON>"}, "header__1": {"content": "Assinante de e-mail", "info": "Assinantes adicionados automaticamente à lista de clientes que \"aceitam marketing\". [Sai<PERSON> ma<PERSON>](https://help.shopify.com/manual/customers/manage-customers)"}, "header__2": {"content": "Ícones de redes sociais", "info": "Para exibir suas contas em redes sociais, crie links nas [configurações do tema](/editor?context=theme&category=social%20media)."}, "show_social": {"label": "<PERSON><PERSON>r <PERSON> de redes sociais"}, "header__3": {"content": "Se<PERSON>or de país/região"}, "header__4": {"info": "Adicione um país/uma região às [configurações de mercado.](/admin/settings/markets)"}, "enable_country_selector": {"label": "Habilitar seletor de país/região"}, "header__5": {"content": "Se<PERSON>or de idioma"}, "header__6": {"info": "Adicione um idioma em [configurações de idioma.](/admin/settings/languages)"}, "enable_language_selector": {"label": "Habilitar seletor de idioma"}, "header__7": {"content": "Formas de pagamento"}, "payment_enable": {"label": "<PERSON><PERSON><PERSON>mento"}, "margin_top": {"label": "Margem superior"}, "header__8": {"content": "Links para políticas", "info": "Para adicionar as pol<PERSON><PERSON><PERSON> da loja, acesse [policy settings](/admin/settings/legal)."}, "show_policy": {"label": "Mostrar links para políticas"}, "header__9": {"content": "Se<PERSON>ir no <PERSON>", "info": "Para permitir que os clientes sigam a loja no app do Shop a partir de sua vitrine, o Shop Pay precisa estar habilitado. [<PERSON><PERSON> mais](https://help.shopify.com/manual/online-store/themes/customizing-themes/follow-on-shop)"}, "enable_follow_on_shop": {"label": "Habilitar \"Seguir no Shop\""}}}, "header": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "settings": {"logo_position": {"label": "Posição do logo no desktop", "options__1": {"label": "Centralizado à esquerda"}, "options__2": {"label": "Canto superior esquerdo"}, "options__3": {"label": "Centralizado na parte superior"}, "options__4": {"label": "Centralizado"}}, "menu": {"label": "<PERSON><PERSON>"}, "show_line_separator": {"label": "<PERSON><PERSON><PERSON> linha separadora"}, "margin_bottom": {"label": "Margem inferior"}, "menu_type_desktop": {"label": "Tipo de menu para desktop", "info": "O tipo de menu é otimizado automaticamente para dispositivos móveis.", "options__1": {"label": "<PERSON>u suspenso"}, "options__2": {"label": "Megamenu"}, "options__3": {"label": "Deslizante"}}, "mobile_layout": {"content": "Layout para dispositivo móvel"}, "mobile_logo_position": {"label": "Posição do logo em dispositivo móvel", "options__1": {"label": "Centro"}, "options__2": {"label": "E<PERSON>rda"}}, "logo_help": {"content": "Edite seu logo nas [configurações do tema](/editor?context=theme&category=logo)."}, "sticky_header_type": {"label": "Cabeçalho fixo", "options__1": {"label": "<PERSON><PERSON><PERSON>"}, "options__2": {"label": "Na rolagem da página para cima"}, "options__3": {"label": "Sempre"}, "options__4": {"label": "Se<PERSON>re, reduzir o tamanho do logo"}}, "header__3": {"content": "Se<PERSON>or de país/região"}, "header__4": {"info": "Adicione um país/uma região às [configurações de mercado.](/admin/settings/markets)"}, "enable_country_selector": {"label": "Habilitar seletor de país/região"}, "header__5": {"content": "Se<PERSON>or de idioma"}, "header__6": {"info": "Adicione um idioma às [configurações de idioma.](/admin/settings/languages)"}, "enable_language_selector": {"label": "Habilitar seletor de idioma"}, "header__1": {"content": "Cor"}, "menu_color_scheme": {"label": "Esquema de cores do menu"}}}, "image-banner": {"name": "Banner de imagem", "settings": {"image": {"label": "Primeira imagem"}, "image_2": {"label": "Segunda imagem"}, "stack_images_on_mobile": {"label": "Empilhar imagens em dispositivos móveis"}, "show_text_box": {"label": "<PERSON><PERSON><PERSON> con<PERSON> no desktop"}, "image_overlay_opacity": {"label": "Opacidade de sobreposição de imagem"}, "show_text_below": {"label": "<PERSON><PERSON><PERSON> contêiner em dispositivos móveis"}, "image_height": {"label": "Altura do banner", "options__1": {"label": "Adaptar à primeira imagem"}, "options__2": {"label": "Pequeno"}, "options__3": {"label": "Médio"}, "info": "Use uma imagem com proporção 3:2 para alcançar os melhores resultados. [<PERSON><PERSON> ma<PERSON>](https://help.shopify.com/manual/shopify-admin/productivity-tools/image-editor#understanding-image-aspect-ratio)", "options__4": {"label": "Grande"}}, "desktop_content_position": {"options__1": {"label": "Canto superior esquerdo"}, "options__2": {"label": "Centralizado na parte superior"}, "options__3": {"label": "Canto superior direito"}, "options__4": {"label": "Centralizado à esquerda"}, "options__5": {"label": "Centralizado"}, "options__6": {"label": "Centralizado à direita"}, "options__7": {"label": "Canto inferior esquerdo"}, "options__8": {"label": "Centralizado na parte inferior"}, "options__9": {"label": "Canto inferior direito"}, "label": "Posição do conteúdo no desktop"}, "desktop_content_alignment": {"options__1": {"label": "E<PERSON>rda"}, "options__2": {"label": "Centro"}, "options__3": {"label": "<PERSON><PERSON><PERSON>"}, "label": "Alinhamento de conteúdo no desktop"}, "mobile_content_alignment": {"options__1": {"label": "E<PERSON>rda"}, "options__2": {"label": "Centro"}, "options__3": {"label": "<PERSON><PERSON><PERSON>"}, "label": "Alinhamento de conteúdo em dispositivos móveis"}, "mobile": {"content": "Layout em dispositivos móveis"}}, "blocks": {"heading": {"name": "<PERSON><PERSON><PERSON><PERSON>", "settings": {"heading": {"label": "<PERSON><PERSON><PERSON><PERSON>"}}}, "text": {"name": "Texto", "settings": {"text": {"label": "Descrição"}, "text_style": {"options__1": {"label": "Corpo"}, "options__2": {"label": "<PERSON>a"}, "options__3": {"label": "Letras maiúsculas"}, "label": "Estilo de texto"}}}, "buttons": {"name": "<PERSON><PERSON><PERSON><PERSON>", "settings": {"button_label_1": {"label": "Primeira etiqueta de botão", "info": "Deixe a etiqueta em branco para ocultar o botão."}, "button_link_1": {"label": "Primeiro link de botão"}, "button_style_secondary_1": {"label": "Usar estilo de botão com contorno"}, "button_label_2": {"label": "Segunda etiqueta de botão", "info": "Deixe a etiqueta em branco para ocultar o botão."}, "button_link_2": {"label": "Segundo link de botão"}, "button_style_secondary_2": {"label": "Usar estilo de botão com contorno"}}}}, "presets": {"name": "Banner de imagem"}}, "image-with-text": {"name": "Imagem com texto", "settings": {"image": {"label": "Imagem"}, "height": {"options__1": {"label": "Ada<PERSON>r à imagem"}, "options__2": {"label": "Pequeno"}, "options__3": {"label": "Médio"}, "label": "<PERSON><PERSON> da imagem", "options__4": {"label": "Grande"}}, "layout": {"options__1": {"label": "<PERSON>m primeiro"}, "options__2": {"label": "Segunda imagem"}, "label": "Posicionamento da imagem no desktop", "info": "O layout padrão para dispositivos móveis coloca a imagem primeiro."}, "desktop_image_width": {"options__1": {"label": "Pequena"}, "options__2": {"label": "Média"}, "options__3": {"label": "Grande"}, "label": "<PERSON><PERSON><PERSON> da imagem no desktop", "info": "A imagem é otimizada automaticamente em dispositivos móveis."}, "desktop_content_alignment": {"options__1": {"label": "E<PERSON>rda"}, "options__2": {"label": "Centro"}, "options__3": {"label": "<PERSON><PERSON><PERSON>"}, "label": "Alinhamento de conteúdo no desktop"}, "desktop_content_position": {"options__1": {"label": "Parte superior"}, "options__2": {"label": "<PERSON><PERSON>"}, "options__3": {"label": "Parte inferior"}, "label": "Posição do conteúdo no desktop"}, "content_layout": {"options__1": {"label": "Sem sobreposição"}, "options__2": {"label": "Com sobreposição"}, "label": "Layout do conteúdo"}, "mobile_content_alignment": {"options__1": {"label": "E<PERSON>rda"}, "options__2": {"label": "Centro"}, "options__3": {"label": "<PERSON><PERSON><PERSON>"}, "label": "Alinhamento de conteúdo em dispositivos móveis"}}, "blocks": {"heading": {"name": "<PERSON><PERSON><PERSON><PERSON>", "settings": {"heading": {"label": "<PERSON><PERSON><PERSON><PERSON>"}}}, "text": {"name": "Texto", "settings": {"text": {"label": "<PERSON><PERSON><PERSON><PERSON>"}, "text_style": {"label": "Estilo de texto", "options__1": {"label": "Corpo"}, "options__2": {"label": "<PERSON>a"}}}}, "button": {"name": "Botão", "settings": {"button_label": {"label": "Etiqueta de botão", "info": "Deixe a etiqueta em branco para ocultar o botão."}, "button_link": {"label": "Link de botão"}, "outline_button": {"label": "Usar estilo de botão com contorno"}}}, "caption": {"name": "<PERSON>a", "settings": {"text": {"label": "Texto"}, "text_style": {"label": "Estilo de texto", "options__1": {"label": "<PERSON>a"}, "options__2": {"label": "Letras maiúsculas"}}, "caption_size": {"label": "Tamanho do texto", "options__1": {"label": "Pequeno"}, "options__2": {"label": "Médio"}, "options__3": {"label": "Grande"}}}}}, "presets": {"name": "Imagem com texto"}}, "main-article": {"name": "Post do blog", "blocks": {"featured_image": {"name": "Imagem em destaque", "settings": {"image_height": {"label": "Altura da imagem em destaque", "options__1": {"label": "Ada<PERSON>r à imagem"}, "options__2": {"label": "Pequena"}, "options__3": {"label": "Média"}, "options__4": {"label": "Grande"}, "info": "Use uma imagem com proporção 16:9 para ter os melhores resultados. [<PERSON><PERSON> ma<PERSON>](https://help.shopify.com/manual/shopify-admin/productivity-tools/image-editor#understanding-image-aspect-ratio)"}}}, "title": {"name": "<PERSON><PERSON><PERSON><PERSON>", "settings": {"blog_show_date": {"label": "Exibir data"}, "blog_show_author": {"label": "<PERSON><PERSON><PERSON> autor"}}}, "content": {"name": "<PERSON><PERSON><PERSON><PERSON>"}, "share": {"name": "Compartilhar", "settings": {"featured_image_info": {"content": "Se você incluir um link em publicações nas redes sociais, a imagem em destaque da página será exibida como na pré-visualização. [<PERSON><PERSON> ma<PERSON>](https://help.shopify.com/manual/online-store/images/showing-social-media-thumbnail-images)."}, "title_info": {"content": "Um título e uma descrição da loja estão incluídos na prévia da imagem. [<PERSON><PERSON> mais](https://help.shopify.com/manual/promoting-marketing/seo/adding-keywords#set-a-title-and-description-for-your-online-store)."}, "text": {"label": "Texto"}}}}}, "main-blog": {"name": "Posts do blog", "settings": {"header": {"content": "Cartão de post do blog"}, "show_image": {"label": "<PERSON><PERSON><PERSON> imagem em destaque"}, "paragraph": {"content": "<PERSON>e os posts do blog para alterar os resumos. [<PERSON><PERSON> ma<PERSON>](https://help.shopify.com/manual/online-store/blogs/writing-blogs#display-an-excerpt-from-a-blog-post)"}, "show_date": {"label": "Exibir data"}, "show_author": {"label": "<PERSON><PERSON><PERSON> autor"}, "layout": {"label": "Layout para desktop", "options__1": {"label": "Grade"}, "options__2": {"label": "Colagem"}, "info": "As postagens aparecem empilhadas em dispositivos móveis."}, "image_height": {"label": "Altura da imagem em destaque", "options__1": {"label": "Ada<PERSON>r à imagem"}, "options__2": {"label": "Pequena"}, "options__3": {"label": "Média"}, "options__4": {"label": "Grande"}, "info": "Use uma imagem com proporção 3:2 para alcançar os melhores resultados. [<PERSON><PERSON> ma<PERSON>](https://help.shopify.com/manual/shopify-admin/productivity-tools/image-editor#understanding-image-aspect-ratio)"}}}, "main-cart-footer": {"name": "Subtotal", "blocks": {"subtotal": {"name": "Preço subtotal"}, "buttons": {"name": "Botão de checkout"}}}, "main-cart-items": {"name": "<PERSON><PERSON>"}, "main-collection-banner": {"name": "Banner da coleção", "settings": {"paragraph": {"content": "Edite a coleção para adicionar imagens ou descrições. [<PERSON><PERSON> <PERSON>](https://help.shopify.com/manual/products/collections/collection-layout)"}, "show_collection_description": {"label": "Exibir a descrição da coleção"}, "show_collection_image": {"label": "<PERSON><PERSON><PERSON> da coleção", "info": "Use uma imagem com proporção 16:9 para ter os melhores resultados. [<PERSON><PERSON> ma<PERSON>](https://help.shopify.com/manual/shopify-admin/productivity-tools/image-editor#understanding-image-aspect-ratio)"}}}, "main-collection-product-grid": {"name": "Grade de produtos", "settings": {"products_per_page": {"label": "Produtos por página"}, "enable_filtering": {"label": "Habilitar filtragem", "info": "Personalize os filtros com o app Search & Discovery. [<PERSON><PERSON> mais](https://help.shopify.com/manual/online-store/search-and-discovery/filters)"}, "enable_sorting": {"label": "Habilitar organização"}, "image_ratio": {"label": "Proporção da imagem", "options__1": {"label": "Ada<PERSON>r à imagem"}, "options__2": {"label": "Retrato"}, "options__3": {"label": "Quadrada"}}, "show_secondary_image": {"label": "Exibir segunda imagem ao passar o cursor"}, "show_vendor": {"label": "<PERSON><PERSON><PERSON>"}, "header__1": {"content": "Filtragem e organização"}, "header__3": {"content": "Cartão de produto"}, "enable_tags": {"label": "Habilitar a filtragem", "info": "Personalize os filtros com o app Search & Discovery. [<PERSON><PERSON> mais](https://help.shopify.com/manual/online-store/search-and-discovery/filters)"}, "show_rating": {"label": "Exibir avaliações do produto", "info": "Para exibir uma avaliação, adicione um app com essa funcionalidade específica. [Sai<PERSON> mais](https://help.shopify.com/manual/online-store/themes/theme-structure/page-types#product-grid-section-settings)"}, "enable_quick_buy": {"label": "Habilitar botão para adicionar rapidamente", "info": "Ideal para o tipo de carrinho deslizante ou pop-up."}, "columns_desktop": {"label": "Número de colunas no desktop"}, "header_mobile": {"content": "Layout para dispositivos móveis"}, "columns_mobile": {"label": "Número de colunas em dispositivos móveis", "options__1": {"label": "1 coluna"}, "options__2": {"label": "2 colunas"}}, "filter_type": {"label": "Layout de filtro para desktop", "options__1": {"label": "Horizontal"}, "options__2": {"label": "Vertical"}, "options__3": {"label": "<PERSON><PERSON>"}, "info": "O menu deslizante é o layout-padrão para dispositivo móvel."}}}, "main-list-collections": {"name": "Página da lista de coleções", "settings": {"title": {"label": "<PERSON><PERSON><PERSON><PERSON>"}, "sort": {"label": "Ordenar coleções por:", "options__1": {"label": "Ordem alfabética, A–Z"}, "options__2": {"label": "Ordem alfabética, Z–A"}, "options__3": {"label": "<PERSON>, mais recente primeiro"}, "options__4": {"label": "<PERSON>, mais antiga primeiro"}, "options__5": {"label": "Contagem de produtos, alta para baixa"}, "options__6": {"label": "Contagem de produtos, baixa para alta"}}, "image_ratio": {"label": "Proporção da imagem", "options__1": {"label": "Ada<PERSON>r à imagem"}, "options__2": {"label": "Retrato"}, "options__3": {"label": "Quadrada"}, "info": "<PERSON>e as coleções para adicionar imagens. [<PERSON><PERSON> ma<PERSON>](https://help.shopify.com/manual/products/collections)"}, "columns_desktop": {"label": "Número de colunas no desktop"}, "header_mobile": {"content": "Layout em dispositivos móveis"}, "columns_mobile": {"label": "Número de colunas em dispositivos móveis", "options__1": {"label": "1 coluna"}, "options__2": {"label": "2 colunas"}}}}, "main-page": {"name": "<PERSON><PERSON><PERSON><PERSON>"}, "main-password-footer": {"name": "<PERSON><PERSON><PERSON>"}, "main-password-header": {"name": "Cabeçal<PERSON>", "settings": {"logo_header": {"content": "Logo"}, "logo_help": {"content": "Edite seu logo nas configurações do tema."}}}, "main-product": {"blocks": {"text": {"name": "Texto", "settings": {"text": {"label": "Texto"}, "text_style": {"label": "Estilo de texto", "options__1": {"label": "Corpo"}, "options__2": {"label": "<PERSON>a"}, "options__3": {"label": "Letras maiúsculas"}}}}, "title": {"name": "<PERSON><PERSON><PERSON><PERSON>"}, "price": {"name": "Preço"}, "quantity_selector": {"name": "Se<PERSON>or de quantidade"}, "variant_picker": {"name": "Se<PERSON>or de variante", "settings": {"picker_type": {"label": "<PERSON><PERSON><PERSON>", "options__1": {"label": "<PERSON>u suspenso"}, "options__2": {"label": "<PERSON><PERSON><PERSON><PERSON>"}}, "swatch_shape": {"label": "Amostra", "info": "Habilite [amostras](https://help.shopify.com/en/manual/online-store/themes/theme-structure/theme-settings#options-with-swatches) nas opções de produtos.", "options__1": {"label": "<PERSON><PERSON><PERSON><PERSON>"}, "options__2": {"label": "Quadrado"}, "options__3": {"label": "<PERSON><PERSON><PERSON><PERSON>"}}}}, "buy_buttons": {"name": "<PERSON><PERSON><PERSON><PERSON>", "settings": {"show_dynamic_checkout": {"label": "<PERSON>ibir bot<PERSON><PERSON> de checkout dinâmico", "info": "Cada cliente vê a forma de pagamento preferencial dentre as disponíveis na loja, como PayPal ou Apple Pay. [<PERSON><PERSON> mais](https://help.shopify.com/manual/using-themes/change-the-layout/dynamic-checkout)"}, "show_gift_card_recipient": {"label": "Mostrar formulário de informações do destinatário para cartões-presente", "info": "Permita que os compradores enviem cartões-presente com uma mensagem pessoal em uma data programada. [<PERSON><PERSON> mais](https://help.shopify.com/manual/online-store/themes/customizing-themes/add-gift-card-recipient-fields)"}}}, "pickup_availability": {"name": "Disponibilidade de retirada"}, "description": {"name": "Descrição"}, "share": {"name": "Compartilhar", "settings": {"featured_image_info": {"content": "Se você incluir um link em publicações nas redes sociais, a imagem em destaque da página será exibida como na pré-visualização. [<PERSON><PERSON> ma<PERSON>](https://help.shopify.com/manual/online-store/images/showing-social-media-thumbnail-images)."}, "title_info": {"content": "Um título e uma descrição da loja estão incluídos na prévia da imagem. [<PERSON><PERSON> mais](https://help.shopify.com/manual/promoting-marketing/seo/adding-keywords#set-a-title-and-description-for-your-online-store)."}, "text": {"label": "Texto"}}}, "collapsible_tab": {"name": "<PERSON><PERSON> re<PERSON>", "settings": {"heading": {"info": "Inclua um título que explique o conteúdo.", "label": "<PERSON><PERSON><PERSON><PERSON>"}, "content": {"label": "<PERSON><PERSON><PERSON><PERSON>"}, "page": {"label": "<PERSON><PERSON><PERSON><PERSON> da linha de página"}, "icon": {"options__1": {"label": "<PERSON><PERSON><PERSON>"}, "options__2": {"label": "Maçã"}, "options__3": {"label": "Banana"}, "options__4": {"label": "<PERSON><PERSON><PERSON>"}, "options__5": {"label": "Caixa"}, "options__6": {"label": "<PERSON><PERSON><PERSON>"}, "options__7": {"label": "Balão de chat"}, "options__8": {"label": "Marca de se<PERSON>ção"}, "options__9": {"label": "Pranchet<PERSON>"}, "options__10": {"label": "<PERSON><PERSON><PERSON><PERSON>"}, "options__11": {"label": "Sem lactose"}, "options__12": {"label": "Secador"}, "options__13": {"label": "<PERSON><PERSON><PERSON>"}, "options__14": {"label": "Fogo"}, "options__15": {"label": "<PERSON><PERSON>"}, "options__16": {"label": "Coração"}, "options__17": {"label": "<PERSON>rro"}, "options__18": {"label": "Fol<PERSON>"}, "options__19": {"label": "<PERSON><PERSON>"}, "options__20": {"label": "Relâmpago"}, "options__21": {"label": "<PERSON><PERSON>"}, "options__22": {"label": "Cadeado"}, "options__23": {"label": "Marcador de mapa"}, "options__24": {"label": "Sem nozes"}, "label": "Ícone", "options__25": {"label": "Calças"}, "options__26": {"label": "Pegada de pata"}, "options__27": {"label": "Pimenta"}, "options__28": {"label": "Perfume"}, "options__29": {"label": "Avião"}, "options__30": {"label": "Planta"}, "options__31": {"label": "Etiqueta de preço"}, "options__32": {"label": "Ponto de interrogação"}, "options__33": {"label": "Reciclar"}, "options__34": {"label": "Voltar"}, "options__35": {"label": "Régua"}, "options__36": {"label": "Travessa"}, "options__37": {"label": "<PERSON><PERSON>"}, "options__38": {"label": "Sapato"}, "options__39": {"label": "<PERSON><PERSON><PERSON><PERSON>"}, "options__40": {"label": "Floco de neve"}, "options__41": {"label": "Estrela"}, "options__42": {"label": "<PERSON><PERSON><PERSON><PERSON>"}, "options__43": {"label": "Caminhão"}, "options__44": {"label": "<PERSON><PERSON><PERSON>"}}}}, "popup": {"name": "Pop-up", "settings": {"link_label": {"label": "Etiqueta de link"}, "page": {"label": "<PERSON><PERSON><PERSON><PERSON>"}}}, "rating": {"name": "Avaliação do produto", "settings": {"paragraph": {"content": "Para exibir uma avaliação, adicione um app com essa funcionalidade específica. [Sai<PERSON> mais](https://help.shopify.com/manual/online-store/themes/theme-structure/page-types#product-rating-block)"}}}, "complementary_products": {"name": "<PERSON><PERSON><PERSON>", "settings": {"paragraph": {"content": "Para selecionar produtos complementares, adicione o app Search & Discovery. [<PERSON><PERSON> mais](https://help.shopify.com/manual/online-store/search-and-discovery/product-recommendations)"}, "heading": {"label": "<PERSON><PERSON><PERSON><PERSON>"}, "make_collapsible_row": {"label": "Exibir como linha recolhível"}, "icon": {"info": "Visível quando a linha recolhível é exibida."}, "product_list_limit": {"label": "Máximo de produtos a serem mostrados"}, "products_per_page": {"label": "Número de produtos por página"}, "pagination_style": {"label": "Estilo de paginação", "options": {"option_1": "Pontos", "option_2": "<PERSON><PERSON><PERSON>", "option_3": "Números"}}, "product_card": {"heading": "Cartão de produto"}, "image_ratio": {"label": "Proporção da imagem", "options": {"option_1": "Retrato", "option_2": "Quadrado"}}, "enable_quick_add": {"label": "Habilitar botão para adicionar rapidamente"}}}, "icon_with_text": {"name": "Ícone com texto", "settings": {"layout": {"label": "Layout", "options__1": {"label": "Horizontal"}, "options__2": {"label": "Vertical"}}, "content": {"label": "<PERSON><PERSON><PERSON><PERSON>", "info": "Escolha um ícone ou adicione uma imagem para cada coluna ou linha."}, "heading": {"info": "Deixe o título em branco para ocultar a coluna de ícone."}, "icon_1": {"label": "<PERSON><PERSON>"}, "image_1": {"label": "Primeira imagem"}, "heading_1": {"label": "<PERSON><PERSON> tí<PERSON>"}, "icon_2": {"label": "<PERSON><PERSON><PERSON>"}, "image_2": {"label": "Segunda imagem"}, "heading_2": {"label": "<PERSON><PERSON><PERSON>"}, "icon_3": {"label": "<PERSON><PERSON><PERSON>"}, "image_3": {"label": "Terceira imagem"}, "heading_3": {"label": "<PERSON><PERSON><PERSON>"}}}, "sku": {"name": "SKU", "settings": {"text_style": {"label": "Estilo de texto", "options__1": {"label": "Corpo"}, "options__2": {"label": "Subtítulo"}, "options__3": {"label": "Letras maiúsculas"}}}}, "inventory": {"name": "Status do estoque", "settings": {"text_style": {"label": "Estilo de texto", "options__1": {"label": "Corpo"}, "options__2": {"label": "<PERSON>a"}, "options__3": {"label": "Letras maiúsculas"}}, "inventory_threshold": {"label": "Limite de estoque baixo", "info": "Escolha 0 para sempre mostrar no estoque, se disponível."}, "show_inventory_quantity": {"label": "Mostrar contagem de estoque"}}}}, "settings": {"header": {"content": "Mí<PERSON>", "info": "Saiba mais sobre [tipos de mídia.](https://help.shopify.com/manual/products/product-media)"}, "enable_video_looping": {"label": "Habilitar loop de vídeo"}, "enable_sticky_info": {"label": "Habilitar conteúdo persistente no desktop"}, "hide_variants": {"label": "Ocultar a mídia de outras variantes após selecionar uma delas"}, "gallery_layout": {"label": "Layout para desktop", "options__1": {"label": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "options__2": {"label": "<PERSON><PERSON> colu<PERSON>"}, "options__3": {"label": "Miniaturas"}, "options__4": {"label": "Carrossel de miniaturas"}}, "media_size": {"label": "<PERSON><PERSON><PERSON> da mídia para desktop", "options__1": {"label": "Pequeno"}, "options__2": {"label": "Médio"}, "options__3": {"label": "Grande"}, "info": "A mídia é otimizada automaticamente para dispositivos móveis."}, "mobile_thumbnails": {"label": "Layout em dispositivos móveis", "options__1": {"label": "<PERSON><PERSON> colu<PERSON>"}, "options__2": {"label": "<PERSON><PERSON>r miniaturas"}, "options__3": {"label": "Ocultar miniaturas"}}, "media_position": {"label": "Posição da mídia no desktop", "info": "A posição é otimizada automaticamente para dispositivos móveis.", "options__1": {"label": "E<PERSON>rda"}, "options__2": {"label": "<PERSON><PERSON><PERSON>"}}, "image_zoom": {"label": "Zoom de imagem", "info": "Clique e passe o cursor para abrir a janela modal no celular.", "options__1": {"label": "<PERSON><PERSON><PERSON> janela modal"}, "options__2": {"label": "Clicar e passar o cursor"}, "options__3": {"label": "Sem zoom"}}, "constrain_to_viewport": {"label": "Ajustar a mídia à tela"}, "media_fit": {"label": "<PERSON><PERSON><PERSON> da mídia", "options__1": {"label": "Original"}, "options__2": {"label": "Preenchimento"}}}, "name": "Informações do produto"}, "main-search": {"name": "Resultados da pesquisa", "settings": {"image_ratio": {"label": "Proporção da imagem", "options__1": {"label": "Ada<PERSON>r à imagem"}, "options__2": {"label": "Retrato"}, "options__3": {"label": "Quadrada"}}, "show_secondary_image": {"label": "Exibir segunda imagem ao passar o cursor"}, "show_vendor": {"label": "<PERSON><PERSON><PERSON>"}, "header__1": {"content": "Cartão de produto"}, "header__2": {"content": "Cartão de blog", "info": "Os estilos de cartão do blog se aplicam também a cartões de página nos resultados de pesquisa. Para alterar esses estilos, atualize as configurações do tema."}, "article_show_date": {"label": "Exibir data"}, "article_show_author": {"label": "<PERSON><PERSON><PERSON> autor"}, "show_rating": {"label": "Exibir avaliações do produto", "info": "Para exibir uma avaliação, adicione um app com essa funcionalidade específica. [<PERSON><PERSON> ma<PERSON>](https://help.shopify.com/manual/online-store/themes/theme-structure/page-types#search-results-section-settings)"}, "columns_desktop": {"label": "Número de colunas no desktop"}, "header_mobile": {"content": "Layout para dispositivos móveis"}, "columns_mobile": {"label": "Número de colunas em dispositivos móveis", "options__1": {"label": "1 coluna"}, "options__2": {"label": "2 colunas"}}}}, "multicolumn": {"name": "Multicoluna", "settings": {"title": {"label": "<PERSON><PERSON><PERSON><PERSON>"}, "image_width": {"label": "<PERSON><PERSON><PERSON> da <PERSON>", "options__1": {"label": "Largura de um terço da coluna"}, "options__2": {"label": "Largura de metade da coluna"}, "options__3": {"label": "Largura total da coluna"}}, "image_ratio": {"label": "Proporção da imagem", "options__1": {"label": "Ada<PERSON>r à imagem"}, "options__2": {"label": "Retrato"}, "options__3": {"label": "Quadrada"}, "options__4": {"label": "<PERSON><PERSON><PERSON><PERSON>"}}, "column_alignment": {"label": "Alinham<PERSON><PERSON> da coluna", "options__1": {"label": "E<PERSON>rda"}, "options__2": {"label": "Centro"}}, "background_style": {"label": "Fundo secundário", "options__1": {"label": "<PERSON><PERSON><PERSON>"}, "options__2": {"label": "Exibir como plano de fundo da coluna"}}, "button_label": {"label": "Etiqueta de botão"}, "button_link": {"label": "Link de botão"}, "swipe_on_mobile": {"label": "Habilitar gesto de deslizar em dispositivos móveis"}, "columns_desktop": {"label": "Número de colunas no desktop"}, "header_mobile": {"content": "Layout para dispositivos móveis"}, "columns_mobile": {"label": "Número de colunas em dispositivos móveis", "options__1": {"label": "1 coluna"}, "options__2": {"label": "2 colunas"}}}, "blocks": {"column": {"name": "Coluna", "settings": {"image": {"label": "Imagem"}, "title": {"label": "<PERSON><PERSON><PERSON><PERSON>"}, "text": {"label": "Descrição"}, "link_label": {"label": "Etiqueta de link"}, "link": {"label": "Link"}}}}, "presets": {"name": "Multicoluna"}}, "newsletter": {"name": "Assinante de e-mail", "settings": {"full_width": {"label": "Definir seção com largura total"}, "paragraph": {"content": "Cada assinatura por e-mail cria uma conta de cliente. [<PERSON><PERSON> ma<PERSON>](https://help.shopify.com/manual/customers)"}}, "blocks": {"heading": {"name": "<PERSON><PERSON><PERSON><PERSON>", "settings": {"heading": {"label": "<PERSON><PERSON><PERSON><PERSON>"}}}, "paragraph": {"name": "Subtítulo", "settings": {"paragraph": {"label": "Descrição"}}}, "email_form": {"name": "Formulário de e-mail"}}, "presets": {"name": "Assinante de e-mail"}}, "page": {"name": "<PERSON><PERSON><PERSON><PERSON>", "settings": {"page": {"label": "<PERSON><PERSON><PERSON><PERSON>"}}, "presets": {"name": "<PERSON><PERSON><PERSON><PERSON>"}}, "rich-text": {"name": "Rich text", "settings": {"full_width": {"label": "Definir seção com largura total"}, "desktop_content_position": {"options__1": {"label": "E<PERSON>rda"}, "options__2": {"label": "Centro"}, "options__3": {"label": "<PERSON><PERSON><PERSON>"}, "label": "Posição do conteúdo no desktop", "info": "A posição é otimizada automaticamente para dispositivos móveis."}, "content_alignment": {"options__1": {"label": "E<PERSON>rda"}, "options__2": {"label": "Centro"}, "options__3": {"label": "<PERSON><PERSON><PERSON>"}, "label": "Alinhamento de conteúdo"}}, "blocks": {"heading": {"name": "<PERSON><PERSON><PERSON><PERSON>", "settings": {"heading": {"label": "<PERSON><PERSON><PERSON><PERSON>"}}}, "text": {"name": "Texto", "settings": {"text": {"label": "Descrição"}}}, "buttons": {"name": "<PERSON><PERSON><PERSON><PERSON>", "settings": {"button_label_1": {"label": "Primeira etiqueta de botão", "info": "Deixar a etiqueta em branco para ocultar o botão."}, "button_link_1": {"label": "Primeiro link de botão"}, "button_style_secondary_1": {"label": "Usar estilo de botão com contorno"}, "button_label_2": {"label": "Segunda etiqueta de botão", "info": "Deixar a etiqueta em branco para ocultar o botão."}, "button_link_2": {"label": "Segundo link de botão"}, "button_style_secondary_2": {"label": "Usar estilo de botão com contorno"}}}, "caption": {"name": "<PERSON>a", "settings": {"text": {"label": "Texto"}, "text_style": {"label": "Estilo de texto", "options__1": {"label": "<PERSON>a"}, "options__2": {"label": "Letras maiúsculas"}}, "caption_size": {"label": "Tamanho do texto", "options__1": {"label": "Pequeno"}, "options__2": {"label": "Médio"}, "options__3": {"label": "Grande"}}}}}, "presets": {"name": "Rich text"}}, "apps": {"name": "Apps", "settings": {"include_margins": {"label": "<PERSON><PERSON>ar margens da seção iguais ao tema"}}, "presets": {"name": "Apps"}}, "video": {"name": "Vídeo", "settings": {"heading": {"label": "<PERSON><PERSON><PERSON><PERSON>"}, "cover_image": {"label": "<PERSON><PERSON> de capa"}, "video_url": {"label": "URL", "info": "Usar um URL do YouTube ou do Vimeo"}, "description": {"label": "Texto alternativo do vídeo", "info": "Descreva o vídeo para clientes que usam leitores de tela. [<PERSON><PERSON> ma<PERSON>](https://help.shopify.com/manual/online-store/themes/theme-structure/theme-features#video)"}, "image_padding": {"label": "Adicionar preenchimento de imagem", "info": "Selecione o preenchimento se não quiser que as imagens sejam cortadas."}, "full_width": {"label": "Definir seção com largura total"}, "video": {"label": "Vídeo"}, "enable_video_looping": {"label": "Reproduzir vídeo em loop"}, "header__1": {"content": "Vídeo hospedado na Shopify"}, "header__2": {"content": "Ou incorporar vídeo de URL"}, "header__3": {"content": "<PERSON><PERSON><PERSON>"}, "paragraph": {"content": "Mostra quando nenhum vídeo hospedado na Shopify é selecionado."}}, "presets": {"name": "Vídeo"}}, "featured-product": {"name": "Produto em destaque", "blocks": {"text": {"name": "Texto", "settings": {"text": {"label": "Texto"}, "text_style": {"label": "Estilo de texto", "options__1": {"label": "Corpo"}, "options__2": {"label": "<PERSON>a"}, "options__3": {"label": "Letras maiúsculas"}}}}, "title": {"name": "<PERSON><PERSON><PERSON><PERSON>"}, "price": {"name": "Preço"}, "quantity_selector": {"name": "Se<PERSON>or de quantidade"}, "variant_picker": {"name": "Se<PERSON>or de variante", "settings": {"picker_type": {"label": "<PERSON><PERSON><PERSON>", "options__1": {"label": "<PERSON>u suspenso"}, "options__2": {"label": "<PERSON><PERSON><PERSON><PERSON>"}}, "swatch_shape": {"label": "Amostra", "info": "Habilite [amostras](https://help.shopify.com/en/manual/online-store/themes/theme-structure/theme-settings#options-with-swatches) nas opções de produtos.", "options__1": {"label": "<PERSON><PERSON><PERSON><PERSON>"}, "options__2": {"label": "Quadrado"}, "options__3": {"label": "<PERSON><PERSON><PERSON><PERSON>"}}}}, "buy_buttons": {"name": "<PERSON><PERSON><PERSON><PERSON>", "settings": {"show_dynamic_checkout": {"label": "<PERSON>ibir bot<PERSON><PERSON> de checkout dinâmico", "info": "Cada cliente vê a forma de pagamento preferencial dentre as disponíveis na loja, como PayPal ou Apple Pay. [<PERSON><PERSON> mais](https://help.shopify.com/manual/using-themes/change-the-layout/dynamic-checkout)"}}}, "description": {"name": "Descrição"}, "share": {"name": "Compartilhar", "settings": {"featured_image_info": {"content": "Se você incluir um link em publicações nas redes sociais, a imagem em destaque da página será exibida como na pré-visualização. [<PERSON><PERSON> ma<PERSON>](https://help.shopify.com/manual/online-store/images/showing-social-media-thumbnail-images)"}, "title_info": {"content": "O título e a descrição da loja estão incluídos na prévia da imagem. [<PERSON><PERSON> mais](https://help.shopify.com/manual/promoting-marketing/seo/adding-keywords#set-a-title-and-description-for-your-online-store)"}, "text": {"label": "Texto"}}}, "rating": {"name": "Avaliação do produto", "settings": {"paragraph": {"content": "Para exibir uma avaliação, adicione um app com essa funcionalidade específica. [<PERSON><PERSON> mais](https://help.shopify.com/manual/online-store/themes/theme-structure/theme-features#featured-product-rating)"}}}, "sku": {"name": "SKU", "settings": {"text_style": {"label": "Estilo de texto", "options__1": {"label": "Corpo"}, "options__2": {"label": "Subtítulo"}, "options__3": {"label": "Letras maiúsculas"}}}}}, "settings": {"product": {"label": "Produ<PERSON>"}, "secondary_background": {"label": "Exibir plano de fundo secundário"}, "header": {"content": "Mí<PERSON>", "info": "Saiba mais sobre os [tipos de mídia](https://help.shopify.com/manual/products/product-media)"}, "enable_video_looping": {"label": "Habilitar loop de vídeo"}, "hide_variants": {"label": "Ocultar mídia das variantes não selecionadas na área de trabalho"}, "media_position": {"label": "Posição da mídia no desktop", "info": "A posição é otimizada automaticamente para dispositivos móveis.", "options__1": {"label": "E<PERSON>rda"}, "options__2": {"label": "<PERSON><PERSON><PERSON>"}}}, "presets": {"name": "Produto em destaque"}}, "email-signup-banner": {"name": "Banner de assinante de e-mail", "settings": {"paragraph": {"content": "Cada assinatura por e-mail cria uma conta de cliente. [<PERSON><PERSON> ma<PERSON>](https://help.shopify.com/manual/customers)"}, "image": {"label": "Imagem de fundo"}, "show_background_image": {"label": "<PERSON><PERSON><PERSON> de fundo"}, "show_text_box": {"label": "<PERSON><PERSON><PERSON> con<PERSON> no desktop"}, "image_overlay_opacity": {"label": "Opacidade de sobreposição de imagem"}, "show_text_below": {"label": "<PERSON><PERSON><PERSON> con<PERSON><PERSON><PERSON> a<PERSON> da imagem em dispositivos móveis", "info": "Use uma imagem com proporção 16:9 para ter os melhores resultados. [<PERSON><PERSON> ma<PERSON>](https://help.shopify.com/manual/shopify-admin/productivity-tools/image-editor#understanding-image-aspect-ratio)"}, "image_height": {"label": "Altura do banner", "options__1": {"label": "Ada<PERSON>r à imagem"}, "options__2": {"label": "Pequena"}, "options__3": {"label": "Média"}, "options__4": {"label": "Grande"}, "info": "Use uma imagem com proporção 16:9 para ter os melhores resultados. [<PERSON><PERSON> ma<PERSON>](https://help.shopify.com/manual/shopify-admin/productivity-tools/image-editor#understanding-image-aspect-ratio)"}, "desktop_content_position": {"options__1": {"label": "Canto superior esquerdo"}, "options__2": {"label": "Centro da parte superior"}, "options__3": {"label": "Canto superior direito"}, "options__4": {"label": "Centro à esquerda"}, "options__5": {"label": "Centralizado"}, "options__6": {"label": "Centralizado à direita"}, "options__7": {"label": "Canto inferior esquerdo"}, "options__8": {"label": "Centralizado na parte inferior"}, "options__9": {"label": "Canto inferior direito"}, "label": "Posição do conteúdo no desktop"}, "desktop_content_alignment": {"options__1": {"label": "E<PERSON>rda"}, "options__2": {"label": "Centro"}, "options__3": {"label": "<PERSON><PERSON><PERSON>"}, "label": "Alinhamento de conteúdo no desktop"}, "header": {"content": "Layout para dispositivos móveis"}, "mobile_content_alignment": {"options__1": {"label": "E<PERSON>rda"}, "options__2": {"label": "Centro"}, "options__3": {"label": "<PERSON><PERSON><PERSON>"}, "label": "Alinhamento de conteúdo em dispositivos móveis"}, "color_scheme": {"info": "Vis<PERSON>vel quando o contêiner é exibido."}}, "blocks": {"heading": {"name": "<PERSON><PERSON><PERSON><PERSON>", "settings": {"heading": {"label": "<PERSON><PERSON><PERSON><PERSON>"}}}, "paragraph": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "settings": {"paragraph": {"label": "Descrição"}, "text_style": {"options__1": {"label": "Corpo"}, "options__2": {"label": "<PERSON>a"}, "label": "Estilo de texto"}}}, "email_form": {"name": "Formulário de e-mail"}}, "presets": {"name": "Banner de assinante de e-mail"}}, "slideshow": {"name": "Apresentação de slides", "settings": {"layout": {"label": "Layout", "options__1": {"label": "Largura total"}, "options__2": {"label": "Grade"}}, "slide_height": {"label": "Altura do slide", "options__1": {"label": "Adaptar à primeira imagem"}, "options__2": {"label": "Pequena"}, "options__3": {"label": "Média"}, "options__4": {"label": "Grande"}}, "slider_visual": {"label": "Estilo de paginação", "options__1": {"label": "<PERSON><PERSON><PERSON>"}, "options__2": {"label": "Pontos"}, "options__3": {"label": "Números"}}, "auto_rotate": {"label": "Rodar automaticamente os slides"}, "change_slides_speed": {"label": "<PERSON><PERSON> os slides a cada"}, "show_text_below": {"label": "<PERSON><PERSON><PERSON> con<PERSON><PERSON><PERSON> a<PERSON> das imagens em dispositivos móveis"}, "mobile": {"content": "Layout em dispositivos móveis"}, "accessibility": {"content": "Acessibilidade", "label": "Descrição da apresentação de slides", "info": "Descreva a apresentação de slides para clientes que usam leitores de tela."}}, "blocks": {"slide": {"name": "Slide", "settings": {"image": {"label": "Imagem"}, "heading": {"label": "<PERSON><PERSON><PERSON><PERSON>"}, "subheading": {"label": "Subtítulo"}, "button_label": {"label": "Etiqueta de botão", "info": "Deixe a etiqueta em branco para ocultar o botão."}, "link": {"label": "Link de botão"}, "secondary_style": {"label": "Usar estilo de botão com contorno"}, "box_align": {"label": "Posição do conteúdo no desktop", "options__1": {"label": "Canto superior esquerdo"}, "options__2": {"label": "Centralizado na parte superior"}, "options__3": {"label": "Canto superior direito"}, "options__4": {"label": "Centralizado à esquerda"}, "options__5": {"label": "Centralizado"}, "options__6": {"label": "Centralizado à direita"}, "options__7": {"label": "Canto inferior esquerdo"}, "options__8": {"label": "Centralizado na parte inferior"}, "options__9": {"label": "Canto inferior direito"}, "info": "A posição é otimizada automaticamente para dispositivos móveis."}, "show_text_box": {"label": "<PERSON><PERSON><PERSON> con<PERSON> no desktop"}, "text_alignment": {"label": "Alinhamento de conteúdo no desktop", "option_1": {"label": "E<PERSON>rda"}, "option_2": {"label": "Centro"}, "option_3": {"label": "<PERSON><PERSON><PERSON>"}}, "image_overlay_opacity": {"label": "Opacidade de sobreposição de imagem"}, "text_alignment_mobile": {"label": "Alinhamento de conteúdo em dispositivos móveis", "options__1": {"label": "E<PERSON>rda"}, "options__2": {"label": "Centro"}, "options__3": {"label": "<PERSON><PERSON><PERSON>"}}}}}, "presets": {"name": "Apresentação de slides"}}, "collapsible_content": {"name": "<PERSON><PERSON><PERSON><PERSON>", "settings": {"caption": {"label": "<PERSON>a"}, "heading": {"label": "<PERSON><PERSON><PERSON><PERSON>"}, "heading_alignment": {"label": "Alinhamento do título", "options__1": {"label": "E<PERSON>rda"}, "options__2": {"label": "Centro"}, "options__3": {"label": "<PERSON><PERSON><PERSON>"}}, "layout": {"label": "Layout", "options__1": {"label": "<PERSON><PERSON><PERSON> con<PERSON>"}, "options__2": {"label": "<PERSON><PERSON><PERSON><PERSON>"}, "options__3": {"label": "Contêiner de seção"}}, "container_color_scheme": {"label": "Esquema de cores do contêiner", "info": "Visível quando o layout está definido para contêiner de Linha ou de Seção."}, "open_first_collapsible_row": {"label": "Abrir primeira linha recolhível"}, "header": {"content": "Layout da imagem"}, "image": {"label": "Imagem"}, "image_ratio": {"label": "Proporção da imagem", "options__1": {"label": "Ada<PERSON>r à imagem"}, "options__2": {"label": "Pequeno"}, "options__3": {"label": "Grande"}}, "desktop_layout": {"label": "Layout para desktop", "options__1": {"label": "Primeira imagem"}, "options__2": {"label": "Segunda imagem"}, "info": "A imagem sempre aparece primeiro em dispositivos móveis."}}, "blocks": {"collapsible_row": {"name": "<PERSON><PERSON> re<PERSON>", "settings": {"heading": {"info": "Inclua um título que explique o conteúdo.", "label": "<PERSON><PERSON><PERSON><PERSON>"}, "row_content": {"label": "<PERSON><PERSON><PERSON><PERSON>"}, "page": {"label": "<PERSON><PERSON><PERSON><PERSON> da linha de página"}, "icon": {"label": "Ícone", "options__1": {"label": "<PERSON><PERSON><PERSON><PERSON>"}, "options__2": {"label": "Maçã"}, "options__3": {"label": "Banana"}, "options__4": {"label": "<PERSON><PERSON><PERSON>"}, "options__5": {"label": "Caixa"}, "options__6": {"label": "<PERSON><PERSON><PERSON>"}, "options__7": {"label": "Balão de chat"}, "options__8": {"label": "Marca de se<PERSON>ção"}, "options__9": {"label": "Pranchet<PERSON>"}, "options__10": {"label": "<PERSON><PERSON><PERSON><PERSON>"}, "options__11": {"label": "Sem lactose"}, "options__12": {"label": "Secador"}, "options__13": {"label": "<PERSON><PERSON><PERSON>"}, "options__14": {"label": "Fogo"}, "options__15": {"label": "<PERSON><PERSON>"}, "options__16": {"label": "Coração"}, "options__17": {"label": "<PERSON>rro"}, "options__18": {"label": "Fol<PERSON>"}, "options__19": {"label": "<PERSON><PERSON>"}, "options__20": {"label": "Relâmpago"}, "options__21": {"label": "<PERSON><PERSON>"}, "options__22": {"label": "Cadeado"}, "options__23": {"label": "Marcador de mapa"}, "options__24": {"label": "Sem nozes"}, "options__25": {"label": "Calças"}, "options__26": {"label": "Pegada de pata"}, "options__27": {"label": "Pimenta"}, "options__28": {"label": "Perfume"}, "options__29": {"label": "Avião"}, "options__30": {"label": "Planta"}, "options__31": {"label": "Etiqueta de preço"}, "options__32": {"label": "Ponto de interrogação"}, "options__33": {"label": "Reciclar"}, "options__34": {"label": "Voltar"}, "options__35": {"label": "Régua"}, "options__36": {"label": "Travessa"}, "options__37": {"label": "<PERSON><PERSON>"}, "options__38": {"label": "Sapato"}, "options__39": {"label": "<PERSON><PERSON><PERSON><PERSON>"}, "options__40": {"label": "Floco de neve"}, "options__41": {"label": "Estrela"}, "options__42": {"label": "<PERSON><PERSON><PERSON><PERSON>"}, "options__43": {"label": "Caminhão"}, "options__44": {"label": "<PERSON><PERSON><PERSON>"}}}}}, "presets": {"name": "<PERSON><PERSON><PERSON><PERSON>"}}, "main-account": {"name": "Conta"}, "main-activate-account": {"name": "Ativação de conta"}, "main-addresses": {"name": "Endereços"}, "main-login": {"name": "Fazer login"}, "main-order": {"name": "Pedido"}, "main-register": {"name": "Registro"}, "main-reset-password": {"name": "Redefinição de senha"}, "related-products": {"name": "Produtos relacionados", "settings": {"heading": {"label": "<PERSON><PERSON><PERSON><PERSON>"}, "products_to_show": {"label": "Máximo de produtos a serem mostrados"}, "columns_desktop": {"label": "Número de colunas no desktop"}, "paragraph__1": {"content": "As recomendações dinâmicas usam informações sobre pedidos e produtos para mudar e melhorar com o tempo. [<PERSON><PERSON> ma<PERSON>](https://help.shopify.com/themes/development/recommended-products)"}, "header__2": {"content": "Cartão de produto"}, "image_ratio": {"label": "Proporção da imagem", "options__1": {"label": "Ada<PERSON>r à imagem"}, "options__2": {"label": "Retrato"}, "options__3": {"label": "Square"}}, "show_secondary_image": {"label": "Exibir segunda imagem ao passar o cursor"}, "show_vendor": {"label": "<PERSON><PERSON><PERSON>"}, "show_rating": {"label": "Exibir avaliações do produto", "info": "Para exibir uma avaliação, adicione um app com essa funcionalidade específica. [Sai<PERSON> mais](https://help.shopify.com/manual/online-store/themes/theme-structure/page-types#product-recommendations-section-settings)"}, "header_mobile": {"content": "Layout em dispositivos móveis"}, "columns_mobile": {"label": "Número de colunas em dispositivos móveis", "options__1": {"label": "1 coluna"}, "options__2": {"label": "2 colunas"}}}}, "multirow": {"name": "<PERSON><PERSON><PERSON><PERSON> l<PERSON>", "settings": {"image": {"label": "Imagem"}, "image_height": {"options__1": {"label": "Ada<PERSON>r à imagem"}, "options__2": {"label": "Pequeno"}, "options__3": {"label": "Médio"}, "options__4": {"label": "Grande"}, "label": "<PERSON><PERSON> da imagem"}, "desktop_image_width": {"options__1": {"label": "Pequeno"}, "options__2": {"label": "Médio"}, "options__3": {"label": "Grande"}, "label": "<PERSON><PERSON><PERSON> da imagem no desktop", "info": "A imagem é otimizada automaticamente em dispositivos móveis."}, "heading_size": {"options__1": {"label": "Pequeno"}, "options__2": {"label": "Médio"}, "options__3": {"label": "Grande"}, "label": "Tamanho do título"}, "text_style": {"options__1": {"label": "Corpo"}, "options__2": {"label": "Subtítulo"}, "label": "Estilo de texto"}, "button_style": {"options__1": {"label": "Botão sólido"}, "options__2": {"label": "Botão com contorno"}, "label": "Estilo de b<PERSON>ão"}, "desktop_content_alignment": {"options__1": {"label": "E<PERSON>rda"}, "options__2": {"label": "Centro"}, "options__3": {"label": "<PERSON><PERSON><PERSON>"}, "label": "Alinhamento de conteúdo no desktop"}, "desktop_content_position": {"options__1": {"label": "Parte superior"}, "options__2": {"label": "<PERSON><PERSON>"}, "options__3": {"label": "Parte inferior"}, "label": "Posição do conteúdo no desktop", "info": "A posição é otimizada automaticamente para dispositivos móveis."}, "image_layout": {"options__1": {"label": "Alternar da esquerda"}, "options__2": {"label": "Alternar da direita"}, "options__3": {"label": "Alinhado à esquerda"}, "options__4": {"label": "Alinhado à direita"}, "label": "Posicionamento da imagem no desktop", "info": "O posicionamento é otimizado automaticamente para dispositivos móveis."}, "container_color_scheme": {"label": "Esquema de cores do contêiner"}, "mobile_content_alignment": {"options__1": {"label": "E<PERSON>rda"}, "options__2": {"label": "Centro"}, "options__3": {"label": "<PERSON><PERSON><PERSON>"}, "label": "Alinhamento de conteúdo em dispositivos móveis"}, "header_mobile": {"content": "Layout em dispositivos móveis"}}, "blocks": {"row": {"name": "<PERSON><PERSON>", "settings": {"image": {"label": "Imagem"}, "caption": {"label": "<PERSON>a"}, "heading": {"label": "<PERSON><PERSON><PERSON><PERSON>"}, "text": {"label": "Texto"}, "button_label": {"label": "Etiqueta de botão"}, "button_link": {"label": "Link de botão"}}}}, "presets": {"name": "<PERSON><PERSON><PERSON><PERSON> l<PERSON>"}}, "quick-order-list": {"name": "Lista de pedidos rápidos", "settings": {"show_image": {"label": "Mostrar imagens"}, "show_sku": {"label": "Mostrar SKUs"}}, "presets": {"name": "Lista de pedidos rápidos"}}}}