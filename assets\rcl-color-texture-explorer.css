:root {
  --accent-color: #d1b073;
  --background-color: #f8f8f8;
  --text-color: #222228;
}

.texture-explorer-section {
  padding: 40px 0;
  background-color: var(--background-color);
  color: var(--text-color);
}

.page-width {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 15px;
}

.section-header {
  margin-bottom: 30px;
  text-align: center;
}

.section-title {
 color: var(--title-color, #d1b073);
 margin-bottom: 10px;
}

.section-description p {
  margin: 0;
  line-height: 1.6;
}

.section-description.rte {
  max-width: 800px;
  margin-left: auto;
  margin-right: auto;
}

.texture-explorer-container {
  display: flex;
  gap: 30px;
}

.texture-sidebar {
  width: 200px;
  flex-shrink: 0;
  display: flex;
  flex-direction: column;
}

.category-selector {
  margin-bottom: 15px;
}

.category-dropdown {
  width: 100%;
  padding: 10px;
  border: 1px solid #ddd;
  border-radius: 4px;
  background-color: #fff;
  font-size: 0.9rem;
  cursor: pointer;
  appearance: none;
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='12' height='12' fill='%23333'%3E%3Cpath d='M6 9l-6-6h12z'/%3E%3C/svg%3E");
  background-repeat: no-repeat;
  background-position: right 10px center;
  padding-right: 30px;
}

.sidebar-heading,
.samples-heading {
  margin-bottom: 15px;
}

.sidebar-title,
.samples-title {
  font-weight: 600;
  margin-bottom: 5px;
  color: var(--text-color, #222228);
}

.sidebar-description,
.samples-description {
  color: rgba(var(--text-color-rgb, 34, 34, 40), 0.8);
  line-height: 1.4;
  margin: 0;
}

.sample-images-list {
  flex-grow: 1;
  overflow-y: auto;
  border: 1px solid #eee;
  border-radius: 4px;
  padding: 10px;
  background-color: #fff;
  max-height: 90vh;
}
.sample-images-list p {
  text-align: center;
  color: #777;
  font-size: 0.9em;
  padding: 10px;
}


.sample-list-helper {
  background-color: rgba(var(--accent-color-rgb, 209, 176, 115), 0.1);
  padding: 8px 10px;
  border-radius: 4px;
  margin-bottom: 12px;
  border-left: 3px solid var(--accent-color, #d1b073);
}

.sample-list-helper p {
  margin: 0;
  font-size: 0.9rem;
  color: var(--text-color, #222228);
  line-height: 1.4;
}

.sample-image-item {
  margin-bottom: 15px;
  cursor: pointer;
  border: 2px solid transparent;
  border-radius: 4px;
  transition: border-color 0.2s ease, transform 0.2s ease;
  overflow: hidden;
}

.sample-image-item:hover {
  transform: translateY(-3px);
}

.sample-image-item:hover,
.sample-image-item.active {
  border-color: var(--accent-color);
}

.sample-image-wrapper {
  position: relative;
}

.sample-image-item img {
  display: block;
  width: 100%;
  height: auto;
  max-height: 150px;
  border-radius: 2px;
}

.sample-image-info {
  background-color: rgba(255, 255, 255, 0.9);
  padding: 5px 8px;
  border-radius: 3px;
  position: absolute;
  bottom: 5px;
  left: 5px;
  right: 5px;
  text-align: center;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.texture-name {
  font-weight: 600;
  color: var(--text-color, #222228);
  font-size: 0.9rem;
}

.texture-preview-container {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 20px;
  min-width: 0;
}

.preview-room {
    position: relative;
    border: 1px solid #eee;
    border-radius: 4px;
    overflow: hidden;
    background-color: #f9f9f9;
}

.texture-image-main {
    position: relative;
    width: 100%;
    aspect-ratio: 1200 / 800;
    display: flex;
    align-items: center;
    justify-content: center;
}

.image-placeholder svg {
    display: block;
    width: 100%;
    height: 100%;
    max-width: 100%;
    max-height: 500px;
    object-fit: contain;
}

.main-preview-image {
    display: block;
    width: 100%;
    height: 100%;
    object-fit: cover;
    cursor: zoom-in;
    border-radius: 4px;
}

.zoom-button {
  position: absolute;
  top: 10px;
  right: 10px;
  background-color: rgba(0, 0, 0, 0.5);
  color: white;
  border: none;
  border-radius: 50%;
  width: 36px;
  height: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: background-color 0.2s ease;
  z-index: 10;
}

.zoom-button:hover {
  background-color: rgba(0, 0, 0, 0.7);
}

.zoom-button svg {
  width: 20px;
  height: 20px;
}

.texture-sample-info {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background-color: rgba(0, 0, 0, 0.6);
  color: white;
  padding: 8px 12px;
  font-size: 0.9rem;
  border-bottom-left-radius: 4px;
  border-bottom-right-radius: 4px;
  z-index: 5;
  transition: opacity 0.3s ease;
}

.texture-sample-info .sample-name {
    font-weight: bold;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.texture-sample-info .sample-code {
    font-size: 0.8rem;
    opacity: 0.8;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.texture-samples-container {
  border: 1px solid #eee;
  border-radius: 4px;
  padding: 15px;
  background-color: #fff;
  overflow-x: auto;
  margin-bottom: 20px;
}

.texture-samples {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(80px, 1fr));
  gap: 10px;
}

.texture-sample {
    display: block;
}


.sample-button {
  position: relative;
  border: 2px solid transparent;
  padding: 0;
  background: none;
  cursor: pointer;
  border-radius: 4px;
  display: block;
  width: 100%;
  transition: border-color 0.2s ease;
  overflow: hidden;
  aspect-ratio: 1 / 1;
}

.sample-button:hover,
.sample-button.active {
  border-color: var(--accent-color);
}

.sample-button img.sample-image {
  display: block;
  width: 100%;
  height: 100%;
  object-fit: cover;
  border-radius: 2px;
}

.texture-lightbox {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.85);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  opacity: 0;
  visibility: hidden;
  transition: opacity 0.3s ease, visibility 0.3s ease;
}

.texture-lightbox.active {
  opacity: 1;
  visibility: visible;
}

.lightbox-content {
  position: relative;
  max-width: 95%;
  max-height: 95vh;
  background-color: #fff;
  border-radius: 5px;
  overflow: visible;
  padding: 20px;
  box-shadow: 0 5px 15px rgba(0,0,0,0.3);
}

.lightbox-container {
  display: flex;
  gap: 20px;
  height: 100%;
}

.lightbox-sidebar {
  width: 150px;
  flex-shrink: 0;
  max-height: 90vh;
  overflow-y: auto;
}

.lightbox-thumbnails {
  display: block;
  gap: 10px;
  max-height: 80vh;
  width: 100%;
  padding: 10px 5px;
  scroll-behavior: smooth;
  scrollbar-width: thin;
}

/* Stylized scrollbar for desktop */
.lightbox-thumbnails::-webkit-scrollbar {
  width: 6px;
}

.lightbox-thumbnails::-webkit-scrollbar-track {
  background: rgba(0, 0, 0, 0.05);
  border-radius: 3px;
}

.lightbox-thumbnails::-webkit-scrollbar-thumb {
  background-color: rgba(0, 0, 0, 0.2);
  border-radius: 3px;
}

.lightbox-thumbnails::-webkit-scrollbar-thumb:hover {
  background-color: rgba(0, 0, 0, 0.3);
}

.lightbox-thumbnail {
  width: 120px;
  height: 120px;
  flex-shrink: 0;
  display: block;
  border: 2px solid transparent;
  border-radius: 4px;
  overflow: hidden;
  cursor: pointer;
  transition: border-color 0.2s ease, transform 0.2s ease;
  margin-bottom: 15px;
}

.lightbox-thumbnail:hover {
  transform: translateX(3px);
}

.lightbox-thumbnail.active {
  border-color: var(--accent-color);
}

.lightbox-thumbnail img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  display: block;
}

.lightbox-main {
  flex: 1;
  position: relative;
  display: flex;
  flex-direction: column;
  min-width: 0;
}

.close-lightbox {
  position: absolute;
  top: 10px;
  right: 10px;
  background: rgba(255, 255, 255, 0.9);
  border: none;
  color: #333;
  font-size: 24px;
  cursor: pointer;
  z-index: 10;
  padding: 8px;
  line-height: 1;
  border-radius: 50%;
  width: 36px;
  height: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
}

.close-lightbox svg {
    width: 24px;
    height: 24px;
    stroke: #555;
}

.close-lightbox:hover svg {
    stroke: #000;
}

.lightbox-image {
  display: block;
  max-width: 100%;
  max-height: calc(90vh - 80px);
  width: auto;
  height: auto;
  margin: 0 auto 15px auto;
  object-fit: contain;
  transition: opacity 0.3s ease;
}

.lightbox-info {
  text-align: center;
  color: #333;
  margin-top: 15px;
  background-color: rgba(255, 255, 255, 0.9);
  padding: 10px 15px;
  border-radius: 4px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.lightbox-name {
  font-weight: bold;
  margin-bottom: 5px;
  font-size: 1.1rem;
}

.lightbox-code {
  font-size: 0.9rem;
  color: #666;
}

.texture-explorer-container[data-layout="horizontal"] {
  flex-direction: column;
}

.texture-explorer-container[data-layout="horizontal"] .texture-sidebar {
  width: 100%;
  margin-bottom: 20px;
  display: flex;
  flex-direction: row;
  gap: 20px;
}

.texture-explorer-container[data-layout="horizontal"] .category-selector {
  flex-basis: 250px;
  flex-shrink: 0;
  margin-bottom: 0;
}

.texture-explorer-container[data-layout="horizontal"] .sidebar-heading {
  flex-basis: 250px;
  flex-shrink: 0;
}

.texture-explorer-container[data-layout="horizontal"] .sample-images-list {
  flex-grow: 1;
  max-height: 150px;
  overflow-y: auto;
}

.texture-explorer-container[data-layout="horizontal"] .sample-image-item {
  margin-bottom: 10px;
  width: 100px;
  height: auto;
}

.texture-explorer-container[data-layout="horizontal"] .sample-image-item img {
  width: 100%;
  height: auto;
  object-fit: cover;
}

.prev-lightbox,
.next-lightbox {
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  background: rgba(255, 255, 255, 0.8);
  color: #333;
  border: none;
  border-radius: 50%;
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  z-index: 10;
  transition: background-color 0.2s ease, transform 0.2s ease;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
}

.prev-lightbox:hover,
.next-lightbox:hover {
  background-color: rgba(255, 255, 255, 0.95);
  transform: translateY(-50%) scale(1.1);
}

.prev-lightbox {
  left: 15px;
}

.next-lightbox {
  right: 15px;
}

.prev-lightbox svg,
.next-lightbox svg {
  width: 24px;
  height: 24px;
  stroke: #555;
}

.prev-lightbox:hover svg,
.next-lightbox:hover svg {
  stroke: #000;
}

/* Loading spinner */
.lightbox-loading {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  z-index: 5;
}

.loading-spinner {
  width: 50px;
  height: 50px;
  display: flex;
  justify-content: center;
  align-items: center;
}

.spinner-circle {
  width: 40px;
  height: 40px;
  border: 4px solid rgba(255, 255, 255, 0.3);
  border-radius: 50%;
  border-top-color: #fff;
  animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

@media screen and (min-width: 1024px) {
  .lightbox-content {
    max-width: 95%;
    max-height: 95vh;
    padding: 30px;
  }

  .lightbox-sidebar {
    width: 180px;
  }

  .lightbox-image {
    max-height: calc(90vh - 60px);
  }

  .lightbox-container {
    gap: 30px;
  }
}

@media screen and (max-width: 767px) {
  .texture-explorer-container {
    flex-direction: column;
  }

  .texture-sidebar {
    width: 100%;
    order: 2;
    margin-top: 15px;
    margin-bottom: 0;
  }

  .texture-preview-container {
    order: 1;
  }

  .category-selector {
     width: 100%;
     margin-bottom: 15px;
  }

  .sidebar-heading,
  .samples-heading {
    margin-bottom: 10px;
  }

  .sample-list-helper {
    padding: 6px 8px;
    margin-bottom: 10px;
  }

  .sample-images-list {
    width: 100%;
    display: flex;
    flex-direction: row;
    overflow-x: auto;
    overflow-y: hidden;
    gap: 10px;
    padding: 10px;
  }

  .sample-image-item {
    margin-bottom: 0;
    flex-shrink: 0;
    width: 60px;
    height: 60px;
  }

  .sample-image-item img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    max-height: none;
  }

  .sample-image-info {
    display: none;
  }

  .texture-samples {
    grid-template-columns: repeat(auto-fill, minmax(65px, 1fr));
    gap: 8px;
  }

  .sample-button {
    aspect-ratio: 1 / 1;
  }

  .lightbox-content {
    max-width: 95%;
    max-height: 95%;
    padding: 15px;
    width: 95%;
    display: flex;
    flex-direction: column;
    overflow: visible;
  }

  .lightbox-container {
    flex-direction: column;
    gap: 15px;
  }

  .lightbox-sidebar {
    width: 100%;
    order: 2;
  }

  .lightbox-thumbnails {
    display: flex;
    flex-direction: row;
    max-height: 100px;
    overflow-x: auto;
    overflow-y: hidden;
    padding: 10px 5px;
    gap: 10px;
    width: 100%;
    justify-content: flex-start;
    flex-wrap: nowrap;
    -webkit-overflow-scrolling: touch;
    scrollbar-width: thin;
    scroll-behavior: smooth;
  }

  /* Make scrollbar less obtrusive */
  .lightbox-thumbnails::-webkit-scrollbar {
    height: 6px;
  }

  .lightbox-thumbnails::-webkit-scrollbar-thumb {
    background-color: rgba(0,0,0,0.3);
    border-radius: 3px;
  }

  .lightbox-thumbnails::-webkit-scrollbar-track {
    background-color: rgba(0,0,0,0.1);
  }

  .lightbox-thumbnail {
    width: 60px;
    height: 60px;
    flex-shrink: 0;
    display: block;
    border: 2px solid transparent;
    margin-right: 10px;
    margin-bottom: 0;
    cursor: pointer;
    overflow: hidden;
    transition: border-color 0.2s ease, transform 0.2s ease;
  }

  .lightbox-thumbnail:hover {
    transform: translateY(-3px);
  }

  .lightbox-thumbnail img {
    width: 100%;
    height: 100%;
    object-fit: cover;
  }

  .lightbox-thumbnail.active {
    border-color: var(--accent-color);
  }

  .lightbox-main {
    order: 1;
  }

  .lightbox-image {
    width: auto;
    height: auto;
    max-width: 100%;
    max-height: 60vh;
    object-fit: contain;
    margin: 0 auto;
  }

  .prev-lightbox,
  .next-lightbox {
    width: 36px;
    height: 36px;
  }

  .prev-lightbox {
    left: 10px;
  }

  .next-lightbox {
    right: 10px;
  }

  .texture-explorer-container[data-layout="horizontal"] .texture-sidebar {
    flex-direction: column;
    gap: 15px;
    order: 2;
    margin-top: 15px;
    margin-bottom: 0;
  }

  .texture-explorer-container[data-layout="horizontal"] .texture-preview-container {
    order: 1;
  }

  .texture-explorer-container[data-layout="horizontal"] .category-selector {
    width: 100%;
    flex-basis: auto;
    margin-bottom: 10px;
  }

  .texture-explorer-container[data-layout="horizontal"] .sample-list-helper {
    padding: 6px 8px;
    margin-bottom: 10px;
  }

  .texture-explorer-container[data-layout="horizontal"] .sample-images-list {
    width: 100%;
    display: flex;
    flex-direction: row;
    overflow-x: auto;
    overflow-y: hidden;
    gap: 10px;
    padding: 10px;
  }

  .texture-explorer-container[data-layout="horizontal"] .sample-image-item {
    margin-bottom: 0;
    flex-shrink: 0;
    width: 60px;
    height: 60px;
  }

  .texture-explorer-container[data-layout="horizontal"] .sample-image-item img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    max-height: none;
  }

  .texture-explorer-container[data-layout="horizontal"] .sample-image-info {
    padding: 3px 5px;
  }
}