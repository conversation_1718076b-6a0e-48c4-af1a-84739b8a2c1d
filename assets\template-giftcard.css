/* Base */
*,
*::before,
*::after {
  box-sizing: inherit;
}

a:empty,
ul:empty,
dl:empty,
div:empty,
section:empty,
article:empty,
p:empty,
h1:empty,
h2:empty,
h3:empty,
h4:empty,
h5:empty,
h6:empty {
  display: none;
}

html {
  font-size: calc(var(--font-body-scale) * 62.5%);
  box-sizing: border-box;
  height: 100%;
}

body {
  background-color: rgb(var(--color-background));
  color: rgb(var(--color-foreground));
  letter-spacing: 0.07rem;
  line-height: calc(1 + 0.8 / var(--font-body-scale));
  max-width: var(--page-width);
  margin: 0 auto;
  flex: 1 0 auto;
  font-family: var(--font-body-family);
  font-style: var(--font-body-style);
  font-weight: var(--font-body-weight);
}

@media only screen and (min-width: 750px) {
  body {
    line-height: calc(1 + 0.8 / var(--font-body-scale));
  }
}

::selection {
  background-color: rgba(var(--color-foreground), 0.2);
}

h1,
.h1,
h2,
.h2 {
  font-family: var(--font-heading-family);
  font-style: var(--font-heading-style);
  font-weight: var(--font-heading-weight);
  letter-spacing: calc(var(--font-heading-scale) * 0.06rem);
  line-height: calc(1 + 0.3 / max(1, var(--font-heading-scale)));
  text-align: center;
}

h1,
.h1 {
  font-size: calc(var(--font-heading-scale) * 3rem);
}

@media only screen and (min-width: 750px) {
  h1,
  .h1 {
    font-size: calc(var(--font-heading-scale) * 4rem);
  }
}

h2,
.h2 {
  font-size: calc(var(--font-heading-scale) * 2rem);
}

@media only screen and (min-width: 750px) {
  h2,
  .h2 {
    font-size: calc(var(--font-heading-scale) * 2.4rem);
  }
}

.button {
  font-family: inherit;
  cursor: pointer;
  display: inline-flex;
  justify-content: center;
  align-items: center;
  text-align: center;
  border: none;
  padding: 1.5rem 3rem;
  text-decoration: none;
  background-color: rgb(var(--color-button));
  box-shadow: 0 0 0 0.1rem rgb(var(--color-button));
  color: rgb(var(--color-button-label));
  min-width: 12rem;
  height: auto;
  font-size: 1.5rem;
  letter-spacing: 0.1rem;
  line-height: calc(1 + 0.2 / var(--font-body-scale));
}

.button::selection {
  background-color: rgba(var(--color-button-label), 0.3);
}

.button:hover {
  box-shadow: 0 0 0 0.2rem rgb(var(--color-button));
}

.button--secondary {
  color: rgb(var(--color-secondary-button-label));
  box-shadow: 0 0 0 0.1rem rgb(var(--color-secondary-button-label));
  background-color: transparent;
}

.button--secondary::selection {
  background-color: rgba(var(--color-secondary-button-label), 0.3);
}

.button--secondary:hover {
  box-shadow: 0 0 0 0.2rem rgb(var(--color-secondary-button-label));
}

@media (forced-colors: active) {
  .button {
    border: transparent solid 1px;
  }
}

.gift-card {
  padding: 3rem;
}

@media only screen and (min-width: 750px) {
  .gift-card {
    padding: 5rem 5rem 3rem;
  }
}

.gift-card__image-wrapper {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100%;
  margin: 3rem auto;
  max-width: 40rem;
}

.gift-card__image {
  max-width: 100%;
  height: auto;
  object-fit: scale-down;
  max-height: 26rem;
}

@media only screen and (min-width: 750px) {
  .gift-card__image {
    padding: 0;
  }
}

.gift-card__price {
  display: flex;
  flex-wrap: wrap;
  gap: 0rem 1rem;
  align-items: center;
  justify-content: center;
  letter-spacing: 1px;
  opacity: 0.8;
}

.gift-card__price h1 {
  margin: 0;
}

.gift-card__number {
  color: rgb(var(--color-foreground));
  font-size: 1.8rem;
  line-height: calc(1 + 0.6 / var(--font-body-scale));
  text-align: center;
  letter-spacing: 0.25rem;
  opacity: 0.8;
  margin: 3rem 0;
}

.gift-card__text-wrapper {
  max-width: 30rem;
  margin: 0 auto;
}

.gift-card__text {
  text-align: center;
  font-size: 1.7rem;
  opacity: 0.6;
  margin: 0;
  line-height: calc(1 + 0.5 / var(--font-body-scale));
}

.badge {
  border: 1px solid transparent;
  border-radius: 4rem;
  display: inline-block;
  font-size: 1.2rem;
  letter-spacing: 0.1rem;
  line-height: 1;
  padding: 0.6rem 1.3rem;
  text-align: center;
  background-color: rgb(var(--color-background));
  border-color: rgba(var(--color-foreground), 0.04);
  color: rgb(var(--color-foreground));
}

.badge--expired {
  background-color: rgb(var(--color-soldout-badge));
  color: rgb(var(--color-soldout-badge-label));
  border-color: rgba(var(--color-soldout-badge-label), 0.04);
}

.gift-card__qr-code {
  margin: 3rem 0;
}

.gift-card__qr-code img {
  margin: 0 auto;
}

.gift_card__apple-wallet {
  line-height: 0;
  display: block;
  margin-bottom: 3rem;
  text-align: center;
}

.gift-card__buttons {
  display: flex;
  flex-direction: column;
  max-width: 25rem;
  flex-wrap: wrap;
  margin: 0 auto;
}

.gift-card__buttons > .button {
  display: block;
  margin: 1rem 0;
}

/*
  Focus ring - default (with offset)
*/

/* Fallback - for browsers that don't support :focus-visible, a fallback is set for :focus */
*:focus {
  outline: 0;
  box-shadow: none;
}

*:focus-visible {
  outline: 0.2rem solid rgba(var(--color-foreground), 0.5);
  outline-offset: 0.3rem;
  box-shadow: 0 0 0 0.3rem rgb(var(--color-background)), 0 0 0.5rem 0.4rem rgba(var(--color-foreground), 0.3);
}

/* Fallback - for browsers that don't support :focus-visible, a fallback is set for :focus */
.focused,
.no-js *:focus {
  outline: 0.2rem solid rgba(var(--color-foreground), 0.5);
  outline-offset: 0.3rem;
  box-shadow: 0 0 0 0.3rem rgb(var(--color-background)), 0 0 0.5rem 0.4rem rgba(var(--color-foreground), 0.3);
}

.button:focus-visible {
  box-shadow: 0 0 0 0.1rem rgb(var(--color-button)), 0 0 0 0.3rem rgb(var(--color-background)),
    0 0 0.5rem 0.4rem rgba(var(--color-foreground), 0.3);
}

/* Negate the fallback side-effect for browsers that support :focus-visible */
.no-js *:focus:not(:focus-visible) {
  outline: 0;
  box-shadow: none;
}

.button:focus {
  box-shadow: 0 0 0 0.1rem rgb(var(--color-button)), 0 0 0 0.3rem rgb(var(--color-background)),
    0 0 0.5rem 0.4rem rgba(var(--color-foreground), 0.3);
}

.button--secondary:focus-visible {
  box-shadow: 0 0 0 0.1rem rgb(var(--color-secondary-button-label)), 0 0 0 0.3rem rgb(var(--color-background)),
    0 0 0.5rem 0.4rem rgba(var(--color-foreground), 0.3);
}

.button--secondary:focus {
  box-shadow: 0 0 0 0.1rem rgb(var(--color-secondary-button-label)), 0 0 0 0.3rem rgb(var(--color-background)),
    0 0 0.5rem 0.4rem rgba(var(--color-foreground), 0.3);
}

.form__message {
  align-items: center;
  display: flex;
  font-size: 1.2rem;
  line-height: 1.5rem;
  justify-content: center;
  margin-bottom: 0.5rem;
  opacity: 0.8;
}

.form__message .icon {
  flex-shrink: 0;
  height: 1.3rem;
  margin-right: 0.5rem;
  width: 1.3rem;
}

@media print {
  .no-print {
    display: none;
  }
}

.gradient {
  background: rgb(var(--color-background));
  background: var(--gradient-background);
  background-attachment: fixed;
}
