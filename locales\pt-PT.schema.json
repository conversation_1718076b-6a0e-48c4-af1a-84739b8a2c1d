{"settings_schema": {"colors": {"name": "Cores", "settings": {"background": {"label": "Fundo"}, "background_gradient": {"label": "Gradiente de fundo", "info": "O gradiente de fundo substitui o fundo sempre que possível."}, "text": {"label": "Texto"}, "button_background": {"label": "Fundo do botão sólido"}, "button_label": {"label": "Etiqueta do botão sólido"}, "secondary_button_label": {"label": "Botão de contorno"}, "shadow": {"label": "Sombra"}}}, "typography": {"name": "Tipografia", "settings": {"type_header_font": {"label": "<PERSON><PERSON><PERSON> de letra", "info": "Selecionar um tipo de letra diferente pode afetar a velocidade da sua loja. [Saiba mais sobre tipos de letra do sistema.](https://help.shopify.com/manual/online-store/os/store-speed/improving-speed#fonts)"}, "header__1": {"content": "<PERSON><PERSON><PERSON><PERSON>"}, "header__2": {"content": "Corpo"}, "type_body_font": {"label": "<PERSON><PERSON><PERSON> de letra", "info": "Selecionar um tipo de letra diferente pode afetar a velocidade da sua loja. [Saiba mais sobre tipos de letra do sistema.](https://help.shopify.com/manual/online-store/os/store-speed/improving-speed#fonts)"}, "heading_scale": {"label": "Escala de tamanho do tipo de letra"}, "body_scale": {"label": "Escala de tamanho do tipo de letra"}}}, "social-media": {"name": "Redes sociais", "settings": {"social_twitter_link": {"label": "Twitter", "info": "https://twitter.com/shopify"}, "social_facebook_link": {"label": "Facebook", "info": "https://facebook.com/shopify"}, "social_pinterest_link": {"label": "Pinterest", "info": "https://pinterest.com/shopify"}, "social_instagram_link": {"label": "Instagram", "info": "http://instagram.com/shopify"}, "social_tiktok_link": {"label": "TikTok", "info": "https://twitter.com/shopify"}, "social_tumblr_link": {"label": "Tumblr", "info": "http://shopify.tumblr.com"}, "social_snapchat_link": {"label": "Snapchat", "info": "https://www.snapchat.com/add/shopify"}, "social_youtube_link": {"label": "YouTube", "info": "https://www.youtube.com/shopify"}, "social_vimeo_link": {"label": "Vimeo", "info": "https://vimeo.com/shopify"}, "header": {"content": "Contas de redes sociais"}}}, "currency_format": {"name": "Formato de moeda", "settings": {"content": "Códigos de moeda", "currency_code_enabled": {"label": "Mostrar códigos de moeda"}, "paragraph": "Os preços de finalização da compra e carrinho mostram sempre os códigos de moeda. Exemplo: 1,00 USD."}}, "layout": {"name": "Esquema", "settings": {"page_width": {"label": "<PERSON><PERSON><PERSON> da página"}, "spacing_sections": {"label": "Espaço entre as secções do modelo"}, "header__grid": {"content": "Grelha"}, "paragraph__grid": {"content": "Afeta áreas com várias colunas ou linhas."}, "spacing_grid_horizontal": {"label": "Espaço horizontal"}, "spacing_grid_vertical": {"label": "Espaço vertical"}}}, "search_input": {"name": "Comportamento de pesquisa", "settings": {"header": {"content": "Sugestões de pesquisa"}, "predictive_search_enabled": {"label": "Ativar as sugestões de pesquisa"}, "predictive_search_show_vendor": {"label": "Mostrar o fornecedor do produto", "info": "<PERSON><PERSON><PERSON><PERSON> quando as sugestões de pesquisa estão ativas."}, "predictive_search_show_price": {"label": "Mostrar o preço do produto", "info": "<PERSON><PERSON><PERSON><PERSON> quando as sugestões de pesquisa estão ativas."}}}, "global": {"settings": {"header__border": {"content": "Limite"}, "header__shadow": {"content": "Sombra"}, "blur": {"label": "Desfocado"}, "corner_radius": {"label": "Raio do canto"}, "horizontal_offset": {"label": "Compensação horizontal"}, "vertical_offset": {"label": "Compensação vertical"}, "thickness": {"label": "E<PERSON><PERSON><PERSON>"}, "opacity": {"label": "Opacidade"}, "image_padding": {"label": "Preenchi<PERSON> da imagem"}, "text_alignment": {"options__1": {"label": "E<PERSON>rda"}, "options__2": {"label": "Ao centro"}, "options__3": {"label": "<PERSON><PERSON><PERSON>"}, "label": "Alinhamento do texto"}}}, "badges": {"name": "<PERSON><PERSON>", "settings": {"position": {"options__1": {"label": "Canto inferior esquerdo"}, "options__2": {"label": "Canto inferior direito"}, "options__3": {"label": "Canto superior esquerdo"}, "options__4": {"label": "Canto superior direito"}, "label": "Posição nos cartões"}, "sale_badge_color_scheme": {"label": "Esquema de cor do selo de venda"}, "sold_out_badge_color_scheme": {"label": "Esquema de cor do selo de esgotado"}}}, "buttons": {"name": "<PERSON><PERSON><PERSON><PERSON>"}, "variant_pills": {"name": "Variantes com forma de comprimidos", "paragraph": "As variantes com forma de comprimidos são uma forma de apresentar as variantes do seu produto. [Saber mais](https://help.shopify.com/en/manual/online-store/themes/theme-structure/page-types#variant-picker-block)"}, "inputs": {"name": "Entradas"}, "content_containers": {"name": "Contentores de conteúdo"}, "popups": {"name": "Menus pendentes e pop-ups", "paragraph": "Afeta áreas como menus pendentes de navegação, pop-ups de modais e pop-ups de carrinho."}, "media": {"name": "Conteúdo multimédia"}, "drawers": {"name": "Gavetas"}, "cart": {"name": "<PERSON><PERSON><PERSON>", "settings": {"cart_type": {"label": "<PERSON><PERSON><PERSON> <PERSON>", "drawer": {"label": "Gaveta"}, "page": {"label": "<PERSON><PERSON><PERSON><PERSON>"}, "notification": {"label": "Notificação pop-up"}}, "show_vendor": {"label": "<PERSON><PERSON> forne<PERSON>or"}, "show_cart_note": {"label": "Ativar nota do carrinho"}, "cart_drawer": {"header": "Painel deslizante do carrinho", "collection": {"label": "Coleção", "info": "Visível quando o painel deslizante do carrinho está vazio."}}}}, "cards": {"name": "Cartões de produtos", "settings": {"style": {"options__1": {"label": "Padrão"}, "options__2": {"label": "Cartão"}, "label": "<PERSON><PERSON><PERSON>"}}}, "collection_cards": {"name": "Cartões de coleção", "settings": {"style": {"options__1": {"label": "Padrão"}, "options__2": {"label": "Cartão"}, "label": "<PERSON><PERSON><PERSON>"}}}, "blog_cards": {"name": "Cartões de blogue", "settings": {"style": {"options__1": {"label": "Padrão"}, "options__2": {"label": "Cartão"}, "label": "<PERSON><PERSON><PERSON>"}}}, "logo": {"name": "Logótipo", "settings": {"logo_image": {"label": "Logótipo"}, "logo_width": {"label": "Largura do logótipo em computador", "info": "A largura do logótipo é automaticamente otimizada para dispositivos móveis."}, "favicon": {"label": "<PERSON><PERSON>", "info": "Será reduzida para 32 x 32 px"}}}, "brand_information": {"name": "Informação de marca", "settings": {"brand_headline": {"label": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "brand_description": {"label": "Descrição"}, "brand_image": {"label": "Imagem"}, "brand_image_width": {"label": "<PERSON><PERSON><PERSON> da <PERSON>"}, "paragraph": {"content": "Adicione uma descrição da marca ao rodapé da sua loja."}}}, "animations": {"name": "Animações", "settings": {"animations_reveal_on_scroll": {"label": "Revelar secções ao percorrer a página"}, "animations_hover_elements": {"options__1": {"label": "<PERSON><PERSON><PERSON>"}, "options__2": {"label": "Elevação vertical"}, "label": "Efeito ao passar o rato", "info": "Afeta cartões e botões.", "options__3": {"label": "Elevação 3D"}}}}}, "sections": {"all": {"padding": {"section_padding_heading": "Secção preenchimento", "padding_top": "Preenchimento superior", "padding_bottom": "Preenchimento inferior"}, "spacing": "Espaçamento", "colors": {"label": "Esquema de cores", "has_cards_info": "Para alterar o esquema de cores do cartão, atualize as suas definições de tema."}, "heading_size": {"label": "Tamanho do título", "options__1": {"label": "Pequeno"}, "options__2": {"label": "Médio"}, "options__3": {"label": "Grande"}, "options__4": {"label": "Extra grande"}}, "image_shape": {"options__1": {"label": "Predefinição"}, "options__2": {"label": "Arco"}, "options__3": {"label": "<PERSON><PERSON>"}, "options__4": {"label": "Divisa para a esquerda"}, "options__5": {"label": "Divisa para a direita"}, "options__6": {"label": "Diamante"}, "options__7": {"label": "Paralelogramo"}, "options__8": {"label": "Redonda"}, "label": "Forma de imagem", "info": "Os cartões com o estilo padrão não têm bordas quando uma forma de imagem está ativa."}, "animation": {"content": "Animações", "image_behavior": {"options__1": {"label": "<PERSON><PERSON><PERSON>"}, "options__2": {"label": "Movimento de ambiente"}, "label": "Comportamento da imagem", "options__3": {"label": "Posição de fundo fixa"}, "options__4": {"label": "Ampliar ao rodar a roda do rato"}}}}, "announcement-bar": {"name": "Barra de comunicado", "blocks": {"announcement": {"settings": {"text": {"label": "Texto"}, "text_alignment": {"label": "Alinhamento do texto", "options__1": {"label": "E<PERSON>rda"}, "options__2": {"label": "Centro"}, "options__3": {"label": "<PERSON><PERSON><PERSON>"}}, "link": {"label": "Ligação"}}, "name": "Comunicado"}}, "settings": {"auto_rotate": {"label": "Rotação automática de comunicados"}, "change_slides_speed": {"label": "Mudar a cada"}, "header__1": {"content": "Ícones de redes sociais", "info": "Para apresentar as suas contas de redes sociais, associe-as nas [definições do tema](/editor?context=theme&category=social%20media)."}, "header__2": {"content": "Comunicados"}, "show_social": {"label": "Mostrar ícones no computador"}, "header__3": {"content": "Se<PERSON>or de país/região", "info": "Para adicionar um país/região, aceda a [definições de mercado.](/admin/settings/markets)"}, "enable_country_selector": {"label": "Ativar seletor de país/região"}, "header__4": {"content": "Se<PERSON>or de idioma", "info": "Para adicionar um idioma, aceda a [definições de idioma.](/admin/settings/languages)"}, "enable_language_selector": {"label": "Ativar seletor de idioma"}}, "presets": {"name": "Barra de comunicado"}}, "collage": {"name": "Colagem", "settings": {"heading": {"label": "<PERSON><PERSON><PERSON><PERSON>"}, "desktop_layout": {"label": "Esquema do desktop", "options__1": {"label": "Bloco es<PERSON>do grande"}, "options__2": {"label": "Bloco direito grande"}}, "mobile_layout": {"label": "Esquema móvel", "options__1": {"label": "Colagem"}, "options__2": {"label": "Coluna"}}, "card_styles": {"label": "Estilo do cartão", "info": "O produto, coleção e estilos do cartão de blogue podem ser atualizados nas definições do tema.", "options__1": {"label": "Utilizar estilos de cartão individuais"}, "options__2": {"label": "<PERSON><PERSON><PERSON><PERSON> todos os cartões de produto"}}}, "blocks": {"image": {"settings": {"image": {"label": "Imagem"}}, "name": "Imagem"}, "product": {"settings": {"product": {"label": "Produ<PERSON>"}, "secondary_background": {"label": "Mostrar fundo secundário"}, "second_image": {"label": "Mostrar a segunda imagem ao passar o rato"}}, "name": "Produ<PERSON>"}, "collection": {"settings": {"collection": {"label": "Coleção"}}, "name": "Coleção"}, "video": {"settings": {"cover_image": {"label": "<PERSON><PERSON> de capa"}, "video_url": {"label": "URL", "info": "O video será reproduzido numa janela pop-up se a secção contiver outros blocos.", "placeholder": "Usar um URL de YouTube ou Vimeo"}, "description": {"label": "Texto alternativo do vídeo", "info": "Descreve o vídeo para que seja acessível a clientes que usam leitores de ecrã. [<PERSON>ber mais](https://help.shopify.com/manual/online-store/themes/theme-structure/theme-features#video-block)"}}, "name": "Vídeo"}}, "presets": {"name": "Colagem"}}, "collection-list": {"name": "Lista de coleções", "settings": {"title": {"label": "<PERSON><PERSON><PERSON><PERSON>"}, "image_ratio": {"label": "Proporção de imagem", "options__1": {"label": "Ada<PERSON>r à imagem"}, "options__2": {"label": "Retrato"}, "options__3": {"label": "Quadrado"}, "info": "Adicione imagens ao editar as suas coleções. [<PERSON><PERSON> mais](https://help.shopify.com/manual/products/collections)"}, "swipe_on_mobile": {"label": "Ativar leitura magnética no dispositivo móvel"}, "show_view_all": {"label": "Ative o botão \"Ver tudo\" caso a lista possua mais coleções do que as apresentadas"}, "columns_desktop": {"label": "Número de colunas no ambiente de trabalho"}, "header_mobile": {"content": "Esquema para dispositivo móvel"}, "columns_mobile": {"label": "Número de colunas em dispositivo móvel", "options__1": {"label": "1 coluna"}, "options__2": {"label": "2 colunas"}}}, "blocks": {"featured_collection": {"settings": {"collection": {"label": "Coleção"}}, "name": "Coleção"}}, "presets": {"name": "Lista de coleções"}}, "contact-form": {"name": "Formulário de contacto", "presets": {"name": "Formulário de contacto"}}, "custom-liquid": {"name": "Liquid personalizado", "settings": {"custom_liquid": {"label": "Código Liquid", "info": "Adicione fragmentos de aplicação ou outro código para criar personalizações avançadas. [Saber mais](https://shopify.dev/docs/api/liquid)"}}, "presets": {"name": "Liquid personalizado"}}, "featured-blog": {"name": "Publicações no blogue", "settings": {"heading": {"label": "<PERSON><PERSON><PERSON><PERSON>"}, "blog": {"label": "Blogue"}, "post_limit": {"label": "Número de publicações no blogue a mostrar"}, "show_view_all": {"label": "Ative o botão \"Ver tudo\" caso o blogue possua mais publicações no blogue do que as apresentadas"}, "show_image": {"label": "Mostrar imagem em destaque", "info": "Para obter os melhores resultados, use uma imagem com uma proporção de 3:2. [<PERSON><PERSON> mais](https://help.shopify.com/manual/shopify-admin/productivity-tools/image-editor#understanding-image-aspect-ratio)"}, "show_date": {"label": "Mostrar data"}, "show_author": {"label": "Mostrar autor"}, "columns_desktop": {"label": "Número de colunas no ambiente de trabalho"}}, "presets": {"name": "Publicações no blogue"}}, "featured-collection": {"name": "Coleção em destaque", "settings": {"title": {"label": "<PERSON><PERSON><PERSON><PERSON>"}, "collection": {"label": "Coleção"}, "products_to_show": {"label": "Máximo de produtos a apresentar"}, "show_view_all": {"label": "Ative a opção \"Ver tudo\" se a coleção tiver mais produtos do que os apresentados"}, "header": {"content": "Cartão de produtos"}, "image_ratio": {"label": "Proporção de imagem", "options__1": {"label": "Ada<PERSON>r à imagem"}, "options__2": {"label": "Retrato"}, "options__3": {"label": "Quadrado"}}, "show_secondary_image": {"label": "Mostrar a segunda imagem ao passar o rato"}, "show_vendor": {"label": "<PERSON><PERSON> forne<PERSON>or"}, "show_rating": {"label": "Mostrar classificação do produto", "info": "Para mostrar uma classificação, adicione uma aplicação de classificação de produto. [<PERSON><PERSON> mais](https://help.shopify.com/manual/online-store/themes/theme-structure/theme-features#featured-collection-show-product-rating)"}, "enable_quick_buy": {"label": "Ativar botão para adicionar rapidamente", "info": "Ideal para o tipo de carrinho deslizante ou pop-up."}, "columns_desktop": {"label": "Número de colunas no ambiente de trabalho"}, "description": {"label": "Descrição"}, "show_description": {"label": "Mostrar descrição da coleção do administrador"}, "description_style": {"label": "Estilo da descrição", "options__1": {"label": "Corpo"}, "options__2": {"label": "<PERSON>a"}, "options__3": {"label": "Mai<PERSON><PERSON><PERSON>"}}, "view_all_style": {"options__1": {"label": "Ligação"}, "options__2": {"label": "Botão de contorno"}, "options__3": {"label": "Botão sólido"}, "label": "<PERSON><PERSON><PERSON> \"ver tudo\""}, "enable_desktop_slider": {"label": "Ativar carrossel em computador"}, "full_width": {"label": "Tornar os produtos em largura total"}, "header_mobile": {"content": "Esquema para dispositivo móvel"}, "columns_mobile": {"label": "Número de colunas em dispositivo móvel", "options__1": {"label": "1 coluna"}, "options__2": {"label": "2 colunas"}}, "swipe_on_mobile": {"label": "Ativar leitura magnética no dispositivo móvel"}}, "presets": {"name": "Coleção em destaque"}}, "footer": {"name": "Rodapé", "blocks": {"link_list": {"settings": {"heading": {"label": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "menu": {"label": "<PERSON><PERSON>", "info": "Apresenta apenas itens de menu de nível superior."}}, "name": "<PERSON><PERSON>"}, "text": {"settings": {"heading": {"label": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "subtext": {"label": "Subtexto"}}, "name": "Texto"}, "brand_information": {"name": "Informação de marca", "settings": {"paragraph": {"content": "Este bloco irá apresentar a sua informação de marca. [Editar informação de marca.](/editor?context=theme&category=brand%20information)"}, "header__1": {"content": "Ícones de redes sociais"}, "show_social": {"label": "Mostrar ícones de redes sociais", "info": "Para apresentar as suas contas de redes sociais, associe-as nas [definições do tema](/editor?context=theme&category=social%20media)."}}}}, "settings": {"newsletter_enable": {"label": "Mostrar registo de e-mail"}, "newsletter_heading": {"label": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "header__1": {"info": "Subscritores adicionados automaticamente à sua lista de clientes que \"aceitam marketing\". [Saber mais](https://help.shopify.com/manual/customers/manage-customers)", "content": "Registo de e-mail"}, "header__2": {"content": "Ícones de redes sociais", "info": "Para apresentar as suas contas de redes sociais, associe-as nas [definições do tema](/editor?context=theme&category=social%20media)."}, "show_social": {"label": "Mostrar ícones de redes sociais"}, "header__3": {"content": "Se<PERSON>or de país/região"}, "header__4": {"info": "Para adicionar um país/região, vá a [definições de mercado.](/admin/settings/markets)"}, "enable_country_selector": {"label": "Ativar seletor de país/região"}, "header__5": {"content": "Se<PERSON>or de idioma"}, "header__6": {"info": "Para adicionar um idioma, vá a [definições de idioma.](/admin/settings/languages)"}, "enable_language_selector": {"label": "Ativar seletor de idioma"}, "header__7": {"content": "Métodos de pagamento"}, "payment_enable": {"label": "Mostrar ícones de pagamento"}, "margin_top": {"label": "Margem superior"}, "header__8": {"content": "Ligações das políticas", "info": "Para adicionar políticas de loja, aceda às suas [definições de políticas](/admin/settings/legal)."}, "show_policy": {"label": "Mostrar ligações das políticas"}, "header__9": {"content": "Se<PERSON>ir no <PERSON>", "info": "O Shop Pay tem de estar ativo para os clientes poderem seguir a sua loja na aplicação Shop a partir da sua frente de loja. [Saber mais](https://help.shopify.com/manual/online-store/themes/customizing-themes/follow-on-shop)"}, "enable_follow_on_shop": {"label": "Ativar Seguir no Shop"}}}, "header": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "settings": {"logo_position": {"label": "Posição do logótipo de desktop", "options__1": {"label": "Intermédio à esquerda"}, "options__2": {"label": "Canto superior esquerdo"}, "options__3": {"label": "Superior centro"}, "options__4": {"label": "Intermédio ao centro"}}, "menu": {"label": "<PERSON><PERSON>"}, "show_line_separator": {"label": "Mostrar linha do separador"}, "margin_bottom": {"label": "Margem inferior"}, "menu_type_desktop": {"label": "Tipo de menu do ambiente de trabalho", "info": "O tipo de menu é automaticamente otimizado para dispositivos móveis.", "options__1": {"label": "<PERSON><PERSON> pendente"}, "options__2": {"label": "Mega menu"}, "options__3": {"label": "<PERSON><PERSON>"}}, "mobile_layout": {"content": "Esquema móvel"}, "mobile_logo_position": {"label": "Posição móvel do logótipo", "options__1": {"label": "Centro"}, "options__2": {"label": "E<PERSON>rda"}}, "logo_help": {"content": "Edite o seu logótipo em [definições de tema](/editor?context=theme&category=logo)."}, "sticky_header_type": {"label": "Cabeçalho fixo", "options__1": {"label": "<PERSON><PERSON><PERSON>"}, "options__2": {"label": "Ao rodar a roda do rato"}, "options__3": {"label": "Sempre"}, "options__4": {"label": "Se<PERSON><PERSON>, reduzir o tamanho do logótipo"}}, "header__3": {"content": "Se<PERSON>or de país/região"}, "header__4": {"info": "Para adicionar um país/região, aceda a [definições de mercado.](/admin/settings/markets)"}, "enable_country_selector": {"label": "Ativar seletor de país/região"}, "header__5": {"content": "Se<PERSON>or de idioma"}, "header__6": {"info": "Para adicionar um idioma, aceda a [definições de idioma.](/admin/settings/languages)"}, "enable_language_selector": {"label": "Ativar seletor de idioma"}, "header__1": {"content": "Cor"}, "menu_color_scheme": {"label": "Esquema de cores do menu"}}}, "image-banner": {"name": "Faixa de imagem", "settings": {"image": {"label": "Primeira imagem"}, "image_2": {"label": "Segunda imagem"}, "stack_images_on_mobile": {"label": "Empilhar imagens em dispositivos móveis"}, "show_text_box": {"label": "Mostrar recetores no desktop"}, "image_overlay_opacity": {"label": "Opacidade da sobreposição da imagem"}, "show_text_below": {"label": "Mostrar recetor em dispositivo móvel"}, "image_height": {"label": "Altura da faixa", "options__1": {"label": "Adaptar à primeira imagem"}, "options__2": {"label": "Pequeno"}, "options__3": {"label": "Médio"}, "info": "Para obter os melhores resultados, use uma imagem com uma proporção de 3:2. [<PERSON><PERSON> mais](https://help.shopify.com/manual/shopify-admin/productivity-tools/image-editor#understanding-image-aspect-ratio)", "options__4": {"label": "Grande"}}, "desktop_content_position": {"options__1": {"label": "Canto superior esquerdo"}, "options__2": {"label": "Superior centro"}, "options__3": {"label": "Canto superior direito"}, "options__4": {"label": "Intermédio à esquerda"}, "options__5": {"label": "Intermédio ao centro"}, "options__6": {"label": "Intermédio à direita"}, "options__7": {"label": "Canto inferior esquerdo"}, "options__8": {"label": "Inferior centro"}, "options__9": {"label": "Canto inferior direito"}, "label": "Posição do conteúdo em computadores"}, "desktop_content_alignment": {"options__1": {"label": "E<PERSON>rda"}, "options__2": {"label": "Ao centro"}, "options__3": {"label": "<PERSON><PERSON><PERSON>"}, "label": "Alinhamento do conteúdo em computadores"}, "mobile_content_alignment": {"options__1": {"label": "E<PERSON>rda"}, "options__2": {"label": "Ao centro"}, "options__3": {"label": "<PERSON><PERSON><PERSON>"}, "label": "Alinhamento do conteúdo em dispositivos móveis"}, "mobile": {"content": "Esquema para dispositivo móvel"}}, "blocks": {"heading": {"settings": {"heading": {"label": "<PERSON><PERSON><PERSON><PERSON>"}}, "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "text": {"settings": {"text": {"label": "Descrição"}, "text_style": {"options__1": {"label": "Corpo"}, "options__2": {"label": "<PERSON>a"}, "options__3": {"label": "Mai<PERSON><PERSON><PERSON>"}, "label": "Estilo de texto"}}, "name": "Texto"}, "buttons": {"settings": {"button_label_1": {"label": "Primeira etiqueta do botão", "info": "Deixe a etiqueta em branco para ocultar o botão."}, "button_link_1": {"label": "Primeira ligação do botão"}, "button_style_secondary_1": {"label": "Utilizar estilo de botão de contorno"}, "button_label_2": {"label": "Segunda etiqueta do botão", "info": "Deixe a etiqueta em branco para ocultar o botão."}, "button_link_2": {"label": "Segunda ligação do botão"}, "button_style_secondary_2": {"label": "Utilizar estilo de botão de contorno"}}, "name": "<PERSON><PERSON><PERSON><PERSON>"}}, "presets": {"name": "Faixa de imagem"}}, "image-with-text": {"name": "Imagem com texto", "settings": {"image": {"label": "Imagem"}, "height": {"options__1": {"label": "Ada<PERSON>r à imagem"}, "options__2": {"label": "Pequeno"}, "options__3": {"label": "Médio"}, "label": "<PERSON><PERSON> da imagem", "options__4": {"label": "Grande"}}, "layout": {"options__1": {"label": "<PERSON>m primeiro"}, "options__2": {"label": "Segunda imagem"}, "label": "Posicionamento da imagem em desktop", "info": "Imagem primeiro é o esquema predefinido para dispositivos móveis."}, "desktop_image_width": {"options__1": {"label": "Pequeno"}, "options__2": {"label": "Médio"}, "options__3": {"label": "Grande"}, "label": "<PERSON><PERSON><PERSON> da imagem em desktop", "info": "A imagem é otimizada automaticamente em dispositivos móveis."}, "desktop_content_alignment": {"options__1": {"label": "E<PERSON>rda"}, "options__3": {"label": "<PERSON><PERSON><PERSON>"}, "label": "Alinhamento do conteúdo em desktop", "options__2": {"label": "Ao centro"}}, "desktop_content_position": {"options__1": {"label": "Em cima"}, "options__2": {"label": "<PERSON><PERSON>"}, "options__3": {"label": "<PERSON> baixo"}, "label": "Posição do conteúdo em desktop"}, "content_layout": {"options__1": {"label": "Sem sobreposição"}, "options__2": {"label": "Sobreposição"}, "label": "Esquema do conteúdo"}, "mobile_content_alignment": {"options__1": {"label": "E<PERSON>rda"}, "options__3": {"label": "<PERSON><PERSON><PERSON>"}, "label": "Alinhamento do conteúdo em dispositivos móveis", "options__2": {"label": "Ao centro"}}}, "blocks": {"heading": {"settings": {"heading": {"label": "<PERSON><PERSON><PERSON><PERSON>"}}, "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "text": {"settings": {"text": {"label": "<PERSON><PERSON><PERSON><PERSON>"}, "text_style": {"label": "Estilo de texto", "options__1": {"label": "Corpo"}, "options__2": {"label": "<PERSON>a"}}}, "name": "Texto"}, "button": {"settings": {"button_label": {"label": "Etiqueta do botão", "info": "Deixe a etiqueta em branco para ocultar o botão."}, "button_link": {"label": "Ligação do botão"}, "outline_button": {"label": "Utilizar estilo de botão de contorno"}}, "name": "Botão"}, "caption": {"name": "<PERSON>a", "settings": {"text": {"label": "Texto"}, "text_style": {"label": "Estilo de texto", "options__1": {"label": "<PERSON>a"}, "options__2": {"label": "Mai<PERSON><PERSON><PERSON>"}}, "caption_size": {"label": "Tamanho do texto", "options__1": {"label": "Pequeno"}, "options__2": {"label": "Médio"}, "options__3": {"label": "Grande"}}}}}, "presets": {"name": "Imagem com texto"}}, "main-article": {"name": "Publicação no blogue", "blocks": {"featured_image": {"settings": {"image_height": {"label": "Altura da imagem em destaque", "options__1": {"label": "Ada<PERSON>r à imagem"}, "options__2": {"label": "Pequeno"}, "options__3": {"label": "Médio"}, "info": "Para obter os melhores resultados, utilize uma imagem com uma proporção de 16:9. [<PERSON><PERSON> mais](https://help.shopify.com/manual/shopify-admin/productivity-tools/image-editor#understanding-image-aspect-ratio)", "options__4": {"label": "Grande"}}}, "name": "Imagem em destaque"}, "title": {"settings": {"blog_show_date": {"label": "Mostrar data"}, "blog_show_author": {"label": "Mostrar autor"}}, "name": "<PERSON><PERSON><PERSON><PERSON>"}, "content": {"name": "<PERSON><PERSON><PERSON><PERSON>"}, "share": {"name": "Partilhar", "settings": {"featured_image_info": {"content": "Se incluir uma ligação nas publicações das redes sociais, a imagem em destaque da página será demonstrada como a imagem de pré-visualização. [<PERSON><PERSON> ma<PERSON>](https://help.shopify.com/manual/online-store/images/showing-social-media-thumbnail-images)."}, "title_info": {"content": "É incluído um título de loja e descrição com a imagem de pré-visualização. [Saber mais](https://help.shopify.com/manual/promoting-marketing/seo/adding-keywords#set-a-title-and-description-for-your-online-store)."}, "text": {"label": "Texto"}}}}}, "main-blog": {"name": "Publicações no blogue", "settings": {"header": {"content": "Cartão de publicação no blogue"}, "show_image": {"label": "Mostrar imagem em destaque"}, "paragraph": {"content": "Altere excertos ao editar as suas publicações no blogue. [<PERSON><PERSON> ma<PERSON>](https://help.shopify.com/manual/online-store/blogs/writing-blogs#display-an-excerpt-from-a-blog-post)"}, "show_date": {"label": "Mostrar data"}, "show_author": {"label": "Mostrar autor"}, "layout": {"label": "Esquema de desktop", "options__1": {"label": "Grelha"}, "options__2": {"label": "Colagem"}, "info": "Publicações empilhadas em dispositivo móvel."}, "image_height": {"label": "Altura da imagem em destaque", "options__1": {"label": "Ada<PERSON>r à imagem"}, "options__2": {"label": "Pequeno"}, "options__3": {"label": "Médio"}, "options__4": {"label": "Grande"}, "info": "Para obter os melhores resultados, use uma imagem com uma proporção de 3:2. [<PERSON><PERSON> mais](https://help.shopify.com/manual/shopify-admin/productivity-tools/image-editor#understanding-image-aspect-ratio)"}}}, "main-cart-footer": {"name": "Subtotal", "blocks": {"subtotal": {"name": "Subtotal"}, "buttons": {"name": "Botão de finalização da compra"}}}, "main-cart-items": {"name": "<PERSON><PERSON>"}, "main-collection-banner": {"name": "Faixa de coleção", "settings": {"paragraph": {"content": "Adicione uma descrição ou imagem ao editar a sua coleção. [<PERSON><PERSON> ma<PERSON>](https://help.shopify.com/manual/products/collections/collection-layout)"}, "show_collection_description": {"label": "Mostrar descrição da coleção"}, "show_collection_image": {"label": "Mostrar imagem da coleção", "info": "Para obter os melhores resultados, utilize uma imagem com uma proporção de 16:9. [<PERSON><PERSON> mais](https://help.shopify.com/manual/shopify-admin/productivity-tools/image-editor#understanding-image-aspect-ratio)"}}}, "main-collection-product-grid": {"name": "<PERSON>rel<PERSON> de produtos", "settings": {"products_per_page": {"label": "Produtos por página"}, "image_ratio": {"label": "Proporção de imagem", "options__1": {"label": "Ada<PERSON>r à imagem"}, "options__2": {"label": "Retrato"}, "options__3": {"label": "Quadrado"}}, "show_secondary_image": {"label": "Mostrar a segunda imagem ao passar o rato"}, "show_vendor": {"label": "<PERSON><PERSON> forne<PERSON>or"}, "header__1": {"content": "Filtragem e ordenação"}, "enable_tags": {"label": "Ativar filtragem", "info": "Personalize filtros com a aplicação Search & Discovery. [<PERSON><PERSON> mais](https://help.shopify.com/manual/online-store/search-and-discovery/filters)"}, "enable_filtering": {"label": "Ativar filtragem", "info": "Personalize filtros com a aplicação Search & Discovery. [<PERSON><PERSON> mais](https://help.shopify.com/manual/online-store/search-and-discovery/filters)"}, "enable_sorting": {"label": "Ativar ordena<PERSON>"}, "header__3": {"content": "Cartão de produtos"}, "show_rating": {"label": "Mostrar classificação do produto", "info": "Para mostrar uma classificação, adicione uma aplicação de classificação de produto. [<PERSON><PERSON> mais](https://help.shopify.com/manual/online-store/themes/theme-structure/page-types#product-grid-section-settings)"}, "enable_quick_buy": {"label": "Ativar botão para adicionar rapidamente", "info": "Ideal para o tipo de carrinho deslizante ou pop-up."}, "columns_desktop": {"label": "Número de colunas no ambiente de trabalho"}, "header_mobile": {"content": "Esquema para dispositivo móvel"}, "columns_mobile": {"label": "Número de colunas em dispositivo móvel", "options__1": {"label": "1 coluna"}, "options__2": {"label": "2 colunas"}}, "filter_type": {"label": "Esquema de filtro para desktop", "options__1": {"label": "Horizontal"}, "options__2": {"label": "Vertical"}, "options__3": {"label": "Gaveta"}, "info": "A gaveta é o esquema padrão para dispositivos móveis."}}}, "main-list-collections": {"name": "Página da lista de coleções", "settings": {"title": {"label": "<PERSON><PERSON><PERSON><PERSON>"}, "sort": {"label": "Ordenar coleções por:", "options__1": {"label": "Alfabeticamente, A-Z"}, "options__2": {"label": "Alfabeticamente, Z-A"}, "options__3": {"label": "<PERSON>, mais recentes"}, "options__4": {"label": "<PERSON>, mais antigos"}, "options__5": {"label": "Contagem de produtos, alta para baixa"}, "options__6": {"label": "Contagem de produtos, baixa para alta"}}, "image_ratio": {"label": "Proporção de imagem", "options__1": {"label": "Ada<PERSON>r à imagem"}, "options__2": {"label": "Retrato"}, "options__3": {"label": "Quadrado"}, "info": "Adicione imagens ao editar as suas coleções. [<PERSON><PERSON> mais](https://help.shopify.com/manual/products/collections)"}, "columns_desktop": {"label": "Número de colunas no ambiente de trabalho"}, "header_mobile": {"content": "Esquema para dispositivo móvel"}, "columns_mobile": {"label": "Número de colunas em dispositivo móvel", "options__1": {"label": "1 coluna"}, "options__2": {"label": "2 colunas"}}}}, "main-page": {"name": "<PERSON><PERSON><PERSON><PERSON>"}, "main-password-footer": {"name": "Rodapé de palavra-passe"}, "main-password-header": {"name": "Cabeçalho de palavra-passe", "settings": {"logo_header": {"content": "Logótipo"}, "logo_help": {"content": "Edite o seu logótipo nas definições de tema."}}}, "main-product": {"name": "Informações do produto", "blocks": {"text": {"settings": {"text": {"label": "Texto"}, "text_style": {"label": "Estilo de texto", "options__1": {"label": "Corpo"}, "options__2": {"label": "<PERSON>a"}, "options__3": {"label": "Mai<PERSON><PERSON><PERSON>"}}}, "name": "Texto"}, "variant_picker": {"name": "Se<PERSON>or de variante", "settings": {"picker_type": {"label": "<PERSON><PERSON><PERSON>", "options__1": {"label": "Pendente"}, "options__2": {"label": "Comprimidos"}}, "swatch_shape": {"label": "<PERSON><PERSON><PERSON>", "info": "Ative [paletas](https://help.shopify.com/en/manual/online-store/themes/theme-structure/theme-settings#options-with-swatches) nas opções de produto.", "options__1": {"label": "<PERSON><PERSON><PERSON><PERSON>"}, "options__2": {"label": "Quadrado"}, "options__3": {"label": "<PERSON><PERSON><PERSON>"}}}}, "buy_buttons": {"settings": {"show_dynamic_checkout": {"label": "Mostrar botões dinâmicos de finalização da compra", "info": "Utilizando os métodos de pagamento disponíveis na sua loja, os clientes poderão ver a sua opção preferida, como o PayPal ou Apple Pay. [<PERSON><PERSON> mais](https://help.shopify.com/manual/using-themes/change-the-layout/dynamic-checkout)"}, "show_gift_card_recipient": {"label": "Apresentar o formulário de informações do destinatário para cartões de oferta", "info": "Permite aos compradores enviar cartões de oferta numa data agendada juntamente com uma mensagem pessoal. [<PERSON><PERSON> mais](https://help.shopify.com/manual/online-store/themes/customizing-themes/add-gift-card-recipient-fields)"}}, "name": "Botão de compra"}, "share": {"settings": {"featured_image_info": {"content": "Se incluir uma ligação nas publicações das redes sociais, a imagem em destaque da página será demonstrada como a imagem de pré-visualização. [<PERSON><PERSON> ma<PERSON>](https://help.shopify.com/manual/online-store/images/showing-social-media-thumbnail-images)."}, "title_info": {"content": "É incluído um título de loja e descrição com a imagem de pré-visualização. [Saber mais](https://help.shopify.com/manual/promoting-marketing/seo/adding-keywords#set-a-title-and-description-for-your-online-store)."}, "text": {"label": "Texto"}}, "name": "Partilhar"}, "collapsible_tab": {"settings": {"heading": {"info": "Inclua um título que explique o conteúdo.", "label": "<PERSON><PERSON><PERSON><PERSON>"}, "content": {"label": "<PERSON><PERSON><PERSON><PERSON>"}, "page": {"label": "<PERSON><PERSON><PERSON><PERSON> da linha da página"}, "icon": {"label": "Ícone", "options__1": {"label": "<PERSON><PERSON><PERSON>"}, "options__2": {"label": "Maçã"}, "options__3": {"label": "Banana"}, "options__4": {"label": "<PERSON><PERSON><PERSON>"}, "options__5": {"label": "Caixa"}, "options__6": {"label": "<PERSON><PERSON><PERSON>"}, "options__7": {"label": "<PERSON><PERSON><PERSON> de conversa"}, "options__8": {"label": "Marca de verificação"}, "options__9": {"label": "Pranchet<PERSON>"}, "options__10": {"label": "<PERSON><PERSON><PERSON> l<PERSON>"}, "options__11": {"label": "Sem produtos lácteos"}, "options__12": {"label": "Secador"}, "options__13": {"label": "<PERSON><PERSON><PERSON>"}, "options__14": {"label": "Fogo"}, "options__15": {"label": "<PERSON><PERSON>"}, "options__16": {"label": "Coração"}, "options__17": {"label": "<PERSON>rro"}, "options__18": {"label": "Fol<PERSON>"}, "options__19": {"label": "<PERSON><PERSON>"}, "options__20": {"label": "Relâmpago"}, "options__21": {"label": "<PERSON><PERSON>"}, "options__22": {"label": "Cadeado"}, "options__23": {"label": "Marcador de mapa"}, "options__24": {"label": "Sem frutos de casca rija"}, "options__25": {"label": "Calças"}, "options__26": {"label": "<PERSON><PERSON> de <PERSON>a"}, "options__27": {"label": "Pimenta"}, "options__28": {"label": "Perfume"}, "options__29": {"label": "Avião"}, "options__30": {"label": "Planta"}, "options__31": {"label": "Etiqueta de preço"}, "options__32": {"label": "Ponto de interrogação"}, "options__33": {"label": "Reciclar"}, "options__34": {"label": "Devolução"}, "options__35": {"label": "Régua"}, "options__36": {"label": "Prato"}, "options__37": {"label": "<PERSON><PERSON>"}, "options__38": {"label": "Sapato"}, "options__39": {"label": "<PERSON><PERSON><PERSON><PERSON>"}, "options__40": {"label": "Floco de neve"}, "options__41": {"label": "Estrela"}, "options__42": {"label": "Cronómetro"}, "options__43": {"label": "Camião"}, "options__44": {"label": "<PERSON><PERSON>"}}}, "name": "<PERSON><PERSON> re<PERSON>"}, "popup": {"settings": {"link_label": {"label": "Etiqueta de ligação"}, "page": {"label": "<PERSON><PERSON><PERSON><PERSON>"}}, "name": "Pop-up"}, "title": {"name": "<PERSON><PERSON><PERSON><PERSON>"}, "price": {"name": "Preço"}, "quantity_selector": {"name": "Se<PERSON>or de quantidade"}, "pickup_availability": {"name": "Disponibilidade de recolha"}, "description": {"name": "Descrição"}, "rating": {"name": "Classificação do produto", "settings": {"paragraph": {"content": "Para mostrar uma classificação, adicione uma aplicação de classificação de produto. [<PERSON><PERSON> mais](https://help.shopify.com/manual/online-store/themes/theme-structure/page-types#product-rating-block)"}}}, "complementary_products": {"name": "<PERSON><PERSON><PERSON>", "settings": {"paragraph": {"content": "Para selecionar produtos complementares, adicione a aplicação Search & Discovery. [<PERSON><PERSON> mais](https://help.shopify.com/manual/online-store/search-and-discovery/product-recommendations)"}, "heading": {"label": "<PERSON><PERSON><PERSON><PERSON>"}, "make_collapsible_row": {"label": "Mostrar como linha recolhível"}, "icon": {"info": "Visível quando é mostrada a linha recolhível."}, "product_list_limit": {"label": "Máximo de produtos a apresentar"}, "products_per_page": {"label": "Número de produtos por página"}, "pagination_style": {"label": "Estilo de paginação", "options": {"option_1": "Pontos", "option_2": "<PERSON><PERSON><PERSON>", "option_3": "Números"}}, "product_card": {"heading": "Cartão de produtos"}, "image_ratio": {"label": "Proporção de imagem", "options": {"option_1": "Retrato", "option_2": "Quadrado"}}, "enable_quick_add": {"label": "Ativar botão para adicionar rapidamente"}}}, "icon_with_text": {"name": "Ícone com texto", "settings": {"layout": {"label": "Esquema", "options__1": {"label": "Horizontal"}, "options__2": {"label": "Vertical"}}, "content": {"label": "<PERSON><PERSON><PERSON><PERSON>", "info": "Escolha um ícone ou adicione uma imagem para cada coluna ou linha."}, "heading": {"info": "Deixe a etiqueta do título em branco para ocultar a coluna do ícone."}, "icon_1": {"label": "<PERSON><PERSON>"}, "image_1": {"label": "Primeira imagem"}, "heading_1": {"label": "<PERSON><PERSON> tí<PERSON>"}, "icon_2": {"label": "<PERSON><PERSON><PERSON>"}, "image_2": {"label": "Segunda imagem"}, "heading_2": {"label": "<PERSON><PERSON><PERSON>"}, "icon_3": {"label": "<PERSON><PERSON><PERSON>"}, "image_3": {"label": "Terceira imagem"}, "heading_3": {"label": "<PERSON><PERSON><PERSON>"}}}, "sku": {"name": "SKU", "settings": {"text_style": {"label": "Estilo de texto", "options__1": {"label": "Corpo"}, "options__2": {"label": "<PERSON>a"}, "options__3": {"label": "Mai<PERSON><PERSON><PERSON>"}}}}, "inventory": {"name": "Estado do inventário", "settings": {"text_style": {"label": "Estilo de texto", "options__1": {"label": "Corpo"}, "options__2": {"label": "<PERSON>a"}, "options__3": {"label": "Mai<PERSON><PERSON><PERSON>"}}, "inventory_threshold": {"label": "Limite do inventário baixo", "info": "Escolha 0 para mostrar sempre em stock, se disponível."}, "show_inventory_quantity": {"label": "Mostrar contagem de inventário"}}}}, "settings": {"header": {"content": "Conteúdo multimédia", "info": "Saiba mais sobre [tipos de média.](https://help.shopify.com/manual/products/product-media)"}, "enable_video_looping": {"label": "Ativar ciclo de vídeo"}, "enable_sticky_info": {"label": "Ativar conteúdo fixo no desktop"}, "hide_variants": {"label": "Ocultar outro conteúdo multimédia das variantes após selecionar uma variante"}, "gallery_layout": {"label": "Esquema de desktop", "options__1": {"label": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "options__2": {"label": "<PERSON><PERSON> colu<PERSON>"}, "options__3": {"label": "Miniaturas"}, "options__4": {"label": "Carrossel de miniaturas"}}, "media_size": {"label": "Largura do conteúdo multimédia para computador", "options__1": {"label": "Pequeno"}, "options__2": {"label": "Médio"}, "options__3": {"label": "Grande"}, "info": "Os conteúdos multimédia são automaticamente otimizados para dispositivos móveis."}, "mobile_thumbnails": {"label": "Esquema móvel", "options__1": {"label": "<PERSON><PERSON> colu<PERSON>"}, "options__2": {"label": "Mostrar miniaturas"}, "options__3": {"label": "Ocultar miniaturas"}}, "media_position": {"label": "Posição do conteúdo multimédia no ambiente de trabalho", "info": "A posição é automaticamente otimizada para dispositivos móveis.", "options__1": {"label": "E<PERSON>rda"}, "options__2": {"label": "<PERSON><PERSON><PERSON>"}}, "image_zoom": {"label": "<PERSON><PERSON> da <PERSON>m", "info": "Clique e passe o cursor para abrir a janela modal (lightbox) no telemóvel.", "options__1": {"label": "Abrir janela modal (lightbox)"}, "options__2": {"label": "Clicar e passar o cursor"}, "options__3": {"label": "Sem zoom"}}, "constrain_to_viewport": {"label": "Ajustar conteúdo multimédia ao ecrã"}, "media_fit": {"label": "Ajuste do conteúdo multimédia", "options__1": {"label": "Original"}, "options__2": {"label": "<PERSON><PERSON><PERSON>"}}}}, "main-search": {"name": "Resultados da pesquisa", "settings": {"image_ratio": {"label": "Proporção de imagem", "options__1": {"label": "Ada<PERSON>r à imagem"}, "options__2": {"label": "Retrato"}, "options__3": {"label": "Quadrado"}}, "show_secondary_image": {"label": "Mostrar a segunda imagem ao passar o rato"}, "show_vendor": {"label": "<PERSON><PERSON> forne<PERSON>or"}, "header__1": {"content": "Cartão de produtos"}, "header__2": {"content": "Cartão de blogue", "info": "Os estilos de cartões de blogue são também aplicáveis aos cartões de páginas nos resultados de pesquisa. Para alterar os estilos de cartões, atualize as suas definições do tema."}, "article_show_date": {"label": "Mostrar data"}, "article_show_author": {"label": "Mostrar autor"}, "show_rating": {"label": "Mostrar classificação do produto", "info": "Para mostrar uma classificação, adicione uma aplicação de classificação de produto. [<PERSON><PERSON> mais](https://help.shopify.com/manual/online-store/themes/theme-structure/page-types#search-results-section-settings)"}, "columns_desktop": {"label": "Número de colunas no ambiente de trabalho"}, "header_mobile": {"content": "Esquema para dispositivo móvel"}, "columns_mobile": {"label": "Número de colunas em dispositivo móvel", "options__1": {"label": "1 coluna"}, "options__2": {"label": "2 colunas"}}}}, "multicolumn": {"name": "<PERSON><PERSON><PERSON><PERSON>", "settings": {"title": {"label": "<PERSON><PERSON><PERSON><PERSON>"}, "image_width": {"label": "<PERSON><PERSON><PERSON> da <PERSON>", "options__1": {"label": "Um terço da largura da coluna"}, "options__2": {"label": "Metade da largura da coluna"}, "options__3": {"label": "Largura total da coluna"}}, "image_ratio": {"label": "Proporção de imagem", "options__1": {"label": "Ada<PERSON>r à imagem"}, "options__2": {"label": "Retrato"}, "options__3": {"label": "Quadrado"}, "options__4": {"label": "<PERSON><PERSON><PERSON><PERSON>"}}, "column_alignment": {"label": "Alinhamento de colunas", "options__1": {"label": "E<PERSON>rda"}, "options__2": {"label": "Centro"}}, "background_style": {"label": "Fundo secundário", "options__1": {"label": "<PERSON><PERSON><PERSON>"}, "options__2": {"label": "Mostrar como fundo da coluna"}}, "button_label": {"label": "Etiqueta do botão"}, "button_link": {"label": "Ligação do botão"}, "swipe_on_mobile": {"label": "Ativar leitura magnética no dispositivo móvel"}, "columns_desktop": {"label": "Número de colunas no ambiente de trabalho"}, "header_mobile": {"content": "Esquema para dispositivo móvel"}, "columns_mobile": {"label": "Número de colunas em dispositivo móvel", "options__1": {"label": "1 coluna"}, "options__2": {"label": "2 colunas"}}}, "blocks": {"column": {"settings": {"image": {"label": "Imagem"}, "title": {"label": "<PERSON><PERSON><PERSON><PERSON>"}, "text": {"label": "Descrição"}, "link_label": {"label": "Etiqueta de ligação"}, "link": {"label": "Ligação"}}, "name": "Coluna"}}, "presets": {"name": "<PERSON><PERSON><PERSON><PERSON>"}}, "newsletter": {"name": "Registo de e-mail", "settings": {"full_width": {"label": "Tornar a secção em largura total"}, "paragraph": {"content": "Cada subscrição de e-mail cria uma conta de cliente. [<PERSON><PERSON> mais](https://help.shopify.com/manual/customers)"}}, "blocks": {"heading": {"settings": {"heading": {"label": "<PERSON><PERSON><PERSON><PERSON>"}}, "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "paragraph": {"settings": {"paragraph": {"label": "Descrição"}}, "name": "Subtítulo"}, "email_form": {"name": "Formulário de e-mail"}}, "presets": {"name": "Registo de e-mail"}}, "page": {"name": "<PERSON><PERSON><PERSON><PERSON>", "settings": {"page": {"label": "<PERSON><PERSON><PERSON><PERSON>"}}, "presets": {"name": "<PERSON><PERSON><PERSON><PERSON>"}}, "rich-text": {"name": "Texto formatado", "settings": {"full_width": {"label": "Tornar a secção em largura total"}, "desktop_content_position": {"options__1": {"label": "E<PERSON>rda"}, "options__2": {"label": "Ao centro"}, "options__3": {"label": "<PERSON><PERSON><PERSON>"}, "label": "Posição do conteúdo para computador", "info": "A posição é automaticamente otimizada para dispositivos móveis."}, "content_alignment": {"options__1": {"label": "E<PERSON>rda"}, "options__2": {"label": "Ao centro"}, "options__3": {"label": "<PERSON><PERSON><PERSON>"}, "label": "Alinhamento de conteúdo"}}, "blocks": {"heading": {"settings": {"heading": {"label": "<PERSON><PERSON><PERSON><PERSON>"}}, "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "text": {"settings": {"text": {"label": "Descrição"}}, "name": "Texto"}, "buttons": {"settings": {"button_label_1": {"label": "Primeira etiqueta do botão", "info": "Deixe a etiqueta em branco para ocultar o botão."}, "button_link_1": {"label": "Primeira ligação do botão"}, "button_style_secondary_1": {"label": "Utilizar estilo de botão de contorno"}, "button_label_2": {"label": "Segunda etiqueta do botão", "info": "Deixe a etiqueta em branco para ocultar o botão."}, "button_link_2": {"label": "Segunda ligação do botão"}, "button_style_secondary_2": {"label": "Utilizar estilo de botão de contorno"}}, "name": "<PERSON><PERSON><PERSON><PERSON>"}, "caption": {"name": "<PERSON>a", "settings": {"text": {"label": "Texto"}, "text_style": {"label": "Estilo de texto", "options__1": {"label": "<PERSON>a"}, "options__2": {"label": "Mai<PERSON><PERSON><PERSON>"}}, "caption_size": {"label": "Tamanho do texto", "options__1": {"label": "Pequeno"}, "options__2": {"label": "Médio"}, "options__3": {"label": "Grande"}}}}}, "presets": {"name": "Texto formatado"}}, "apps": {"name": "Aplicações", "settings": {"include_margins": {"label": "<PERSON><PERSON><PERSON> as margen<PERSON> de sec<PERSON> as mesmas que o tema"}}, "presets": {"name": "Aplicações"}}, "video": {"name": "Vídeo", "settings": {"heading": {"label": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "cover_image": {"label": "<PERSON><PERSON> de capa"}, "video_url": {"label": "URL", "info": "Usar um URL do YouTube ou Vimeo"}, "description": {"label": "Texto alternativo do vídeo", "info": "Descreve o vídeo para que seja acessível a clientes que usam leitores de ecrã. [<PERSON><PERSON> mais](https://help.shopify.com/manual/online-store/themes/theme-structure/theme-features#video)"}, "image_padding": {"label": "Adicionar preenchimento de imagem", "info": "Selecione um preenchimento de imagem se não quer que a sua imagem de capa seja cortada."}, "full_width": {"label": "Tornar a secção em largura total"}, "video": {"label": "Vídeo"}, "enable_video_looping": {"label": "Repetir continuamente o vídeo"}, "header__1": {"content": "Vídeo hospedado na Shopify"}, "header__2": {"content": "Ou incorporar vídeo a partir de URL"}, "header__3": {"content": "<PERSON><PERSON><PERSON>"}, "paragraph": {"content": "Exibido quando nenhum vídeo hospedado na Shopify está selecionado."}}, "presets": {"name": "Vídeo"}}, "featured-product": {"name": "Produto em destaque", "blocks": {"text": {"name": "Texto", "settings": {"text": {"label": "Texto"}, "text_style": {"label": "Estilo de texto", "options__1": {"label": "Corpo"}, "options__2": {"label": "<PERSON>a"}, "options__3": {"label": "Mai<PERSON><PERSON><PERSON>"}}}}, "title": {"name": "<PERSON><PERSON><PERSON><PERSON>"}, "price": {"name": "Preço"}, "quantity_selector": {"name": "Se<PERSON>or de quantidade"}, "variant_picker": {"name": "Se<PERSON>or de variante", "settings": {"picker_type": {"label": "<PERSON><PERSON><PERSON>", "options__1": {"label": "Pendente"}, "options__2": {"label": "Forma de comprimidos"}}, "swatch_shape": {"label": "<PERSON><PERSON><PERSON>", "info": "Ative [paletas](https://help.shopify.com/en/manual/online-store/themes/theme-structure/theme-settings#options-with-swatches) nas opções de produto.", "options__1": {"label": "<PERSON><PERSON><PERSON><PERSON>"}, "options__2": {"label": "Quadrado"}, "options__3": {"label": "<PERSON><PERSON><PERSON>"}}}}, "buy_buttons": {"name": "Botão de compra", "settings": {"show_dynamic_checkout": {"label": "Mostrar botões dinâmicos de finalização da compra", "info": "Utilizando os métodos de pagamento disponíveis na sua loja, os clientes poderão ver a sua opção preferida, como o PayPal ou Apple Pay. [<PERSON>ber mais](https://help.shopify.com/manual/using-themes/change-the-layout/dynamic-checkout)"}}}, "description": {"name": "Descrição"}, "share": {"name": "Partilhar", "settings": {"featured_image_info": {"content": "Se incluir uma ligação nas publicações das redes sociais, a imagem em destaque da página será demonstrada como a imagem de pré-visualização. [<PERSON><PERSON> ma<PERSON>](https://help.shopify.com/manual/online-store/images/showing-social-media-thumbnail-images)"}, "title_info": {"content": "É incluído um título de loja e descrição com a imagem de pré-visualização. [Saber mais](https://help.shopify.com/manual/promoting-marketing/seo/adding-keywords#set-a-title-and-description-for-your-online-store)"}, "text": {"label": "Texto"}}}, "rating": {"name": "Classificação do produto", "settings": {"paragraph": {"content": "Para mostrar uma classificação, adicione uma aplicação de classificação de produto. [<PERSON><PERSON> mais](https://help.shopify.com/manual/online-store/themes/theme-structure/theme-features#featured-product-rating)"}}}, "sku": {"name": "SKU", "settings": {"text_style": {"label": "Estilo de texto", "options__1": {"label": "Corpo"}, "options__2": {"label": "<PERSON>a"}, "options__3": {"label": "Mai<PERSON><PERSON><PERSON>"}}}}}, "settings": {"product": {"label": "Produ<PERSON>"}, "secondary_background": {"label": "Mostrar fundo secundário"}, "header": {"content": "Conteúdo multimédia", "info": "Saiba mais sobre [tipos de conteúdo multimédia](https://help.shopify.com/manual/products/product-media)"}, "enable_video_looping": {"label": "Ativar ciclo de vídeo"}, "hide_variants": {"label": "Ocultar conteúdo multimédia das variantes não selecionadas no desktop"}, "media_position": {"label": "Posição do conteúdo multimédia no ambiente de trabalho", "info": "A posição é automaticamente otimizada para dispositivos móveis.", "options__1": {"label": "E<PERSON>rda"}, "options__2": {"label": "<PERSON><PERSON><PERSON>"}}}, "presets": {"name": "Produto em destaque"}}, "email-signup-banner": {"name": "Faixa de registo de e-mail", "settings": {"paragraph": {"content": "Cada subscrição de e-mail cria uma conta de cliente. [<PERSON><PERSON> mais](https://help.shopify.com/manual/customers)"}, "image": {"label": "Imagem de fundo"}, "show_background_image": {"label": "Mostrar imagem de fundo"}, "show_text_box": {"label": "Mostrar recetores no desktop"}, "image_overlay_opacity": {"label": "Opacidade da sobreposição da imagem"}, "show_text_below": {"label": "Mostrar conteúdo por baixo da imagem em dispositivos móveis", "info": "Para obter os melhores resultados, utilize uma imagem com uma proporção de 16:9. [<PERSON><PERSON> mais](https://help.shopify.com/manual/shopify-admin/productivity-tools/image-editor#understanding-image-aspect-ratio)"}, "image_height": {"label": "Altura da faixa", "options__1": {"label": "Ada<PERSON>r à imagem"}, "options__2": {"label": "Pequeno"}, "options__3": {"label": "Médio"}, "options__4": {"label": "Grande"}, "info": "Para obter os melhores resultados, utilize uma imagem com uma proporção de 16:9. [<PERSON><PERSON> mais](https://help.shopify.com/manual/shopify-admin/productivity-tools/image-editor#understanding-image-aspect-ratio)"}, "desktop_content_position": {"options__1": {"label": "Canto superior esquerdo"}, "options__2": {"label": "Superior centro"}, "options__3": {"label": "Canto superior direito"}, "options__4": {"label": "Intermédio à esquerda"}, "options__5": {"label": "Intermédio ao centro"}, "options__6": {"label": "Intermédio à direita"}, "options__7": {"label": "Canto inferior esquerdo"}, "options__8": {"label": "Inferior centro"}, "options__9": {"label": "Canto inferior direito"}, "label": "Posição do conteúdo em computadores"}, "desktop_content_alignment": {"options__1": {"label": "E<PERSON>rda"}, "options__2": {"label": "Ao centro"}, "options__3": {"label": "<PERSON><PERSON><PERSON>"}, "label": "Alinhamento do conteúdo em desktop"}, "header": {"content": "Esquema para dispositivo móvel"}, "mobile_content_alignment": {"options__1": {"label": "E<PERSON>rda"}, "options__2": {"label": "Ao centro"}, "options__3": {"label": "<PERSON><PERSON><PERSON>"}, "label": "Alinhamento do conteúdo em dispositivos móveis"}, "color_scheme": {"info": "Visível quando o recetor é exibido."}}, "blocks": {"heading": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "settings": {"heading": {"label": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}}}, "paragraph": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "settings": {"paragraph": {"label": "Descrição"}, "text_style": {"options__1": {"label": "Corpo"}, "options__2": {"label": "<PERSON>a"}, "label": "Estilo de texto"}}}, "email_form": {"name": "Formulário de e-mail"}}, "presets": {"name": "Faixa de registo de e-mail"}}, "slideshow": {"name": "Apresentação de diapositivos", "settings": {"layout": {"label": "Esquema", "options__1": {"label": "Largura total"}, "options__2": {"label": "Grelha"}}, "slide_height": {"label": "Altura do diapositivo", "options__1": {"label": "Adaptar à primeira imagem"}, "options__2": {"label": "Pequeno"}, "options__3": {"label": "Médio"}, "options__4": {"label": "Grande"}}, "slider_visual": {"label": "Estilo de paginação", "options__1": {"label": "<PERSON><PERSON><PERSON>"}, "options__2": {"label": "Pontos"}, "options__3": {"label": "Números"}}, "auto_rotate": {"label": "Rotação automática de diapositivos"}, "change_slides_speed": {"label": "Mudar diapositivos a cada"}, "mobile": {"content": "Esquema móvel"}, "show_text_below": {"label": "Mostrar conteúdo abaixo das imagens em dispositivos móveis"}, "accessibility": {"content": "Acessibilidade", "label": "Descrição da apresentação de diapositivos", "info": "Descreve a apresentação de diapositivos para que seja acessível a clientes que usam leitores de ecrã."}}, "blocks": {"slide": {"name": "Diapositivo", "settings": {"image": {"label": "Imagem"}, "heading": {"label": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "subheading": {"label": "Subtítulo"}, "button_label": {"label": "Etiqueta do botão", "info": "Deixe a etiqueta em branco para ocultar o botão."}, "link": {"label": "Ligação do botão"}, "secondary_style": {"label": "Utilizar estilo de botão de contorno"}, "box_align": {"label": "Posição do conteúdo em desktop", "options__1": {"label": "Canto superior esquerdo"}, "options__2": {"label": "Superior centro"}, "options__3": {"label": "Canto superior direito"}, "options__4": {"label": "Intermédio à esquerda"}, "options__5": {"label": "Intermédio ao centro"}, "options__6": {"label": "Intermédio à direita"}, "options__7": {"label": "Canto inferior esquerdo"}, "options__8": {"label": "Inferior centro"}, "options__9": {"label": "Canto inferior direito"}, "info": "Posição é automaticamente otimizada para dispositivos móveis."}, "show_text_box": {"label": "Mostrar recetores no desktop"}, "text_alignment": {"label": "Alinhamento do conteúdo em desktop", "option_1": {"label": "E<PERSON>rda"}, "option_2": {"label": "Ao centro"}, "option_3": {"label": "<PERSON><PERSON><PERSON>"}}, "image_overlay_opacity": {"label": "Opacidade da sobreposição da imagem"}, "text_alignment_mobile": {"label": "Alinhamento do conteúdo em dispositivos móveis", "options__1": {"label": "E<PERSON>rda"}, "options__2": {"label": "Ao centro"}, "options__3": {"label": "<PERSON><PERSON><PERSON>"}}}}}, "presets": {"name": "Apresentação de diapositivos"}}, "collapsible_content": {"name": "<PERSON><PERSON><PERSON><PERSON>", "settings": {"caption": {"label": "<PERSON>a"}, "heading": {"label": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "heading_alignment": {"label": "Alinhamento do título", "options__1": {"label": "E<PERSON>rda"}, "options__2": {"label": "Ao centro"}, "options__3": {"label": "<PERSON><PERSON><PERSON>"}}, "layout": {"label": "Esquema", "options__1": {"label": "<PERSON><PERSON><PERSON> contentor"}, "options__2": {"label": "<PERSON><PERSON>"}, "options__3": {"label": "Contentor de se<PERSON>ção"}}, "container_color_scheme": {"label": "Esquema de cores do contentor", "info": "Visível quando o esquema está definido para contentor de linha ou de secção."}, "open_first_collapsible_row": {"label": "Abrir primeira linha recolhível"}, "header": {"content": "Esquema de imagem"}, "image": {"label": "Imagem"}, "image_ratio": {"label": "Proporção de imagem", "options__1": {"label": "Ada<PERSON>r à imagem"}, "options__2": {"label": "Pequeno"}, "options__3": {"label": "Grande"}}, "desktop_layout": {"label": "Esquema de desktop", "options__1": {"label": "Primeira imagem"}, "options__2": {"label": "Segunda imagem"}, "info": "A imagem sempre aparece sempre primeiro nos dispositivos móveis."}}, "blocks": {"collapsible_row": {"name": "<PERSON><PERSON> re<PERSON>", "settings": {"heading": {"info": "Inclua um título que explique o conteúdo.", "label": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "row_content": {"label": "<PERSON><PERSON><PERSON><PERSON>"}, "page": {"label": "<PERSON><PERSON><PERSON><PERSON> da linha da página"}, "icon": {"label": "Ícone", "options__1": {"label": "<PERSON><PERSON><PERSON>"}, "options__2": {"label": "Maçã"}, "options__3": {"label": "Banana"}, "options__4": {"label": "<PERSON><PERSON><PERSON>"}, "options__5": {"label": "Caixa"}, "options__6": {"label": "<PERSON><PERSON><PERSON>"}, "options__7": {"label": "<PERSON><PERSON><PERSON> de conversa"}, "options__8": {"label": "Marca de verificação"}, "options__9": {"label": "Pranchet<PERSON>"}, "options__10": {"label": "<PERSON><PERSON><PERSON> l<PERSON>"}, "options__11": {"label": "Sem produtos lácteos"}, "options__12": {"label": "Secador"}, "options__13": {"label": "<PERSON><PERSON><PERSON>"}, "options__14": {"label": "Fogo"}, "options__15": {"label": "<PERSON><PERSON>"}, "options__16": {"label": "Coração"}, "options__17": {"label": "<PERSON>rro"}, "options__18": {"label": "Fol<PERSON>"}, "options__19": {"label": "<PERSON><PERSON>"}, "options__20": {"label": "Relâmpago"}, "options__21": {"label": "<PERSON><PERSON>"}, "options__22": {"label": "Cadeado"}, "options__23": {"label": "Marcador de mapa"}, "options__24": {"label": "Sem frutos de casca rija"}, "options__25": {"label": "Calças"}, "options__26": {"label": "<PERSON><PERSON> de <PERSON>a"}, "options__27": {"label": "Pimenta"}, "options__28": {"label": "Perfume"}, "options__29": {"label": "Avião"}, "options__30": {"label": "Planta"}, "options__31": {"label": "Etiqueta de preço"}, "options__32": {"label": "Ponto de interrogação"}, "options__33": {"label": "Reciclar"}, "options__34": {"label": "Devolução"}, "options__35": {"label": "Régua"}, "options__36": {"label": "Prato"}, "options__37": {"label": "<PERSON><PERSON>"}, "options__38": {"label": "Sapato"}, "options__39": {"label": "<PERSON><PERSON><PERSON><PERSON>"}, "options__40": {"label": "Floco de neve"}, "options__41": {"label": "Estrela"}, "options__42": {"label": "Cronómetro"}, "options__43": {"label": "Camião"}, "options__44": {"label": "<PERSON><PERSON>"}}}}}, "presets": {"name": "<PERSON><PERSON><PERSON><PERSON>"}}, "main-account": {"name": "Conta"}, "main-activate-account": {"name": "Ativação de conta"}, "main-addresses": {"name": "Endereços"}, "main-login": {"name": "<PERSON><PERSON><PERSON>"}, "main-order": {"name": "Encomenda"}, "main-register": {"name": "Registo"}, "main-reset-password": {"name": "Redefinição de palavra-passe"}, "related-products": {"name": "Produtos relacionados", "settings": {"heading": {"label": "<PERSON><PERSON><PERSON><PERSON>"}, "products_to_show": {"label": "Máximo de produtos a apresentar"}, "columns_desktop": {"label": "Número de colunas no computador"}, "paragraph__1": {"content": "As recomendações dinâmicas utilizam informações de encomenda e de produto para mudar e melhorar ao longo do tempo. [Saber mais](https://help.shopify.com/themes/development/recommended-products)"}, "header__2": {"content": "Cartão de produtos"}, "image_ratio": {"label": "Proporção de imagem", "options__1": {"label": "Ada<PERSON>r à imagem"}, "options__2": {"label": "Retrato"}, "options__3": {"label": "Quadrado"}}, "show_secondary_image": {"label": "Mostrar a segunda imagem ao passar o rato"}, "show_vendor": {"label": "<PERSON><PERSON> forne<PERSON>or"}, "show_rating": {"label": "Mostrar classificação do produto", "info": "Para mostrar uma classificação, adicione uma aplicação de classificação de produto. [<PERSON><PERSON> mais](https://help.shopify.com/manual/online-store/themes/theme-structure/page-types#product-recommendations-section-settings)"}, "header_mobile": {"content": "Esquema para dispositivo móvel"}, "columns_mobile": {"label": "Número de colunas em dispositivo móvel", "options__1": {"label": "1 coluna"}, "options__2": {"label": "2 colunas"}}}}, "multirow": {"name": "<PERSON><PERSON><PERSON><PERSON> l<PERSON>", "settings": {"image": {"label": "Imagem"}, "image_height": {"options__1": {"label": "Ada<PERSON>r à imagem"}, "options__2": {"label": "Pequeno"}, "options__3": {"label": "Médio"}, "options__4": {"label": "Grande"}, "label": "<PERSON><PERSON> da imagem"}, "desktop_image_width": {"options__1": {"label": "Pequeno"}, "options__2": {"label": "Médio"}, "options__3": {"label": "Grande"}, "label": "<PERSON><PERSON><PERSON> da imagem em computador", "info": "A imagem é otimizada automaticamente em dispositivos móveis."}, "heading_size": {"options__1": {"label": "Pequeno"}, "options__2": {"label": "Médio"}, "options__3": {"label": "Grande"}, "label": "Tamanho do título"}, "text_style": {"options__1": {"label": "Corpo"}, "options__2": {"label": "<PERSON>a"}, "label": "Estilo de texto"}, "button_style": {"options__1": {"label": "Botão sólido"}, "options__2": {"label": "Botão de contorno"}, "label": "Estilo do botão"}, "desktop_content_alignment": {"options__1": {"label": "E<PERSON>rda"}, "options__2": {"label": "Centro"}, "options__3": {"label": "<PERSON><PERSON><PERSON>"}, "label": "Alinhamento do conteúdo em computador"}, "desktop_content_position": {"options__1": {"label": "Em cima"}, "options__2": {"label": "Ao meio"}, "options__3": {"label": "<PERSON> baixo"}, "label": "Posição do conteúdo para computador", "info": "A posição é automaticamente otimizada para dispositivos móveis."}, "image_layout": {"options__1": {"label": "Alternar da esquerda"}, "options__2": {"label": "Alternar da direita"}, "options__3": {"label": "Alinhado à esquerda"}, "options__4": {"label": "Alinhado à direita"}, "label": "Posicionamento da imagem em computador", "info": "O posicionamento é otimizado automaticamente para dispositivos móveis."}, "container_color_scheme": {"label": "Esquema de cores do contentor"}, "mobile_content_alignment": {"options__1": {"label": "E<PERSON>rda"}, "options__2": {"label": "Centro"}, "options__3": {"label": "<PERSON><PERSON><PERSON>"}, "label": "Alinhamento do conteúdo em dispositivos móveis"}, "header_mobile": {"content": "Esquema para dispositivo móvel"}}, "blocks": {"row": {"name": "<PERSON><PERSON>", "settings": {"image": {"label": "Imagem"}, "caption": {"label": "<PERSON>a"}, "heading": {"label": "<PERSON><PERSON><PERSON><PERSON>"}, "text": {"label": "Texto"}, "button_label": {"label": "Etiqueta do botão"}, "button_link": {"label": "Ligação do botão"}}}}, "presets": {"name": "<PERSON><PERSON><PERSON><PERSON> l<PERSON>"}}, "quick-order-list": {"name": "Lista de encomendas rápida", "settings": {"show_image": {"label": "Mostrar imagens"}, "show_sku": {"label": "Mostrar SKU"}}, "presets": {"name": "Lista de encomendas rápida"}}}}