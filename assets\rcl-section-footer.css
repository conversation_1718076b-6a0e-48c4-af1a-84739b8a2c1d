.footer {
  padding: 60px 0 30px;
  color: var(--footer-text-color);
  background-color: var(--footer-background-color);
  font-size: 14px;
}

.footer-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 30px;
  margin-bottom: 40px;
}

.footer-block {
  margin-bottom: 20px;
}

.footer-block__heading {
  margin-top: 0;
  margin-bottom: 20px;
  font-size: 16px;
  font-weight: 600;
  color: var(--footer-heading-color);
}

.footer-block__menu {
  list-style: none;
  padding: 0;
  margin: 0;
}

.footer-block__menu-item {
  margin-bottom: 10px;
}

.footer-block__menu-link {
  color: var(--footer-text-color);
  text-decoration: none;
  transition: color 0.2s ease;
}

.footer-block__menu-link:hover {
  color: var(--footer-link-hover-color);
}

.footer-block__text {
  margin-top: 0;
  margin-bottom: 15px;
  line-height: 1.5;
}

.footer-block__location {
  margin-bottom: 20px;
}

.footer-block__hours {
  margin-bottom: 20px;
}

.footer-block__hours-item {
  display: flex;
  justify-content: space-between;
  margin-bottom: 5px;
}

.footer-block__map {
  width: 100%;
  height: 200px;
  margin-bottom: 20px;
}

.footer-block__newsletter-form {
  display: flex;
  margin-bottom: 20px;
}

.footer-block__newsletter-input {
  flex-grow: 1;
  padding: 10px;
  border: 1px solid var(--footer-border-color);
  border-right: none;
  background-color: var(--footer-form-background);
  color: var(--footer-form-text);
}

.footer-block__newsletter-button {
  padding: 10px 15px;
  background-color: var(--footer-button-background);
  color: var(--footer-button-text);
  border: none;
  cursor: pointer;
  transition: background-color 0.2s ease;
}

.footer-block__newsletter-button:hover {
  background-color: var(--footer-button-hover-background);
}

.footer-block__social {
  display: flex;
  flex-wrap: wrap;
  gap: 15px;
  margin-bottom: 20px;
}

.footer-block__social-link {
  color: var(--footer-social-icon-color);
  transition: color 0.2s ease;
}

.footer-block__social-link:hover {
  color: var(--footer-social-icon-hover-color);
}

.footer-block__payment {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
  margin-bottom: 20px;
}

.footer-block__payment-icon {
  height: 24px;
  width: auto;
}

.footer-bottom {
  padding-top: 20px;
  border-top: 1px solid var(--footer-border-color);
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
  align-items: center;
  gap: 20px;
}

.footer-bottom__copyright {
  margin: 0;
  font-size: 12px;
}

.footer-bottom__shopify {
  font-size: 12px;
  color: var(--footer-text-color);
  text-decoration: none;
  display: flex;
  align-items: center;
}

@media screen and (max-width: 767px) {
  .footer-grid {
    grid-template-columns: 1fr;
  }
  
  .footer-bottom {
    flex-direction: column;
    align-items: flex-start;
  }
  
  .footer-block__newsletter-form {
    flex-direction: column;
  }
  
  .footer-block__newsletter-input {
    border-right: 1px solid var(--footer-border-color);
    border-bottom: none;
    margin-bottom: 10px;
  }
}