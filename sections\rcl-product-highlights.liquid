<div class="product-highlights-section {{ section.settings.background_style }}">
  <div class="product-highlights-container">
    <div class="section-header">
      <h2 class="section-title">{{ section.settings.title }}</h2>
      <div class="section-description">{{ section.settings.description }}</div>
    </div>
    
    {% if section.settings.layout == 'carousel' %}
      <div class="product-carousel">
        <div class="carousel-container" id="productCarousel">
          {%- for block in section.blocks -%}
            {%- if block.type == 'product_highlight' -%}
              <div class="carousel-slide" {{ block.shopify_attributes }}>
                <div class="product-card">
                  <div class="product-image-container">
                    {%- if block.settings.image -%}
                      {{ block.settings.image | image_url: width: 600 | image_tag: 
                        loading: 'lazy',
                        class: 'product-image',
                        alt: block.settings.product_name
                      }}
                    {%- else -%}
                      {{ 'product-1' | placeholder_svg_tag: 'product-image' }}
                    {%- endif -%}
                  </div>
                  <div class="product-info">
                    <h3 class="product-name">{{ block.settings.product_name }}</h3>
                    <div class="product-description">{{ block.settings.description }}</div>
                    
                    {%- if block.settings.show_features -%}
                      <div class="product-features">
                        {%- if block.settings.feature_1 != blank -%}
                          <div class="feature-item">
                            <span class="feature-icon">✓</span>
                            <span>{{ block.settings.feature_1 }}</span>
                          </div>
                        {%- endif -%}
                        
                        {%- if block.settings.feature_2 != blank -%}
                          <div class="feature-item">
                            <span class="feature-icon">✓</span>
                            <span>{{ block.settings.feature_2 }}</span>
                          </div>
                        {%- endif -%}
                        
                        {%- if block.settings.feature_3 != blank -%}
                          <div class="feature-item">
                            <span class="feature-icon">✓</span>
                            <span>{{ block.settings.feature_3 }}</span>
                          </div>
                        {%- endif -%}
                      </div>
                    {%- endif -%}
                    
                    {%- if block.settings.button_text != blank and block.settings.button_link != blank -%}
                      <a href="{{ block.settings.button_link }}" class="product-button">
                        {{ block.settings.button_text }}
                      </a>
                    {%- endif -%}
                  </div>
                </div>
              </div>
            {%- endif -%}
          {%- endfor -%}
        </div>
        
        <div class="carousel-controls">
          <button class="carousel-arrow prev-arrow" aria-label="Previous slide" onclick="prevSlide()">&#10094;</button>
          <button class="carousel-arrow next-arrow" aria-label="Next slide" onclick="nextSlide()">&#10095;</button>
        </div>
        
        <div class="carousel-dots" id="carouselDots">
          {%- for block in section.blocks -%}
            {%- if block.type == 'product_highlight' -%}
              <div class="carousel-dot{% if forloop.first %} active{% endif %}" onclick="goToSlide({{ forloop.index0 }})"></div>
            {%- endif -%}
          {%- endfor -%}
        </div>
      </div>
    {% else %}
      <div class="product-highlights-grid" style="--grid-columns: {{ section.settings.columns }};">
        {%- for block in section.blocks -%}
          {%- if block.type == 'product_highlight' -%}
            <div class="grid-item" {{ block.shopify_attributes }}>
              <div class="product-card">
                <div class="product-image-container">
                  {%- if block.settings.image -%}
                    {{ block.settings.image | image_url: width: 600 | image_tag: 
                      loading: 'lazy',
                      class: 'product-image',
                      alt: block.settings.product_name
                    }}
                  {%- else -%}
                    {{ 'product-1' | placeholder_svg_tag: 'product-image' }}
                  {%- endif -%}
                </div>
                <div class="product-info">
                  <h3 class="product-name">{{ block.settings.product_name }}</h3>
                  <div class="product-description">{{ block.settings.description }}</div>
                  
                  {%- if block.settings.show_features -%}
                    <div class="product-features">
                      {%- if block.settings.feature_1 != blank -%}
                        <div class="feature-item">
                          <span class="feature-icon">✓</span>
                          <span>{{ block.settings.feature_1 }}</span>
                        </div>
                      {%- endif -%}
                      
                      {%- if block.settings.feature_2 != blank -%}
                        <div class="feature-item">
                          <span class="feature-icon">✓</span>
                          <span>{{ block.settings.feature_2 }}</span>
                        </div>
                      {%- endif -%}
                      
                      {%- if block.settings.feature_3 != blank -%}
                        <div class="feature-item">
                          <span class="feature-icon">✓</span>
                          <span>{{ block.settings.feature_3 }}</span>
                        </div>
                      {%- endif -%}
                    </div>
                  {%- endif -%}
                  
                  {%- if block.settings.button_text != blank and block.settings.button_link != blank -%}
                    <a href="{{ block.settings.button_link }}" class="product-button">
                      {{ block.settings.button_text }}
                    </a>
                  {%- endif -%}
                </div>
              </div>
            </div>
          {%- endif -%}
        {%- endfor -%}
      </div>
    {% endif %}
  </div>
</div>

<script>
  let currentSlide = 0;
  const carousel = document.getElementById('productCarousel');
  const dots = document.getElementById('carouselDots').getElementsByClassName('carousel-dot');
  const slides = carousel.getElementsByClassName('carousel-slide');
  const totalSlides = slides.length;
  
  function updateCarousel() {
    const slideWidth = slides[0].offsetWidth + 30; // Width + margin
    carousel.scrollLeft = currentSlide * slideWidth;
    
    // Update active dot
    for (let i = 0; i < dots.length; i++) {
      dots[i].classList.remove('active');
    }
    dots[currentSlide].classList.add('active');
  }
  
  function nextSlide() {
    currentSlide = (currentSlide + 1) % totalSlides;
    updateCarousel();
  }
  
  function prevSlide() {
    currentSlide = (currentSlide - 1 + totalSlides) % totalSlides;
    updateCarousel();
  }
  
  function goToSlide(index) {
    currentSlide = index;
    updateCarousel();
  }
  
  // Auto-scroll carousel (optional)
  // setInterval(nextSlide, 5000);
  
  // Initialize carousel responsiveness
  window.addEventListener('resize', updateCarousel);
  
  // Initialize carousel
  updateCarousel();
</script>
{% schema %}
{
  "name": "Product Highlights",
  "class": "product-highlights-section",
  "settings": [
    {
      "type": "text",
      "id": "title",
      "label": "Section Title",
      "default": "Our Signature Products"
    },
    {
      "type": "richtext",
      "id": "description",
      "label": "Section Description",
      "default": "<p>We are proud to be the first in Ukraine to master the production of decorative coatings based on high-quality lime. The composition of these materials includes only natural ingredients and old recipes are used.</p>"
    },
    {
      "type": "select",
      "id": "background_style",
      "label": "Background Style",
      "options": [
        {
          "value": "light",
          "label": "Light"
        },
        {
          "value": "dark",
          "label": "Dark"
        },
        {
          "value": "accent",
          "label": "Accent"
        }
      ],
      "default": "light"
    },
    {
      "type": "range",
      "id": "padding_top",
      "min": 0,
      "max": 100,
      "step": 10,
      "unit": "px",
      "label": "Padding Top",
      "default": 60
    },
    {
      "type": "range",
      "id": "padding_bottom",
      "min": 0,
      "max": 100,
      "step": 10,
      "unit": "px",
      "label": "Padding Bottom",
      "default": 60
    },
    {
      "type": "select",
      "id": "layout",
      "label": "Layout",
      "options": [
        {
          "value": "carousel",
          "label": "Carousel"
        },
        {
          "value": "grid",
          "label": "Grid"
        }
      ],
      "default": "carousel"
    },
    {
      "type": "range",
      "id": "columns",
      "min": 2,
      "max": 4,
      "step": 1,
      "label": "Columns (Grid Layout)",
      "default": 3
    }
  ],
  "blocks": [
    {
      "type": "product_highlight",
      "name": "Product Highlight",
      "settings": [
        {
          "type": "image_picker",
          "id": "image",
          "label": "Product Image"
        },
        {
          "type": "text",
          "id": "product_name",
          "label": "Product Name",
          "default": "GROTTO"
        },
        {
          "type": "richtext",
          "id": "description",
          "label": "Description",
          "default": "<p>Premium decorative coating based on high-quality lime, creating elegant textured surfaces reminiscent of natural stone.</p>"
        },
        {
          "type": "checkbox",
          "id": "show_features",
          "label": "Show Features",
          "default": true
        },
        {
          "type": "text",
          "id": "feature_1",
          "label": "Feature 1",
          "default": "Natural Ingredients"
        },
        {
          "type": "text",
          "id": "feature_2",
          "label": "Feature 2",
          "default": "Eco-Friendly"
        },
        {
          "type": "text",
          "id": "feature_3",
          "label": "Feature 3",
          "default": "Durable Finish"
        },
        {
          "type": "url",
          "id": "button_link",
          "label": "Button Link"
        },
        {
          "type": "text",
          "id": "button_text",
          "label": "Button Text",
          "default": "Learn More"
        }
      ]
    }
  ],
  "presets": [
    {
      "name": "Product Highlights",
      "blocks": [
        {
          "type": "product_highlight"
        },
        {
          "type": "product_highlight"
        },
        {
          "type": "product_highlight"
        },
        {
          "type": "product_highlight"
        }
      ]
    }
  ]
}
{% endschema %}

<style>
  .product-highlights-section {
    padding-top: {{ section.settings.padding_top }}px;
    padding-bottom: {{ section.settings.padding_bottom }}px;
    overflow: hidden;
  }
  
  .product-highlights-section.light {
    background-color: #fff;
    color: #222228;
  }
  
  .product-highlights-section.dark {
    background-color: #222228;
    color: #fff;
  }
  
  .product-highlights-section.accent {
    background-color: #f9f7f2;
    color: #222228;
  }
  
  .product-highlights-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
  }
  
  .product-highlights-section .section-header {
    text-align: center;
    margin-bottom: 4rem;
  }
  
  .product-highlights-section .section-title {
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 1rem;
    position: relative;
    display: inline-block;
    margin-bottom: 2rem;
  }
  
  .product-highlights-section .section-title:after {
    content: '';
    position: absolute;
    bottom: -15px;
    left: 50%;
    transform: translateX(-50%);
    width: 80px;
    height: 3px;
    background-color: #d1b073;
  }
  
  .product-highlights-section .section-description {
    max-width: 800px;
    margin: 0 auto;
    font-size: 1.1rem;
    line-height: 1.6;
  }
  
  /* Grid Layout */
  .product-highlights-grid {
    display: grid;
    grid-template-columns: repeat(var(--grid-columns), 1fr);
    gap: 30px;
  }
  
  /* Carousel Layout */
  .product-carousel {
    position: relative;
  }
  
  .carousel-container {
    display: flex;
    overflow-x: hidden;
    scroll-behavior: smooth;
    -webkit-overflow-scrolling: touch;
    margin: 0 -15px;
    padding: 20px 15px;
  }
  
  .carousel-slide {
    flex: 0 0 calc(33.333% - 30px);
    margin: 0 15px;
    transition: transform 0.3s ease;
  }
  
  .carousel-controls {
    display: flex;
    justify-content: center;
    margin-top: 2rem;
  }
  
  .carousel-arrow {
    background-color: #d1b073;
    color: white;
    border: none;
    width: 50px;
    height: 50px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    margin: 0 10px;
    transition: all 0.2s ease;
    font-size: 1.5rem;
  }
  
  .carousel-arrow:hover {
    background-color: #222228;
  }
  
  .carousel-dots {
    display: flex;
    justify-content: center;
    margin-top: 20px;
  }
  
  .carousel-dot {
    width: 10px;
    height: 10px;
    border-radius: 50%;
    background-color: #ddd;
    margin: 0 5px;
    cursor: pointer;
    transition: all 0.2s ease;
  }
  
  .carousel-dot.active {
    background-color: #d1b073;
  }
  
  /* Product Card Styles */
  .product-card {
    background-color: white;
    border-radius: 10px;
    overflow: hidden;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.08);
    height: 100%;
    display: flex;
    flex-direction: column;
    transition: transform 0.3s ease, box-shadow 0.3s ease;
  }
  
  .product-highlights-section.dark .product-card {
    background-color: #2d2d35;
  }
  
  .product-card:hover {
    transform: translateY(-10px);
    box-shadow: 0 20px 40px rgba(209, 176, 115, 0.2);
  }
  
  .product-image-container {
    position: relative;
    padding-bottom: 75%; /* 4:3 Aspect Ratio */
    overflow: hidden;
  }
  
  .product-image {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.5s ease;
  }
  
  .product-card:hover .product-image {
    transform: scale(1.05);
  }
  
  .product-info {
    padding: 25px;
    flex-grow: 1;
    display: flex;
    flex-direction: column;
  }
  
  .product-name {
    font-size: 1.5rem;
    font-weight: 700;
    margin-bottom: 15px;
    color: #222228;
  }
  
  .product-highlights-section.dark .product-name {
    color: #fff;
  }
  
  .product-description {
    font-size: 0.95rem;
    line-height: 1.6;
    color: #666;
    margin-bottom: 20px;
  }
  
  .product-highlights-section.dark .product-description {
    color: #ccc;
  }
  
  .product-features {
    margin-top: auto;
    margin-bottom: 20px;
  }
  
  .feature-item {
    display: flex;
    align-items: center;
    margin-bottom: 10px;
    font-size: 0.9rem;
    color: #444;
  }
  
  .product-highlights-section.dark .feature-item {
    color: #bbb;
  }
  
  .feature-icon {
    margin-right: 10px;
    color: #d1b073;
    font-size: 0.8rem;
  }
  
  .product-button {
    display: inline-block;
    padding: 10px 25px;
    background-color: transparent;
    color: #222228;
    border: 2px solid #d1b073;
    border-radius: 5px;
    font-weight: 600;
    text-decoration: none;
    transition: all 0.3s ease;
    text-align: center;
  }
  
  .product-highlights-section.dark .product-button {
    color: #fff;
  }
  
  .product-button:hover {
    background-color: #d1b073;
    color: white;
  }
  
  @media screen and (max-width: 992px) {
    .carousel-slide {
      flex: 0 0 calc(50% - 30px);
    }
    
    .product-highlights-section .section-title {
      font-size: 2rem;
    }
    
    .product-highlights-section .section-description {
      font-size: 1rem;
    }
    
    .product-highlights-grid {
      grid-template-columns: repeat(2, 1fr);
    }
  }
  
  @media screen and (max-width: 767px) {
    .carousel-slide {
      flex: 0 0 calc(100% - 30px);
    }
    
    .product-highlights-section .section-title {
      font-size: 1.8rem;
    }
    
    .product-name {
      font-size: 1.3rem;
    }
    
    .product-highlights-grid {
      grid-template-columns: 1fr;
    }
  }
</style>