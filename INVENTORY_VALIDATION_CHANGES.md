# Default Warehouse Inventory Validation Implementation

## Problem Statement
Customers were able to add more items to their cart than were available in the default warehouse because:
1. The system was using total inventory across all locations instead of default location inventory
2. **CRITICAL**: Inventory data was not updating when customers switched between variants, causing incorrect inventory limits to persist

## Solution Overview
Modified the inventory validation system to:
1. Use only the default warehouse/location inventory instead of total inventory across all locations
2. **Dynamically update inventory data when variants change** to ensure accurate limits for each variant

## Changes Made

### 1. Updated Liquid Templates

#### `snippets/quantity-input.liquid`
- Added logic to calculate default location inventory using `variant.store_availabilities.first`
- Updated `data-inventory-quantity` to use default location inventory
- Added `data-total-inventory-quantity` to preserve total inventory information
- Updated `max` attribute to use default location inventory

#### `sections/main-product.liquid`
- Added default location inventory calculation
- Updated quantity input to use default location inventory
- Modified max attribute validation
- **Added `updateMainProductInventoryDataAttributes()` function** for dynamic inventory updates
- **Added variant change event listener** to handle variant switching

#### `sections/rcl-main-product.liquid`
- Added default location inventory calculation
- Updated quantity input to use default location inventory
- Modified max attribute validation

#### `sections/main-cart-items.liquid`
- Added default location inventory calculation for cart items
- Updated quantity input validation in cart

### 2. Updated JavaScript Validation

#### `assets/global.js`
- Modified `getInventoryLimit()` function to use default location inventory
- Added comments to clarify the change

#### `sections/rcl-main-product.liquid` (JavaScript functions)
- Updated `validateInventoryBeforeAdd()` function to use default location inventory
- Updated `getInventoryLimit()` function to use default location inventory
- **Added `updateInventoryDataAttributes()` function** to dynamically update inventory when variants change
- **Added variant change event listener** using PubSub system to respond to variant switches
- **Added server-side inventory fetching** to get accurate location-specific data for each variant

### 3. Added Testing Infrastructure

#### `assets/inventory-validation-test.js`
- Created comprehensive test suite to verify inventory validation
- Tests default location inventory usage
- Tests max attribute validation
- Tests JavaScript validation functions

#### `templates/page.inventory-test.liquid`
- Created test page template for manual testing
- Provides UI to run tests and view results
- Displays current inventory data for debugging

## How It Works

### Default Location Detection
The system uses Shopify's `store_availabilities` array to detect location-specific inventory:

```liquid
{%- liquid
  assign default_location_inventory = variant.inventory_quantity
  
  if variant.store_availabilities.size > 0
    assign default_location = variant.store_availabilities.first
    if default_location.available and default_location.inventory_quantity
      assign default_location_inventory = default_location.inventory_quantity
    endif
  endif
-%}
```

### Fallback Behavior
- If `store_availabilities` is empty or unavailable, falls back to total inventory
- If default location doesn't have inventory data, uses total inventory
- Maintains backward compatibility with existing functionality

### Data Attributes
- `data-inventory-quantity`: Now contains default location inventory
- `data-total-inventory-quantity`: Contains total inventory across all locations
- This allows JavaScript to access both values if needed

## Testing

### Manual Testing
1. Create a page with handle "inventory-test"
2. Navigate to the test page
3. Click "Run Inventory Tests" to verify functionality
4. Check browser console for detailed test results

### Automated Testing
The test suite checks:
- Default location inventory is being used instead of total inventory
- Max attributes match default location inventory
- JavaScript validation functions work correctly

## Benefits

1. **Accurate Inventory Control**: Customers can only add quantities available in the default warehouse
2. **Prevents Overselling**: Eliminates the risk of selling more than available in the primary fulfillment location
3. **Maintains Compatibility**: Falls back to existing behavior when location data is unavailable
4. **Comprehensive Testing**: Includes test infrastructure to verify functionality

## Configuration

No additional configuration is required. The system automatically:
- Detects the default location (first in `store_availabilities` array)
- Uses location-specific inventory when available
- Falls back to total inventory when location data is unavailable

## Monitoring

To monitor the effectiveness of this change:
1. Use the test page to verify inventory validation
2. Check browser console for any JavaScript errors
3. Monitor cart abandonment rates (should decrease if customers can't add unavailable quantities)
4. Review order fulfillment issues (should decrease)

## Future Enhancements

Potential improvements for the future:
1. Allow configuration of which location to use as default
2. Add admin interface to view location-specific inventory
3. Implement location-based inventory selection for customers
4. Add more sophisticated inventory allocation logic
