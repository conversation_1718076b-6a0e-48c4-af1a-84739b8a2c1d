/* Custom Featured Collection Styles */
.custom-featured-collection {
  padding-top: var(--section-padding-top);
  padding-bottom: var(--section-padding-bottom);
  background-color: var(--section-background);
  position: relative;
  overflow: hidden;
}

.custom-featured-collection__header {
  margin-bottom: 3rem;
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  justify-content: space-between;
  gap: 1.5rem;
}

.custom-featured-collection__title-container {
  flex: 1;
  min-width: 200px;
}

.custom-featured-collection__description-container {
  flex: 2;
  max-width: 600px;
}

.custom-featured-collection__header-right {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.custom-featured-collection__title {
  color: var(--title-color);
  margin: 0;
  line-height: 1.2;
}

.custom-featured-collection__description {
  color: var(--text-color);
  line-height: 1.6;
}

.custom-featured-collection__grid {
  display: grid;
  grid-template-columns: repeat(var(--columns-mobile), 1fr);
  gap: var(--product-spacing-mobile);
  list-style: none;
  padding: 0;
  margin: 0;
}

@media screen and (min-width: 750px) {
  .custom-featured-collection__grid {
    grid-template-columns: repeat(var(--columns-desktop), 1fr);
    gap: var(--product-spacing-desktop);
  }
}

/* Slider Styles */
.custom-featured-collection__slider {
  position: relative;
  margin: 0 -1rem;
  padding: 0 1rem;
  overflow: visible;
}

.custom-featured-collection__slider .custom-featured-collection__grid {
  display: flex;
  flex-wrap: nowrap;
  overflow-x: auto;
  scrollbar-width: none; /* Firefox */
  -ms-overflow-style: none; /* IE and Edge */
  scroll-behavior: smooth;
  scroll-snap-type: x mandatory;
  padding: 2.5rem 0;
  gap: 1.5rem;
}

.custom-featured-collection__slider .custom-featured-collection__grid::-webkit-scrollbar {
  display: none; /* Chrome, Safari, Opera */
}

.custom-featured-collection__slider .custom-product-card {
  flex: 0 0 auto;
  width: calc(80% / var(--columns-mobile) - (var(--product-spacing-mobile) * (var(--columns-mobile) - 1) / var(--columns-mobile)));
  scroll-snap-align: start;
  margin-bottom: 0;
  box-shadow: 0 4px 12px rgba(var(--color-foreground), 0.08);
  border-radius: 1rem;
}

@media screen and (min-width: 750px) {
  .custom-featured-collection__slider .custom-product-card {
    width: calc(95% / var(--columns-desktop) - (var(--product-spacing-desktop) * (var(--columns-desktop) - 1) / var(--columns-desktop)));
    height: auto;
  }
}

.custom-featured-collection__slider-nav {
  display: flex;
  justify-content: center;
  gap: 1rem;
}

.custom-featured-collection__slider-button {
  background-color: transparent;
  border: 1px solid var(--text-color);
  border-radius: 50%;
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: background-color 0.3s ease, color 0.3s ease;
}

.custom-featured-collection__slider-button:hover {
  background-color: var(--text-color);
  color: var(--section-background);
}

.custom-featured-collection__slider-button[disabled] {
  opacity: 0.3;
  cursor: not-allowed;
}

/* Mobile Slider Controls */
.custom-featured-collection__slider-mobile-nav {
  display: none;
}

@media screen and (max-width: 749px) {
  .custom-featured-collection__slider-mobile-nav {
    display: block;
    position: absolute;
    top: 50%;
    left: 0;
    right: 0;
    transform: translateY(-50%);
    z-index: 2;
    pointer-events: none;
  }
  
  .custom-featured-collection__slider-mobile-nav .custom-featured-collection__slider-button {
    position: absolute;
    background-color: rgba(255, 255, 255, 0.8);
    pointer-events: auto;
  }
  
  .custom-featured-collection__slider-mobile-nav .custom-featured-collection__slider-prev {
    left: 10px;
  }
  
  .custom-featured-collection__slider-mobile-nav .custom-featured-collection__slider-next {
    right: 10px;
  }
  
  .custom-featured-collection__header-right .custom-featured-collection__slider-nav {
    display: none;
  }
}

/* Product Card Styles - Modified for Equal Heights */
.custom-product-card {
  position: relative;
  background: #ffffff;
  overflow: hidden;
  margin-bottom: 1.5rem;
  display: flex;
  flex-direction: column;
  height: 100%;
  /* Add this to make sure cards align properly */
  align-items: stretch;
  box-shadow: 0 4px 12px rgba(var(--color-foreground), 0.08);
  border-radius: 1rem;
}

.custom-product-card__media {
  position: relative;
  overflow: hidden;
  width: 100%;
  /* Set a fixed aspect ratio for all images */
  padding-bottom: 100%; /* 1:1 Aspect ratio */
  height: 0;
}

.custom-product-card__media img,
.custom-product-card__media .placeholder-svg {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  object-fit: cover;
  object-position: center;
  display: block;
  transition: transform 0.5s ease;
}

.custom-product-card:hover .custom-product-card__media img {
  transform: scale(1.05);
}

.custom-product-card__collection-tag {
  background: rgba(209, 176, 115, 1);
  color: #fff;
  text-transform: uppercase;
  padding: 4px 20px;
  font-size: 1.2rem;
  font-weight: 600;
  z-index: 1;
  text-align: center;
  margin: 0 0 1rem;
}

.custom-product-card__info {
  padding: 2rem;
  display: flex;
  flex-direction: column;
  flex: 1;
  /* Use auto height instead of fixed height */
  height: auto;
}

.custom-product-card__title {
  font-size: 1.6rem;
  font-weight: 500;
  margin: 0;
  line-height: 1.3;
  /* Use text-overflow with ellipsis for proper truncation */
  max-height: none; /* Remove fixed height */
  overflow: hidden;
  display: -webkit-box;
  -webkit-line-clamp: 2; /* Limit to 2 lines */
  -webkit-box-orient: vertical;
  text-overflow: ellipsis; /* Show ellipsis for truncated text */
}

.custom-product-card__title a {
  color: var(--text-color);
  text-decoration: none;
}

.custom-product-card__title a:hover {
  text-decoration: underline;
}

.custom-product-card__description {
  font-size: 0.9rem;
  color: var(--product-primary);
  margin-top: 0.5rem;
  line-height: 1.4;
  /* Remove fixed height and use flex properties for proper spacing */
  height: auto;
  overflow: hidden;
  display: -webkit-box;
  -webkit-line-clamp: 3; /* Limit to 3 lines */
  -webkit-box-orient: vertical;
  text-overflow: ellipsis; /* Show ellipsis for truncated text */
  flex: 1;
  padding: 1rem 0;
}

.custom-product-card__price span.price-item.price-item--regular {
  font-size: 1.8rem;
  font-weight: 600;
  margin-top: 0.5rem;
  color: var(--text-color);
  height: auto;
}

.custom-product-card__view-button {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background-color: #000;
  color: #fff;
  text-align: center;
  padding: 0.8rem 1rem;
  text-decoration: none;
  font-weight: 500;
  font-size: 0.9rem;
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  transform: translateY(100%);
  transition: all 0.3s ease;
}

.custom-product-card__view-button svg {
  margin-left: 0.5rem;
}

.custom-product-card:hover .custom-product-card__view-button {
  opacity: 1;
  transform: translateY(0);
}

.custom-featured-collection__view-all {
  margin-top: 3rem;
  text-align: center;
}

.custom-featured-collection__view-all-button {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  background-color: #000;
  color: #fff;
  padding: 0.8rem 2rem;
  text-decoration: none;
  font-weight: 500;
  border-radius: 0;
  transition: background-color 0.3s ease;
}

.custom-featured-collection__view-all-button:hover {
  background-color: #333;
}

.custom-featured-collection__view-all-button svg {
  margin-left: 0.5rem;
}

@media screen and (max-width: 749px) {
  .custom-featured-collection__header {
    flex-direction: column;
    align-items: flex-start;
  }

  .custom-featured-collection__p1 {
    text-align: center;
  }

  .custom-featured-collection__title-container,
  .custom-featured-collection__description-container {
    width: 100%;
    max-width: 100%;
  }

  .custom-featured-collection__title {
    margin-bottom: 1rem;
  }
  
  /* Remove fixed heights for mobile */
  .custom-product-card__info {
    height: auto;
  }
  
  .custom-product-card__description {
    height: auto;
  }
}