document.addEventListener('DOMContentLoaded', function() {
  initQuantityCalculator();
});

function initQuantityCalculator() {
  const calculator = document.querySelector('.product-calculator');
  if (!calculator) {
    console.log('Calculator element not found');
    return;
  }

  const toggle = calculator.querySelector('.calculator-toggle');
  if (!toggle) {
    console.log('Toggle button not found');
    return;
  }

  const drawer = document.getElementById('QuantityCalculator');
  if (!drawer) {
    console.log('Drawer element not found');
    return;
  }

  const overlay = document.getElementById('CalculatorOverlay');
  if (!overlay) {
    console.log('Overlay element not found');
    return;
  }

  const closeBtn = drawer.querySelector('.quantity-calculator-drawer__close');
  if (!closeBtn) {
    console.log('Close button not found');
    return;
  }

  const addWallBtn = drawer.querySelector('.quantity-calculator__add-wall');
  if (!addWallBtn) {
    console.log('Add wall button not found');
    return;
  }

  const resetBtn = drawer.querySelector('.quantity-calculator__reset');
  if (!resetBtn) {
    console.log('Reset button not found');
    return;
  }

  const unitRadios = drawer.querySelectorAll('input[name="unit"]');
  if (!unitRadios.length) {
    console.log('Unit radio buttons not found');
    return;
  }

  const wallsContainer = document.getElementById('calculatorWalls');
  if (!wallsContainer) {
    console.log('Walls container not found');
    return;
  }

  const consumptionText = calculator.getAttribute('data-consumption');
  console.log('Consumption data:', consumptionText);

  const parsedConsumption = parseConsumptionRate(consumptionText);
  console.log('Parsed consumption rate:', parsedConsumption);

  const minRate = parsedConsumption.minRate;
  const maxRate = parsedConsumption.maxRate;
  const unit = parsedConsumption.unit;
  const isAreaToVolume = parsedConsumption.isAreaToVolume;

  toggle.addEventListener('click', function(e) {
    e.preventDefault();
    e.stopPropagation();
    openDrawer();
  });

  function openDrawer() {
    drawer.classList.add('active');
    overlay.classList.add('active');
    document.body.classList.add('has-calculator-open');

    const headerElements = document.querySelectorAll('.shopify-section-header-sticky, .header-wrapper, .section-header, .shopify-section-group-header-group');
    headerElements.forEach(element => {
      element.style.zIndex = '0';
    });

    overlay.style.zIndex = '1001';
    drawer.style.zIndex = '1002';

    document.addEventListener('keydown', handleEscKey);
  }

  function closeDrawer() {
    drawer.classList.remove('active');
    overlay.classList.remove('active');
    document.body.classList.remove('has-calculator-open');

    const headerElements = document.querySelectorAll('.shopify-section-header-sticky, .header-wrapper, .section-header, .shopify-section-group-header-group');
    headerElements.forEach(element => {
      element.style.removeProperty('z-index');
    });

    overlay.style.removeProperty('z-index');
    drawer.style.removeProperty('z-index');

    document.removeEventListener('keydown', handleEscKey);
  }

  function handleEscKey(event) {
    if (event.key === 'Escape') {
      closeDrawer();
    }
  }

  closeBtn.addEventListener('click', function(e) {
    e.preventDefault();
    closeDrawer();
  });

  overlay.addEventListener('click', function(e) {
    e.preventDefault();
    closeDrawer();
  });

  unitRadios.forEach(radio => {
    radio.addEventListener('change', function() {
      convertAllInputs();
      setTimeout(calculateTotals, 10);
    });
  });

  function convertAllInputs() {
    const isMeters = drawer.querySelector('input[name="unit"][value="meters"]').checked;
    const walls = wallsContainer.querySelectorAll('.quantity-calculator__wall');

    walls.forEach(wall => {
      const heightInput = wall.querySelector('.wall-height');
      const widthInput = wall.querySelector('.wall-width');

      if (heightInput && widthInput && heightInput.value && widthInput.value) {
        if (isMeters) {
          if (heightInput.dataset.originalUnit === 'feet') {
            heightInput.value = (parseFloat(heightInput.value) * 0.3048).toFixed(2);
            heightInput.dataset.originalUnit = 'meters';
          }
          if (widthInput.dataset.originalUnit === 'feet') {
            widthInput.value = (parseFloat(widthInput.value) * 0.3048).toFixed(2);
            widthInput.dataset.originalUnit = 'meters';
          }
        } else {
          if (heightInput.dataset.originalUnit === 'meters' || !heightInput.dataset.originalUnit) {
            heightInput.value = (parseFloat(heightInput.value) / 0.3048).toFixed(2);
            heightInput.dataset.originalUnit = 'feet';
          }
          if (widthInput.dataset.originalUnit === 'meters' || !widthInput.dataset.originalUnit) {
            widthInput.value = (parseFloat(widthInput.value) / 0.3048).toFixed(2);
            widthInput.dataset.originalUnit = 'feet';
          }
        }
      }
    });
  }

  addWallBtn.addEventListener('click', function() {
    const walls = wallsContainer.querySelectorAll('.quantity-calculator__wall');
    if (!walls.length) return;

    const newIndex = walls.length + 1;

    const newWall = walls[0].cloneNode(true);
    newWall.setAttribute('data-wall-index', newIndex);

    const wallNumber = newWall.querySelector('.wall-number');
    if (wallNumber) {
      wallNumber.textContent = newIndex;
    }

    const inputs = newWall.querySelectorAll('input');
    inputs.forEach(input => {
      input.value = '';
      delete input.dataset.originalUnit;
      input.id = input.id.replace(/\d+/, newIndex);
    });

    const removeBtn = newWall.querySelector('.quantity-calculator__wall-remove');
    if (removeBtn) {
      removeBtn.style.display = 'block';
    }

    addWallEventListeners(newWall);

    wallsContainer.appendChild(newWall);

    if (newIndex > 1) {
      const allRemoveBtns = wallsContainer.querySelectorAll('.quantity-calculator__wall-remove');
      allRemoveBtns.forEach(btn => {
        btn.style.display = 'block';
      });
    }

    calculateTotals();
  });

  resetBtn.addEventListener('click', function() {
    const walls = wallsContainer.querySelectorAll('.quantity-calculator__wall');

    for (let i = walls.length - 1; i > 0; i--) {
      wallsContainer.removeChild(walls[i]);
    }

    if (walls.length > 0) {
      const firstWallInputs = walls[0].querySelectorAll('input');
      firstWallInputs.forEach(input => {
        input.value = '';
        delete input.dataset.originalUnit;
      });

      const removeBtn = walls[0].querySelector('.quantity-calculator__wall-remove');
      if (removeBtn) {
        removeBtn.style.display = 'none';
      }
    }

    calculateTotals();
  });

  const initialWall = wallsContainer.querySelector('.quantity-calculator__wall');
  if (initialWall) {
    addWallEventListeners(initialWall);
  }

  function addWallEventListeners(wall) {
    const inputs = wall.querySelectorAll('input');
    inputs.forEach(input => {
      input.addEventListener('input', function() {
        const isMeters = drawer.querySelector('input[name="unit"][value="meters"]').checked;
        input.dataset.originalUnit = isMeters ? 'meters' : 'feet';
        calculateTotals();
      });
    });

    const removeBtn = wall.querySelector('.quantity-calculator__wall-remove');
    if (removeBtn) {
      removeBtn.addEventListener('click', function() {
        wallsContainer.removeChild(wall);

        const walls = wallsContainer.querySelectorAll('.quantity-calculator__wall');
        walls.forEach((w, index) => {
          w.setAttribute('data-wall-index', index + 1);
          const wallNumber = w.querySelector('.wall-number');
          if (wallNumber) {
            wallNumber.textContent = index + 1;
          }

          if (walls.length === 1) {
            const firstRemoveBtn = walls[0].querySelector('.quantity-calculator__wall-remove');
            if (firstRemoveBtn) {
              firstRemoveBtn.style.display = 'none';
            }
          }
        });

        calculateTotals();
      });
    }
  }

  function calculateTotals() {
    const isMetersRadio = drawer.querySelector('input[name="unit"][value="meters"]');
    if (!isMetersRadio) return;

    const isMeters = isMetersRadio.checked;
    const walls = wallsContainer.querySelectorAll('.quantity-calculator__wall');

    let totalArea = 0;

    walls.forEach((wall) => {
      const heightInput = wall.querySelector('.wall-height');
      const widthInput = wall.querySelector('.wall-width');

      if (heightInput && widthInput && heightInput.value && widthInput.value) {
        let height = parseFloat(heightInput.value);
        let width = parseFloat(widthInput.value);

        let calculateHeight = height;
        let calculateWidth = width;

        if (!isMeters) {
          calculateHeight = height * 0.3048;
          calculateWidth = width * 0.3048;
        }

        const area = calculateHeight * calculateWidth;
        totalArea += area;
      }
    });

    console.log('Total area calculated:', totalArea);

    const areaValueElement = drawer.querySelector('.quantity-calculator__area-value');
    if (!areaValueElement) return;

    const areaUnit = isMeters ? 'm²' : 'ft²';

    let displayArea = totalArea;
    if (!isMeters) {
      displayArea = totalArea * 10.764;
    }

    areaValueElement.textContent = `${displayArea.toFixed(1)} ${areaUnit}`;

    let recommendedQuantity = 0;

    if (totalArea > 0) {
      if (isAreaToVolume) {
        recommendedQuantity = totalArea / maxRate;
      } else {
        recommendedQuantity = totalArea * maxRate;
      }
    }

    console.log('Recommended quantity:', recommendedQuantity, 'Using max rate:', maxRate);

    const recommendationValueElement = drawer.querySelector('.quantity-calculator__recommendation-value');
    if (!recommendationValueElement) return;

    const unitDisplay = isAreaToVolume ? 'L' : 'kg';
    recommendationValueElement.textContent = `${recommendedQuantity.toFixed(1)} ${unitDisplay}`;

    if (recommendedQuantity > 0) {
      console.log('Generating variant suggestions for quantity:', recommendedQuantity);
      generateVariantSuggestions(recommendedQuantity, unitDisplay);
    }
  }

  function generateVariantSuggestions(totalQuantity, unit) {
    console.log('Starting variant suggestions generation for quantity:', totalQuantity);
    const variantOptions = getProductVariants();
    console.log('Variant options found:', variantOptions);

    if (!variantOptions || variantOptions.length === 0) {
      console.warn('No variant options available for suggestions');
      return;
    }

    const variantsByStyle = {};
    variantOptions.forEach(variant => {
      const style = variant.style || 'Default Style';
      if (!variantsByStyle[style]) {
        variantsByStyle[style] = [];
      }
      variantsByStyle[style].push(variant);
    });

    console.log('Variants grouped by style:', variantsByStyle);

    const selectedStyle = findSelectedStyle() || Object.keys(variantsByStyle)[0];
    console.log('Selected style for suggestions:', selectedStyle);

    let styleVariants = variantsByStyle[selectedStyle] || variantOptions;

    styleVariants.sort((a, b) => b.size - a.size);
    console.log('Sorted style variants for suggestions:', styleVariants);

    const suggestions = [];
    let remainingQuantity = totalQuantity;

    for (const variant of styleVariants) {
      if (remainingQuantity <= 0) break;

      const count = Math.floor(remainingQuantity / variant.size);
      if (count > 0) {
        suggestions.push({
          variant_id: variant.id,
          title: variant.title,
          size: variant.size,
          style: variant.style || selectedStyle,
          count: count
        });

        remainingQuantity -= count * variant.size;
      }
    }

    if (remainingQuantity > 0 && styleVariants.length > 0) {
      const smallestVariant = styleVariants[styleVariants.length - 1];

      const existingVariant = suggestions.find(v => v.variant_id === smallestVariant.id);

      if (existingVariant) {
        existingVariant.count += 1;
      } else {
        suggestions.push({
          variant_id: smallestVariant.id,
          title: smallestVariant.title,
          size: smallestVariant.size,
          style: smallestVariant.style || selectedStyle,
          count: 1
        });
      }
    }

    const optimizedSuggestions = optimizePackages(suggestions, styleVariants);

    console.log('Final variant suggestions:', optimizedSuggestions);
    displayVariantSuggestions(optimizedSuggestions, unit);

    return optimizedSuggestions;
  }

  function optimizePackages(suggestions, availableVariants) {
    const result = [...suggestions];

    for (let i = availableVariants.length - 1; i > 0; i--) {
      const smallerVariant = availableVariants[i];

      for (let j = 0; j < i; j++) {
        const largerVariant = availableVariants[j];

        if (smallerVariant.style !== largerVariant.style) {
          continue;
        }

        const ratio = Math.floor(largerVariant.size / smallerVariant.size);

        if (ratio > 1) {
          const smallerVariantSuggestion = result.find(s => s.size === smallerVariant.size && s.style === smallerVariant.style);

          if (smallerVariantSuggestion && smallerVariantSuggestion.count >= ratio) {
            const replacementsCount = Math.floor(smallerVariantSuggestion.count / ratio);

            const largerVariantSuggestion = result.find(s => s.size === largerVariant.size && s.style === largerVariant.style);

            if (largerVariantSuggestion) {
              largerVariantSuggestion.count += replacementsCount;
            } else {
              result.push({
                variant_id: largerVariant.id,
                title: largerVariant.title,
                size: largerVariant.size,
                style: largerVariant.style,
                count: replacementsCount
              });
            }

            smallerVariantSuggestion.count -= replacementsCount * ratio;

            if (smallerVariantSuggestion.count === 0) {
              const index = result.findIndex(s => s.size === smallerVariant.size && s.style === smallerVariant.style);
              if (index !== -1) {
                result.splice(index, 1);
              }
            }
          }
        }
      }
    }

    result.sort((a, b) => {
      if (a.style !== b.style) {
        return a.style < b.style ? -1 : 1;
      }
      return b.size - a.size;
    });

    return result;
  }

  function displayVariantSuggestions(suggestions, unit) {
    console.log('Displaying variant suggestions:', suggestions);
    let suggestionsContainer = drawer.querySelector('.quantity-calculator__variant-suggestions');

    if (!suggestionsContainer) {
      console.log('Creating new suggestions container');
      suggestionsContainer = document.createElement('div');
      suggestionsContainer.className = 'quantity-calculator__variant-suggestions';

      const heading = document.createElement('h3');
      heading.className = 'quantity-calculator__suggestions-heading';
      heading.textContent = 'Suggested Products';
      suggestionsContainer.appendChild(heading);

      const recommendationElement = drawer.querySelector('.quantity-calculator__recommendation');
      const resultsContainer = drawer.querySelector('.quantity-calculator__results');

      if (recommendationElement && recommendationElement.parentNode) {
        console.log('Inserting container after recommendation element');
        recommendationElement.parentNode.insertBefore(suggestionsContainer, recommendationElement.nextSibling);
      } else if (resultsContainer) {
        console.log('Appending container to results container');
        resultsContainer.appendChild(suggestionsContainer);
      } else {
        console.warn('Could not find a place to add suggestions container');
        return;
      }
    } else {
      console.log('Updating existing suggestions container');
      while (suggestionsContainer.firstChild) {
        suggestionsContainer.removeChild(suggestionsContainer.firstChild);
      }

      const heading = document.createElement('h3');
      heading.className = 'quantity-calculator__suggestions-heading';
      heading.textContent = 'Suggested Products';
      suggestionsContainer.appendChild(heading);
    }

    suggestions.forEach(suggestion => {
      const item = document.createElement('div');
      item.className = 'quantity-calculator__suggestion-item';

      item.innerHTML = `
        <div class="suggestion-item__details">
          <span class="suggestion-item__count">${suggestion.count}×</span>
          <span class="suggestion-item__title">${suggestion.title} (${suggestion.size} ${unit})</span>
        </div>
        <button
          type="button"
          class="suggestion-item__add-btn"
          data-variant-id="${suggestion.variant_id}"
          data-quantity="${suggestion.count}"
        >
          Add to Cart
        </button>
      `;

      suggestionsContainer.appendChild(item);

      const addButton = item.querySelector('.suggestion-item__add-btn');
      if (addButton) {
        addButton.addEventListener('click', function() {
          addVariantToCart(suggestion.variant_id, suggestion.count);
        });
      }
    });

    const addAllButton = document.createElement('button');
    addAllButton.type = 'button';
    addAllButton.className = 'quantity-calculator__add-all-btn';
    addAllButton.textContent = 'Add All to Cart';
    addAllButton.addEventListener('click', function() {
      addAllToCart(suggestions);
    });

    suggestionsContainer.appendChild(addAllButton);
    console.log('Finished displaying variant suggestions');
  }

  function getProductVariants() {
    console.log('Fetching product variants...');
    try {
      const variantDataElement = document.getElementById('ProductVariantData');
      console.log('Variant data element:', variantDataElement);

      if (variantDataElement) {
        console.log('Variant data element content:', variantDataElement.textContent);
        try {
          const cleanJSON = variantDataElement.textContent.trim()
            .replace(/\s+/g, ' ')
            .replace(/,\s*]/g, ']');

          console.log('Cleaned JSON:', cleanJSON);

          const variantData = JSON.parse(cleanJSON);
          console.log('Parsed variant data:', variantData);

          if (!variantData.variants || !Array.isArray(variantData.variants)) {
            console.warn('Invalid variants data format - variants property missing or not an array');
            return [];
          }

          const availableVariants = variantData.variants.filter(variant => variant.available).map(variant => {
            const processedVariant = {...variant};

            if (!processedVariant.size || processedVariant.size === 0) {
              const sizeMatch = processedVariant.title.match(/(\d+(\.\d+)?)(?:\s*KG|\s*kg)/i);
              if (sizeMatch && sizeMatch[1]) {
                processedVariant.size = parseFloat(sizeMatch[1]);
              }
            }

            const styleMatch = processedVariant.title.match(/\/\s*([^(]+?)(?:\s*\(|$)/i);
            if (styleMatch && styleMatch[1]) {
              processedVariant.style = styleMatch[1].trim();
            } else {
              const colorMatch = processedVariant.title.match(/^([^\/]+?)\s*\//i);
              if (colorMatch && colorMatch[1]) {
                processedVariant.style = colorMatch[1].trim();
              }
            }

            return processedVariant;
          }).filter(variant => variant.size && !isNaN(variant.size));

          console.log('Available variants after processing:', availableVariants);

          if (availableVariants.length > 0) {
            return availableVariants;
          }
        } catch (jsonError) {
          console.error('Error parsing variant JSON:', jsonError);
        }
      }

      console.log('Fallback: Trying to get variants from select elements');
      const sizeOptions = [];
      const variantSelects = document.querySelectorAll('select[name="id"], select[data-variant-selector]');
      console.log('Variant selects found:', variantSelects.length);

      if (variantSelects.length > 0) {
        const variantSelect = variantSelects[0];
        const options = variantSelect.querySelectorAll('option');
        console.log('Options in variant select:', options.length);

        options.forEach(option => {
          if (!option.disabled && option.value) {
            const title = option.textContent.trim();
            console.log('Processing option:', title, option.value);
            const sizeMatch = title.match(/(\d+(\.\d+)?)(?:\s*KG|\s*kg)/i);

            if (sizeMatch && sizeMatch[1]) {
              const size = parseFloat(sizeMatch[1]);

              const styleMatch = title.match(/\/\s*([^(]+?)(?:\s*\(|$)/i);
              let style = 'Default Style';

              if (styleMatch && styleMatch[1]) {
                style = styleMatch[1].trim();
              }

              sizeOptions.push({
                id: option.value,
                title: title,
                size: size,
                style: style,
                available: true,
                price: option.dataset.price || 0
              });
            }
          }
        });

        if (sizeOptions.length > 0) {
          console.log('Found variants from select:', sizeOptions);
          return sizeOptions;
        }
      }

      console.log('Trying to get variants from selected options');
      const selectedStyle = findSelectedStyle();

      const sizeButtons = document.querySelectorAll('.size-selector button, [data-option-name="Size"] button');
      if (sizeButtons.length > 0) {
        const sizeVariants = [];

        sizeButtons.forEach(button => {
          const sizeText = button.textContent.trim();
          const sizeMatch = sizeText.match(/(\d+(\.\d+)?)(?:\s*KG|\s*kg)/i);

          if (sizeMatch && sizeMatch[1]) {
            const size = parseFloat(sizeMatch[1]);
            const variantId = button.getAttribute('data-variant-id') ||
                             button.getAttribute('data-value') ||
                             button.value;

            if (variantId) {
              const variantTitle = findVariantTitle(size, selectedStyle);
              sizeVariants.push({
                id: variantId,
                title: variantTitle || `${size}KG / ${selectedStyle || 'Default Style'}`,
                size: size,
                style: selectedStyle || 'Default Style',
                available: !button.disabled,
                price: 0
              });
            }
          }
        });

        if (sizeVariants.length > 0) {
          console.log('Found variants from size buttons:', sizeVariants);
          return sizeVariants;
        }
      }

      console.log('Trying to get variants from visible product options');
      const sizeSelectors = Array.from(document.querySelectorAll('.product__sizes button, .size button, [data-option="Size"] button'));
      const visibleSizes = [];

      sizeSelectors.forEach(sizeSelector => {
        if (sizeSelector.offsetParent !== null) {
          const sizeText = sizeSelector.textContent.trim();
          const sizeMatch = sizeText.match(/(\d+(\.\d+)?)(?:\s*KG|\s*kg)/i);

          if (sizeMatch && sizeMatch[1]) {
            const size = parseFloat(sizeMatch[1]);
            const variantId = sizeSelector.dataset.variantId || sizeSelector.dataset.value;

            if (variantId) {
              visibleSizes.push({
                id: variantId,
                title: sizeText,
                size: size,
                style: selectedStyle || 'Default Style',
                available: !sizeSelector.disabled,
                price: 0
              });
            }
          }
        }
      });

      if (visibleSizes.length > 0) {
        console.log('Found variants from visible size selectors:', visibleSizes);
        return visibleSizes;
      }

      console.log('Fallback: Trying to get variants from product form');
      const productForm = document.querySelector('form[action*="/cart/add"]');
      if (productForm) {
        const variantInput = productForm.querySelector('input[name="id"]');

        const sizeLabelButtons = productForm.querySelectorAll('.size-options button, .single-option-selector button');
        if (sizeLabelButtons.length > 0) {
          const formSizes = [];

          sizeLabelButtons.forEach(button => {
            const text = button.textContent.trim();
            const sizeMatch = text.match(/(\d+(\.\d+)?)(?:\s*KG|\s*kg)/i);

            if (sizeMatch && sizeMatch[1] && (button.classList.contains('active') || !button.disabled)) {
              const size = parseFloat(sizeMatch[1]);
              formSizes.push({
                id: button.dataset.value || (variantInput ? variantInput.value : '1'),
                title: text,
                size: size,
                style: selectedStyle || 'Default Style',
                available: true
              });
            }
          });

          if (formSizes.length > 0) {
            console.log('Found variants from form size buttons:', formSizes);
            return formSizes;
          }
        }

        if (variantInput) {
          const sizeElements = document.querySelectorAll('.product__description, .product-options, .product-variant-size');
          let variantSizes = [];

          sizeElements.forEach(element => {
            const text = element.textContent.trim();
            const sizeMatches = text.match(/(\d+(\.\d+)?)(?:\s*KG|\s*kg)/gi);

            if (sizeMatches) {
              sizeMatches.forEach(match => {
                const sizeValue = parseFloat(match.match(/\d+(\.\d+)?/)[0]);
                if (!isNaN(sizeValue)) {
                  const variantTitle = findVariantTitle(sizeValue, selectedStyle);
                  variantSizes.push({
                    id: variantInput.value,
                    title: variantTitle || `${sizeValue}KG / ${selectedStyle || 'Default Style'}`,
                    size: sizeValue,
                    style: selectedStyle || 'Default Style',
                    available: true
                  });
                }
              });
            }
          });

          if (variantSizes.length > 0) {
            return variantSizes;
          }
        }
      }

      return extractVariantsFromUI(selectedStyle);

    } catch (error) {
      console.error('Error in getProductVariants:', error);
      return [];
    }
  }

  function extractVariantsFromUI(selectedStyle) {
    const productForm = document.querySelector('form[action*="/cart/add"]');
    if (!productForm) return [];

    const variantInput = productForm.querySelector('input[name="id"]');
    if (!variantInput) return [];

    const activeButtons = document.querySelectorAll('.active[data-option-value], .selected[data-option-value]');
    const variantSizes = [];

    activeButtons.forEach(button => {
      const optionType = button.closest('[data-option]')?.getAttribute('data-option') || '';
      const value = button.getAttribute('data-option-value') || button.textContent.trim();

      if (optionType.toLowerCase() === 'size' || value.match(/(\d+(\.\d+)?)(?:\s*KG|\s*kg)/i)) {
        const sizeMatch = value.match(/(\d+(\.\d+)?)(?:\s*KG|\s*kg)/i);
        if (sizeMatch && sizeMatch[1]) {
          const size = parseFloat(sizeMatch[1]);
          variantSizes.push({
            id: variantInput.value,
            title: `${size}KG / ${selectedStyle || 'Default Style'}`,
            size: size,
            style: selectedStyle || 'Default Style',
            available: true
          });
        }
      }
    });

    if (variantSizes.length > 0) {
      return variantSizes;
    }

    const allStyles = document.querySelectorAll('[data-option="Color"] button, [data-option="Style"] button');
    const allSizes = document.querySelectorAll('[data-option="Size"] button');

    const variants = [];

    if (allStyles.length > 0 && allSizes.length > 0) {
      allStyles.forEach(styleBtn => {
        const style = styleBtn.textContent.trim();

        allSizes.forEach(sizeBtn => {
          const sizeText = sizeBtn.textContent.trim();
          const sizeMatch = sizeText.match(/(\d+(\.\d+)?)(?:\s*KG|\s*kg)/i);

          if (sizeMatch && sizeMatch[1]) {
            const size = parseFloat(sizeMatch[1]);
            variants.push({
              id: variantInput.value,
              title: `${size}KG / ${style}`,
              size: size,
              style: style,
              available: true
            });
          }
        });
      });
    }

    return variants;
  }

  function findSelectedStyle() {
    const styleButtons = document.querySelectorAll('.color-selector button, [data-option="Color"] button, [data-option-name="Color"] button, .product-form__input[data-option="Color"] .product-form__radio');

    for (const button of styleButtons) {
      if (button.classList.contains('active') ||
          button.classList.contains('selected') ||
          button.getAttribute('aria-checked') === 'true' ||
          button.checked) {
        return button.textContent.trim();
      }
    }

    const styleSelects = document.querySelectorAll('select[data-option="Color"], select[data-index="option1"]');
    for (const select of styleSelects) {
      const selectedOption = select.options[select.selectedIndex];
      if (selectedOption) {
        return selectedOption.textContent.trim();
      }
    }

    const colorLabels = document.querySelectorAll('.product__info-container .color-value, .product-single__meta .current-color');
    for (const label of colorLabels) {
      if (label.textContent.trim()) {
        return label.textContent.trim();
      }
    }

    const allStyles = document.querySelectorAll('[data-option="Color"] button, [data-option-name="Color"] button');
    if (allStyles.length === 1) {
      return allStyles[0].textContent.trim();
    }

    const productTitle = document.querySelector('.product__title, .product-single__title')?.textContent.trim();
    if (productTitle) {
      const styleParts = productTitle.split(' - ');
      if (styleParts.length > 1) {
        return styleParts[1].trim();
      }
    }

    const urlParams = new URLSearchParams(window.location.search);
    for (const [key, value] of urlParams.entries()) {
      if (key.toLowerCase().includes('color') || key.toLowerCase().includes('style')) {
        return value;
      }
    }

    return null;
  }

  function findVariantTitle(size, style) {
    const productForm = document.querySelector('form[action*="/cart/add"]');
    if (!productForm) return null;

    const variantOptions = productForm.querySelectorAll('option, .variant-input label');
    for (const option of variantOptions) {
      const text = option.textContent.trim();
      const sizeMatch = text.match(/(\d+(\.\d+)?)(?:\s*KG|\s*kg)/i);

      if (sizeMatch && sizeMatch[1] && parseFloat(sizeMatch[1]) === size) {
        if (!style || text.includes(style)) {
          return text;
        }
      }
    }

    const descriptions = document.querySelectorAll('.product__description p, .product-single__description p');
    for (const desc of descriptions) {
      const text = desc.textContent.trim();
      if (text.includes(`${size}KG`) || text.includes(`${size} kg`)) {
        if (!style || text.includes(style)) {
          const match = text.match(/([^.]+(?:\s*\d+(?:\.\d+)?\s*(?:KG|kg)[^.]+))/i);
          if (match && match[1]) {
            return match[1].trim();
          }
        }
      }
    }

    return style ? `${size}KG / ${style}` : `${size}KG`;
  }

  function addVariantToCart(variantId, quantity) {
    console.log('Adding variant to cart:', variantId, 'quantity:', quantity);
    if (!window.Shopify || !window.Shopify.routes || !window.Shopify.routes.root) {
      console.error('Shopify object not available');
      return;
    }

    const formData = {
      items: [
        {
          id: variantId,
          quantity: quantity
        }
      ]
    };

    console.log('Sending cart add request with data:', formData);
    fetch(window.Shopify.routes.root + 'cart/add.js', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(formData)
    })
    .then(response => {
      console.log('Cart add response status:', response.status);
      return response.json();
    })
    .then(data => {
      console.log('Cart add response data:', data);
      updateCartCount();
      showAddToCartConfirmation();

      setTimeout(closeDrawer, 1000);
    })
    .catch(error => {
      console.error('Error adding to cart:', error);
    });
  }

  function addAllToCart(suggestions) {
    console.log('Adding all variants to cart:', suggestions);
    if (!window.Shopify || !window.Shopify.routes || !window.Shopify.routes.root) {
      console.error('Shopify object not available');
      return;
    }

    const items = suggestions.map(suggestion => {
      return {
        id: suggestion.variant_id,
        quantity: suggestion.count
      };
    });

    const formData = {
      items: items
    };

    console.log('Sending cart add all request with data:', formData);
    fetch(window.Shopify.routes.root + 'cart/add.js', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(formData)
    })
    .then(response => {
      console.log('Cart add all response status:', response.status);
      return response.json();
    })
    .then(data => {
      console.log('Cart add all response data:', data);
      updateCartCount();
      showAddToCartConfirmation();

      setTimeout(closeDrawer, 1000);
    })
    .catch(error => {
      console.error('Error adding all to cart:', error);
    });
  }

  function updateCartCount() {
    console.log('Updating cart count');
    if (!window.Shopify || !window.Shopify.routes || !window.Shopify.routes.root) {
      console.error('Shopify object not available');
      return;
    }

    fetch(window.Shopify.routes.root + 'cart.js')
      .then(response => response.json())
      .then(cart => {
        console.log('Cart data:', cart);
        document.querySelectorAll('.cart-count-bubble').forEach(el => {
          const cartCountBubble = el.querySelector('span');
          if (cartCountBubble) {
            cartCountBubble.textContent = cart.item_count;
          }

          if (cart.item_count > 0) {
            el.classList.remove('hidden');
          }
        });
      })
      .catch(error => {
        console.error('Error updating cart count:', error);
      });
  }

  function showAddToCartConfirmation() {
    console.log('Showing add to cart confirmation');
    const message = document.createElement('div');
    message.className = 'cart-notification';
    message.style.position = 'fixed';
    message.style.top = '20px';
    message.style.right = '20px';
    message.style.backgroundColor = '#4a90e2';
    message.style.color = 'white';
    message.style.padding = '12px 20px';
    message.style.borderRadius = '8px';
    message.style.boxShadow = '0 4px 12px rgba(0,0,0,0.15)';
    message.style.zIndex = '9999';
    message.style.transform = 'translateY(-20px)';
    message.style.opacity = '0';
    message.style.transition = 'all 0.3s ease';
    message.textContent = 'Added to cart';

    document.body.appendChild(message);

    setTimeout(() => {
      message.style.transform = 'translateY(0)';
      message.style.opacity = '1';
    }, 10);

    setTimeout(() => {
      message.style.transform = 'translateY(-20px)';
      message.style.opacity = '0';

      setTimeout(() => {
        document.body.removeChild(message);
      }, 300);
    }, 3000);
  }

  function parseConsumptionRate(text) {
    let minRate = 0;
    let maxRate = 0;
    let unit = 'kg';
    let isAreaToVolume = false;

    if (!text) return { minRate, maxRate, unit, isAreaToVolume };

    if (text.toLowerCase().includes('kg/m²') || text.toLowerCase().includes('kg/m2')) {
      unit = 'kg';
      isAreaToVolume = false;
    } else if (text.toLowerCase().includes('m²/l') || text.toLowerCase().includes('m2/l') ||
               text.toLowerCase().includes('m²/L') || text.toLowerCase().includes('m2/L')) {
      unit = 'L';
      isAreaToVolume = true;
    }

    const numbers = text.match(/[\d.,]+/g);
    if (numbers && numbers.length >= 1) {
      minRate = parseFloat(numbers[0].replace(',', '.'));

      if (numbers.length >= 2) {
        maxRate = parseFloat(numbers[1].replace(',', '.'));
      } else {
        maxRate = minRate;
      }
    }

    return { minRate, maxRate, unit, isAreaToVolume };
  }

  calculateTotals();
}