/*
 * ------------------------------------------------------------
 * IMPORTANT: The contents of this file are auto-generated.
 *
 * This file may be updated by the Shopify admin theme editor
 * or related systems. Please exercise caution as any changes
 * made to this file may be overwritten.
 * ------------------------------------------------------------
 */
{
  "sections": {
    "rcl_hero_section_JFhD3h": {
      "type": "rcl-hero-section",
      "blocks": {
        "heading_CxLijh": {
          "type": "heading",
          "settings": {
            "heading": "{{ collection.title }}",
            "heading_size": "h1",
            "heading_color": "#ffffff"
          }
        },
        "subheading_Rnmbx9": {
          "type": "subheading",
          "disabled": true,
          "settings": {
            "subheading": "Premium Decorative Coatings & Plasters",
            "subheading_color": "#d1b073"
          }
        },
        "text_GaPVwz": {
          "type": "text",
          "disabled": true,
          "settings": {
            "description": "<p>Creating premium decorative finishes since 2006</p>",
            "description_color": "#ffffff"
          }
        },
        "button_iJzkzq": {
          "type": "button",
          "disabled": true,
          "settings": {
            "button_label": "Explore Our Products",
            "button_link": "",
            "button_bg_color": "#d1b073",
            "button_text_color": "#ffffff"
          }
        }
      },
      "block_order": [
        "heading_CxLijh",
        "subheading_Rnmbx9",
        "text_GaPVwz",
        "button_iJzkzq"
      ],
      "settings": {
        "hero_image": "shopify://shop_images/7260c61656015d288167c3d198e11fc7_4d73874b-69bd-4e80-b2a8-828ba6bca07d.jpg",
        "text_alignment": "text-center",
        "desktop_height": 400,
        "mobile_height": 300,
        "overlay_color": "#222228",
        "overlay_opacity": 70,
        "parallax_effect": true,
        "enable_image_filter": true,
        "filter_opacity": 50
      }
    },
    "banner": {
      "type": "main-collection-banner",
      "disabled": true,
      "settings": {
        "show_collection_description": true,
        "show_collection_image": false,
        "color_scheme": "scheme-1"
      }
    },
    "product-grid": {
      "type": "main-collection-product-grid",
      "disabled": true,
      "settings": {
        "products_per_page": 16,
        "columns_desktop": 4,
        "color_scheme": "scheme-1",
        "image_ratio": "square",
        "image_shape": "default",
        "show_secondary_image": true,
        "show_vendor": false,
        "show_rating": false,
        "enable_quick_add": false,
        "enable_filtering": true,
        "filter_type": "horizontal",
        "enable_sorting": true,
        "columns_mobile": "2",
        "padding_top": 36,
        "padding_bottom": 36
      }
    },
    "rcl_custom_product_grid_jeEYTK": {
      "type": "rcl-custom-product-grid",
      "disabled": true,
      "settings": {
        "products_per_page": 16,
        "columns_desktop": 4,
        "color_scheme": "",
        "image_ratio": "square",
        "image_shape": "default",
        "show_secondary_image": true,
        "show_vendor": false,
        "show_rating": false,
        "enable_quick_add": false,
        "text_color": "#222222",
        "background_color": "#ffffff",
        "columns_mobile": "2",
        "product_spacing": 25,
        "product_spacing_mobile": 15,
        "padding_top": 36,
        "padding_bottom": 36
      }
    },
    "collection_list_LMp8x8": {
      "type": "collection-list",
      "blocks": {
        "featured_collection_Jryg6k": {
          "type": "featured_collection",
          "settings": {
            "collection": "acrylic-coatings"
          }
        },
        "featured_collection_3VPr3W": {
          "type": "featured_collection",
          "settings": {
            "collection": "pearlescent-effect-plasters"
          }
        },
        "featured_collection_Vb9bmb": {
          "type": "featured_collection",
          "settings": {
            "collection": "lime-based-coatings"
          }
        },
        "featured_collection_dVTQGz": {
          "type": "featured_collection",
          "settings": {
            "collection": "frontpage"
          }
        },
        "featured_collection_cRrfnR": {
          "type": "featured_collection",
          "settings": {
            "collection": "exterior-coatings"
          }
        },
        "featured_collection_JhVXbt": {
          "type": "featured_collection",
          "settings": {
            "collection": "concrete-effect-plasters"
          }
        },
        "featured_collection_KjBRjt": {
          "type": "featured_collection",
          "settings": {
            "collection": "liquid-metal-coatings"
          }
        },
        "featured_collection_3EBVQU": {
          "type": "featured_collection",
          "settings": {
            "collection": "polished-effect-plasters"
          }
        },
        "featured_collection_acmFfk": {
          "type": "featured_collection",
          "settings": {
            "collection": "primers"
          }
        },
        "featured_collection_NtAzXA": {
          "type": "featured_collection",
          "settings": {
            "collection": "varnishes-and-paints"
          }
        },
        "featured_collection_iXJ7M6": {
          "type": "featured_collection",
          "settings": {
            "collection": "waxes-and-emulsions"
          }
        },
        "featured_collection_Pw3QWm": {
          "type": "featured_collection",
          "settings": {
            "collection": "colorants"
          }
        },
        "featured_collection_9HYxwB": {
          "type": "featured_collection",
          "settings": {
            "collection": "decorative-additives"
          }
        },
        "featured_collection_wPVg9e": {
          "type": "featured_collection",
          "settings": {
            "collection": "elf-decor-tools"
          }
        },
        "featured_collection_3YmPMy": {
          "type": "featured_collection",
          "settings": {
            "collection": "sample-cataloges"
          }
        }
      },
      "block_order": [
        "featured_collection_Jryg6k",
        "featured_collection_3VPr3W",
        "featured_collection_Vb9bmb",
        "featured_collection_dVTQGz",
        "featured_collection_cRrfnR",
        "featured_collection_JhVXbt",
        "featured_collection_KjBRjt",
        "featured_collection_3EBVQU",
        "featured_collection_acmFfk",
        "featured_collection_NtAzXA",
        "featured_collection_iXJ7M6",
        "featured_collection_Pw3QWm",
        "featured_collection_9HYxwB",
        "featured_collection_wPVg9e",
        "featured_collection_3YmPMy"
      ],
      "name": "t:sections.collection-list.presets.name",
      "settings": {
        "title": "",
        "heading_size": "h1",
        "image_ratio": "square",
        "columns_desktop": 3,
        "color_scheme": "",
        "show_view_all": false,
        "columns_mobile": "1",
        "swipe_on_mobile": false,
        "padding_top": 36,
        "padding_bottom": 36
      }
    },
    "collection_list_TrePzC": {
      "type": "collection-list",
      "blocks": {
        "featured_collection_V9WrHT": {
          "type": "featured_collection",
          "settings": {
            "collection": "merchandise"
          }
        },
        "featured_collection_n7mgAW": {
          "type": "featured_collection",
          "settings": {
            "collection": "tapes"
          }
        }
      },
      "block_order": [
        "featured_collection_V9WrHT",
        "featured_collection_n7mgAW"
      ],
      "name": "t:sections.collection-list.presets.name",
      "settings": {
        "title": "",
        "heading_size": "h1",
        "image_ratio": "square",
        "columns_desktop": 3,
        "color_scheme": "",
        "show_view_all": false,
        "columns_mobile": "1",
        "swipe_on_mobile": false,
        "padding_top": 36,
        "padding_bottom": 36
      }
    }
  },
  "order": [
    "rcl_hero_section_JFhD3h",
    "banner",
    "product-grid",
    "rcl_custom_product_grid_jeEYTK",
    "collection_list_LMp8x8",
    "collection_list_TrePzC"
  ]
}
