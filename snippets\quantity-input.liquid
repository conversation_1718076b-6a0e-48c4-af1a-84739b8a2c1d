{% comment %}
  Quantity input

  Accepts:
  - variant: {Object} Variant object
  - variant_id: {String} Variant ID (optional)

  Usage:
  {% render 'quantity-input' variant: variant %}
{% endcomment %}

{% comment %} Calculate default location inventory {% endcomment %}
{%- liquid
  assign default_location_inventory = variant.inventory_quantity

  if variant.store_availabilities.size > 0
    assign default_location = variant.store_availabilities.first
    if default_location.available and default_location.inventory_quantity
      assign default_location_inventory = default_location.inventory_quantity
    endif
  endif
-%}

<quantity-input class="quantity cart-quantity">
  <button class="quantity__button no-js-hidden" name="minus" type="button">
    <span class="visually-hidden">
      {{- 'products.product.quantity.decrease' | t: product: variant.title | escape -}}
    </span>
    {% render 'icon-minus' %}
  </button>
  <input
    class="quantity__input"
    data-quantity-variant-id="{{ variant.id }}"
    data-inventory-management="{{ variant.inventory_management }}"
    data-inventory-policy="{{ variant.inventory_policy }}"
    data-inventory-quantity="{{ default_location_inventory }}"
    data-total-inventory-quantity="{{ variant.inventory_quantity }}"
    data-safety-stock-check="true"
    type="number"
    name="updates[{{ variant_id }}]"
    {% # theme-check-disable %}
    value="{{ cart | item_count_for_variant: variant.id }}"
    data-cart-quantity="{{ cart | item_count_for_variant: variant.id }}"
    min="{{ variant.quantity_rule.min }}"
    {% if variant.quantity_rule.max != null %}
      max="{{ variant.quantity_rule.max }}"
    {% elsif variant.inventory_management == 'shopify' and variant.inventory_policy != 'continue' and default_location_inventory != null %}
      max="{{ default_location_inventory }}"
    {% endif %}
    step="{{ variant.quantity_rule.increment }}"
    {% # theme-check-enable %}
    aria-label="{{ 'products.product.quantity.input_label' | t: product: variant.title | escape }}"
    id="Quantity-{{ variant.id }}"
    data-index="{{ variant.id }}"
  >
  <button class="quantity__button no-js-hidden" name="plus" type="button">
    <span class="visually-hidden">
      {{- 'products.product.quantity.increase' | t: product: variant.title | escape -}}
    </span>
    {% render 'icon-plus' %}
  </button>
</quantity-input>
