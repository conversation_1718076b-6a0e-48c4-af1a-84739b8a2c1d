.enhanced-related-products {
  margin-top: 3rem;
}

.enhanced-related-products .related-products__heading {
  margin-bottom: 2rem;
  text-align: center;
}

.enhanced-related-products .product-grid {
  row-gap: 3rem;
}

/* Animation styles */
.enhanced-related-products .animate-card {
  opacity: 0;
  transform: translateY(20px);
  transition: opacity 0.5s ease, transform 0.5s ease;
}

.enhanced-related-products .animate-card.is-visible {
  opacity: 1;
  transform: translateY(0);
}

/* Staggered animation for cards */
.enhanced-related-products .grid__item:nth-child(1) .animate-card {
  transition-delay: 0.1s;
}

.enhanced-related-products .grid__item:nth-child(2) .animate-card {
  transition-delay: 0.2s;
}

.enhanced-related-products .grid__item:nth-child(3) .animate-card {
  transition-delay: 0.3s;
}

.enhanced-related-products .grid__item:nth-child(4) .animate-card {
  transition-delay: 0.4s;
}

.enhanced-related-products .grid__item:nth-child(5) .animate-card {
  transition-delay: 0.5s;
}

/* Hover effects */
.enhanced-related-products .card-hover {
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.08);
  transform: translateY(-5px);
}

/* Responsive adjustments */
@media screen and (max-width: 749px) {
  .enhanced-related-products {
    margin-top: 2rem;
  }
  
  .enhanced-related-products .product-grid {
    row-gap: 2rem;
  }
}

/* Ensure proper spacing between cards */
.enhanced-related-products .grid {
  margin-bottom: 0;
}

/* Ensure proper card styling */
.enhanced-related-products .card {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.enhanced-related-products .card__inner {
  flex-grow: 1;
  display: flex;
  flex-direction: column;
}

.enhanced-related-products .card__content {
  flex-grow: 1;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

/* Ensure proper image aspect ratio */
.enhanced-related-products .card__media {
  position: relative;
  overflow: hidden;
}

/* Ensure proper secondary image transition */
.enhanced-related-products .card__media .media.media--hover-effect > img:nth-child(2) {
  position: absolute;
  top: 0;
  left: 0;
  opacity: 0;
  transition: opacity var(--duration-long) ease;
}

.enhanced-related-products .card:hover .card__media .media.media--hover-effect > img:nth-child(2) {
  opacity: 1;
}

/* Ensure proper price styling */
.enhanced-related-products .price {
  margin-top: 0.5rem;
}

/* Ensure proper vendor styling */
.enhanced-related-products .card__vendor {
  margin-bottom: 0.5rem;
}
