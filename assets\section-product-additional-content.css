.product-additional-content {
  margin: 4rem 0;
}

.product-additional-specs.js-additional-specs {
  margin-bottom: 2rem;
}

/* Accordion Styling */
.accordion-container {
  border-top: 1px solid #f0f0f0;
  margin-bottom: 0;
}

.accordion-container:last-child {
  border-bottom: 1px solid #f0f0f0;
}

.accordion-trigger {
  background: transparent;
  border: none;
  cursor: pointer;
  display: flex;
  align-items: center;
  width: 100%;
  padding: 2rem 0;
  text-align: left;
  transition: all 0.3s ease;
}

.accordion-trigger:hover .accordion-title {
  color: #D1B15A;
}

.accordion-icon {
  margin-right: 1.5rem;
  position: relative;
  width: 16px;
  height: 16px;
  flex-shrink: 0;
}

.icon-plus {
  display: flex;
  align-items: center;
  justify-content: center;
  transition: transform 0.3s ease;
}

.accordion-container.is-active .icon-plus {
  transform: rotate(45deg);
}

.accordion-title {
  font-size: 1.6rem;
  font-weight: 600;
  letter-spacing: normal;
  margin: 0;
  color: #333;
  transition: color 0.3s ease;
}

.accordion-content {
  display: none;
  padding: 0 0 2rem 2.5rem;
  color: var(--product-primary);
}

/* Application Technology Images */
.application-tech-images {
  display: grid;
  grid-template-columns: 1fr;
  gap: 1.5rem;
  margin-top: 2rem;
}

@media screen and (min-width: 750px) {
  .application-tech-images {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media screen and (min-width: 990px) {
  .application-tech-images {
    grid-template-columns: repeat(3, 1fr);
  }
}

.application-tech-image {
  overflow: hidden;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.application-tech-image:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.1);
}

.tech-image {
  display: block;
  width: 100%;
  height: auto;
  object-fit: cover;
}

/* Texture Gallery */
.texture-gallery {
  display: grid;
  grid-template-columns: 1fr 1fr 1fr;
  gap: 0.5rem;
  margin-top: 1rem;
}

@media screen and (min-width: 750px) {
  .texture-gallery {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media screen and (min-width: 990px) {
  .texture-gallery {
    grid-template-columns: repeat(3, 1fr);
  }
}

.texture-item {
  overflow: hidden;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.texture-item:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.1);
}

.texture-link {
  display: block;
  cursor: pointer;
}

.texture-image {
  display: block;
  width: 100%;
  height: auto;
  object-fit: cover;
  aspect-ratio: 1/1;
  transition: transform 0.5s ease;
}

.texture-item:hover .texture-image {
  transform: scale(1.05);
}