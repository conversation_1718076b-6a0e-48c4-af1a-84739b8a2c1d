{{ 'section-custom-collection.css' | asset_url | stylesheet_tag: preload: true }}

<style>
  .custom-featured-collection-{{ section.id }} {
    --section-background: {{ section.settings.background_color }};
    --title-color: {{ section.settings.title_color }};
    --text-color: {{ section.settings.text_color }};
    --section-padding-top: {{ section.settings.padding_top | times: 0.75 | round: 0 }}px;
    --section-padding-bottom: {{ section.settings.padding_bottom | times: 0.75 | round: 0 }}px;
    --columns-desktop: {{ section.settings.columns_desktop }};
    --columns-mobile: {{ section.settings.columns_mobile }};
    --product-spacing-desktop: {{ section.settings.product_spacing }}px;
    --product-spacing-mobile: {{ section.settings.product_spacing_mobile }}px;
  }

  @media screen and (min-width: 750px) {
    .custom-featured-collection-{{ section.id }} {
      --section-padding-top: {{ section.settings.padding_top }}px;
      --section-padding-bottom: {{ section.settings.padding_bottom }}px;
    }
  }
</style>

<section class="custom-featured-collection custom-featured-collection-{{ section.id }}" id="section-{{ section.id }}" data-slider="{{ section.settings.enable_slider }}">
  <div class="page-width">
    <div class="custom-featured-collection__header">
      <div class="custom-featured-collection__p1">
        <div class="custom-featured-collection__title-container">
          <h2 class="section-title custom-featured-collection__title">{{ section.settings.title | escape }}</h2>
        </div>
      </div>

      {% if section.settings.enable_slider %}
        <div class="custom-featured-collection__header-right">
          <div class="custom-featured-collection__slider-nav">
            <button class="custom-featured-collection__slider-button custom-featured-collection__slider-prev" aria-label="{{ 'general.accessibility.previous_slide' | t }}">
              {% render 'icon-arrow-left' %}
            </button>
            <button class="custom-featured-collection__slider-button custom-featured-collection__slider-next" aria-label="{{ 'general.accessibility.next_slide' | t }}">
              {% render 'icon-arrow-right' %}
            </button>
          </div>
        </div>
      {% endif %}
    </div>

    {% if section.settings.enable_slider %}
      <div class="custom-featured-collection__slider">
        <ul class="custom-featured-collection__grid">
          {%- for product in section.settings.collection.products limit: section.settings.products_to_show -%}
            <li class="custom-product-card">
              <div class="custom-product-card__media">
                <a href="{{ product.url }}">
                  {%- if product.featured_media -%}
                    <img
                      srcset="
                        {%- if product.featured_media.width >= 165 -%}{{ product.featured_media | image_url: width: 165 }} 165w,{%- endif -%}
                        {%- if product.featured_media.width >= 360 -%}{{ product.featured_media | image_url: width: 360 }} 360w,{%- endif -%}
                        {%- if product.featured_media.width >= 533 -%}{{ product.featured_media | image_url: width: 533 }} 533w,{%- endif -%}
                        {%- if product.featured_media.width >= 720 -%}{{ product.featured_media | image_url: width: 720 }} 720w,{%- endif -%}
                        {{ product.featured_media | image_url }} {{ product.featured_media.width }}w
                      "
                      src="{{ product.featured_media | image_url: width: 533 }}"
                      loading="lazy"
                      alt="{{ product.featured_media.alt | escape }}"
                      width="{{ product.featured_media.width }}"
                      height="{{ product.featured_media.height }}"
                    >
                  {%- else -%}
                    {{ 'product-apparel' | placeholder_svg_tag: 'placeholder-svg' }}
                  {%- endif -%}
                </a>
                <a href="{{ product.url }}" class="custom-product-card__view-button">
                  VIEW PRODUCT {% render 'icon-arrow-right' %}
                </a>
              </div>
              <div class="custom-product-card__info">
                <div class="custom-product-card__collection-tag">
                  {{ product.collections.first.title }}
                </div>
                <h3 class="custom-product-card__title card__heading">
                  <a href="{{ product.url }}">{{ product.title | escape }}</a>
                </h3>

                <div class="custom-product-card__description">
                  <span>
                    {%- if product.metafields.custom.short_description != blank -%}
                      {{ product.metafields.custom.short_description | escape }}
                    {%- else -%}
                      {{ product.description | strip_html | truncatewords: 15 }}
                    {%- endif -%}
                  </span>
                </div>

                <div class="custom-product-card__price">
                  {% render 'price', product: product, show_compare_at_price: true %}
                </div>
              </div>
            </li>
          {%- else -%}
            {%- for i in (1..section.settings.products_to_show) -%}
              <li class="custom-product-card">
                <div class="custom-product-card__media">
                  <div class="custom-product-card__collection-tag">
                    Collection Name
                  </div>
                  {{ 'product-apparel' | placeholder_svg_tag: 'placeholder-svg' }}
                  <a href="#" class="custom-product-card__view-button">
                    VIEW PRODUCT {% render 'icon-arrow-right' %}
                  </a>
                </div>
                <div class="custom-product-card__info">
                  <h3 class="custom-product-card__title">
                    <a href="#">{{ 'onboarding.product_title' | t }}</a>
                  </h3>

                  <div class="custom-product-card__description">
                    <span>{{ 'onboarding.product_description' | t }}</span>
                  </div>

                  <div class="custom-product-card__price">
                    $XX.XX
                  </div>
                </div>
              </li>
            {%- endfor -%}
          {%- endfor -%}
        </ul>

        <!-- Mobile slider controls positioned in the middle of the slides -->
        <div class="custom-featured-collection__slider-mobile-nav">
          <button class="custom-featured-collection__slider-button custom-featured-collection__slider-prev" aria-label="{{ 'general.accessibility.previous_slide' | t }}">
            {% render 'icon-arrow-left' %}
          </button>
          <button class="custom-featured-collection__slider-button custom-featured-collection__slider-next" aria-label="{{ 'general.accessibility.next_slide' | t }}">
            {% render 'icon-arrow-right' %}
          </button>
        </div>
      </div>
    {% else %}
      <ul class="custom-featured-collection__grid">
        {%- for product in section.settings.collection.products limit: section.settings.products_to_show -%}
          <li class="custom-product-card">
            <div class="custom-product-card__media">
              <a href="{{ product.url }}">
                {%- if product.featured_media -%}
                  <img
                    srcset="
                      {%- if product.featured_media.width >= 165 -%}{{ product.featured_media | image_url: width: 165 }} 165w,{%- endif -%}
                      {%- if product.featured_media.width >= 360 -%}{{ product.featured_media | image_url: width: 360 }} 360w,{%- endif -%}
                      {%- if product.featured_media.width >= 533 -%}{{ product.featured_media | image_url: width: 533 }} 533w,{%- endif -%}
                      {%- if product.featured_media.width >= 720 -%}{{ product.featured_media | image_url: width: 720 }} 720w,{%- endif -%}
                      {{ product.featured_media | image_url }} {{ product.featured_media.width }}w
                    "
                    src="{{ product.featured_media | image_url: width: 533 }}"
                    loading="lazy"
                    alt="{{ product.featured_media.alt | escape }}"
                    width="{{ product.featured_media.width }}"
                    height="{{ product.featured_media.height }}"
                  >
                {%- else -%}
                  {{ 'product-apparel' | placeholder_svg_tag: 'placeholder-svg' }}
                {%- endif -%}
              </a>
              <a href="{{ product.url }}" class="custom-product-card__view-button">
                VIEW PRODUCT {% render 'icon-arrow-right' %}
              </a>
            </div>
            <div class="custom-product-card__info">
              <div class="custom-product-card__collection-tag">
                {{ product.collections.first.title }}
              </div>
              <h3 class="custom-product-card__title card__heading">
                <a href="{{ product.url }}">{{ product.title | escape }}</a>
              </h3>

              <div class="custom-product-card__description">
                <span>
                  {%- if product.metafields.custom.short_description != blank -%}
                    {{ product.metafields.custom.short_description | escape }}
                  {%- else -%}
                    {{ product.description | strip_html | truncatewords: 15 }}
                  {%- endif -%}
                </span>
              </div>

              <div class="custom-product-card__price">
                {% render 'price', product: product, show_compare_at_price: true %}
              </div>
            </div>
          </li>
        {%- else -%}
          {%- for i in (1..section.settings.products_to_show) -%}
            <li class="custom-product-card">
              <div class="custom-product-card__media">
                <div class="custom-product-card__collection-tag">
                  Collection Name
                </div>
                {{ 'product-apparel' | placeholder_svg_tag: 'placeholder-svg' }}
                <a href="#" class="custom-product-card__view-button">
                  VIEW PRODUCT {% render 'icon-arrow-right' %}
                </a>
              </div>
              <div class="custom-product-card__info">
                <h3 class="custom-product-card__title">
                  <a href="#">{{ 'onboarding.product_title' | t }}</a>
                </h3>

                <div class="custom-product-card__description">
                  <span>{{ 'onboarding.product_description' | t }}</span>
                </div>

                <div class="custom-product-card__price">
                  $XX.XX
                </div>
              </div>
            </li>
          {%- endfor -%}
        {%- endfor -%}
      </ul>
    {% endif %}

    {%- if section.settings.show_view_all and section.settings.collection != blank -%}
      <div class="custom-featured-collection__view-all">
        <a href="{{ section.settings.collection.url }}" class="custom-featured-collection__view-all-button">
          SHOP ALL {% render 'icon-arrow-right' %}
        </a>
      </div>
    {%- endif -%}

    {% if section.settings.description != blank %}
      <div class="custom-featured-collection__description-container">
        <div class="custom-featured-collection__description">{{ section.settings.description }}</div>
      </div>
    {% endif %}
  </div>
</section>

<script>
  if (typeof window.FeatureCollection === 'undefined') {
    window.FeatureCollection = class {
      constructor(container) {
        this.container = container;
        this.sliderEnabled = container.dataset.slider === 'true';
        this.productCards = container.querySelectorAll('.custom-product-card');
        this.init();
      }

      init() {
        this.initProductCardHover();
        this.initAnimations();
        this.initEqualHeights();

        if (this.sliderEnabled) {
          this.initSlider();
        }
      }

      initProductCardHover() {
        this.productCards.forEach(card => {
          card.addEventListener('mouseenter', function() {
            const button = this.querySelector('.custom-product-card__view-button');
            if (button) {
              button.style.transform = 'translateY(0)';
              button.style.opacity = '1';
            }
          });

          card.addEventListener('mouseleave', function() {
            const button = this.querySelector('.custom-product-card__view-button');
            if (button) {
              button.style.transform = 'translateY(100%)';
              button.style.opacity = '0';
            }
          });
        });
      }

      initAnimations() {
        if (typeof IntersectionObserver !== 'undefined') {
          const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
              if (entry.isIntersecting) {
                entry.target.classList.add('is-visible');
                observer.unobserve(entry.target);
              }
            });
          }, {
            threshold: 0.15,
            rootMargin: '0px 0px -50px 0px'
          });

          this.productCards.forEach(card => {
            observer.observe(card);
          });
        }
      }

      initEqualHeights() {
        // Function to equalize product title heights
        const equalizeHeights = () => {
          // Reset all heights first
          this.productCards.forEach(card => {
            const title = card.querySelector('.custom-product-card__title');
            const desc = card.querySelector('.custom-product-card__description');
            if (title) title.style.height = 'auto';
            if (desc) desc.style.height = 'auto';
          });

          // Get cards in same row (different approach for slider vs grid)
          if (this.sliderEnabled) {
            // For slider, we handle visible cards
            const visibleCards = Array.from(this.productCards).filter(card => {
              const rect = card.getBoundingClientRect();
              const containerRect = this.container.getBoundingClientRect();
              return (
                rect.left >= containerRect.left - rect.width / 2 &&
                rect.right <= containerRect.right + rect.width / 2
              );
            });
            this.equalizeElementsInCards(visibleCards);
          } else {
            // For grid, we need to group cards by row
            const gridContainer = this.container.querySelector('.custom-featured-collection__grid');
            if (!gridContainer) return;

            // Get computed columns from CSS variables
            let columnsCount;
            if (window.innerWidth >= 750) {
              columnsCount = getComputedStyle(this.container).getPropertyValue('--columns-desktop');
            } else {
              columnsCount = getComputedStyle(this.container).getPropertyValue('--columns-mobile');
            }
            columnsCount = parseInt(columnsCount) || 1;

            // Group cards by row and equalize heights within each row
            for (let i = 0; i < this.productCards.length; i += columnsCount) {
              const rowCards = Array.from(this.productCards).slice(i, i + columnsCount);
              this.equalizeElementsInCards(rowCards);
            }
          }
        };

        // Initial equalization
        if (this.productCards.length > 0) {
          // Run once when DOM is loaded
          if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', equalizeHeights);
          } else {
            equalizeHeights();
          }

          // Run again after images load
          window.addEventListener('load', equalizeHeights);

          // Re-equalize on resize
          let resizeTimer;
          window.addEventListener('resize', () => {
            clearTimeout(resizeTimer);
            resizeTimer = setTimeout(equalizeHeights, 250);
          });
        }
      }

      equalizeElementsInCards(cards) {
        if (!cards.length) return;

        // Equalize title heights
        let maxTitleHeight = 0;
        cards.forEach(card => {
          const title = card.querySelector('.custom-product-card__title');
          if (title) {
            const height = title.scrollHeight;
            if (height > maxTitleHeight) maxTitleHeight = height;
          }
        });

        cards.forEach(card => {
          const title = card.querySelector('.custom-product-card__title');
          if (title && maxTitleHeight > 0) {
            title.style.height = `${maxTitleHeight}px`;
          }
        });

        // Equalize description heights
        let maxDescHeight = 0;
        cards.forEach(card => {
          const desc = card.querySelector('.custom-product-card__description');
          if (desc) {
            const height = desc.scrollHeight;
            if (height > maxDescHeight) maxDescHeight = height;
          }
        });

        cards.forEach(card => {
          const desc = card.querySelector('.custom-product-card__description');
          if (desc && maxDescHeight > 0) {
            desc.style.height = `${maxDescHeight}px`;
          }
        });
      }

      initSlider() {
        const slider = this.container.querySelector('.custom-featured-collection__slider');
        if (!slider) return;

        const gridContainer = slider.querySelector('.custom-featured-collection__grid');

        // Get all slider navigation buttons (desktop and mobile)
        const prevButtons = this.container.querySelectorAll('.custom-featured-collection__slider-prev');
        const nextButtons = this.container.querySelectorAll('.custom-featured-collection__slider-next');

        if (!gridContainer || !prevButtons.length || !nextButtons.length) return;

        // Set initial state for all buttons
        this.updateSliderButtonStates(gridContainer, prevButtons, nextButtons);

        // Event listeners for all prev buttons
        prevButtons.forEach(button => {
          button.addEventListener('click', () => {
            this.scrollSlider(gridContainer, 'prev');
            this.updateSliderButtonStates(gridContainer, prevButtons, nextButtons);

            // Re-equalize heights after scroll (with a small delay)
            setTimeout(() => this.initEqualHeights(), 300);
          });
        });

        // Event listeners for all next buttons
        nextButtons.forEach(button => {
          button.addEventListener('click', () => {
            this.scrollSlider(gridContainer, 'next');
            this.updateSliderButtonStates(gridContainer, prevButtons, nextButtons);

            // Re-equalize heights after scroll (with a small delay)
            setTimeout(() => this.initEqualHeights(), 300);
          });
        });

        // Update button states on scroll
        gridContainer.addEventListener('scroll', () => {
          this.updateSliderButtonStates(gridContainer, prevButtons, nextButtons);

          // Re-equalize heights after scroll (throttled)
          clearTimeout(this.scrollTimer);
          this.scrollTimer = setTimeout(() => this.initEqualHeights(), 200);
        });

        // Update on resize
        window.addEventListener('resize', () => {
          this.updateSliderButtonStates(gridContainer, prevButtons, nextButtons);
        });
      }

      scrollSlider(container, direction) {
        const cardWidth = container.querySelector('.custom-product-card').offsetWidth;
        const gapWidth = parseInt(getComputedStyle(container).columnGap || '0');
        const scrollAmount = cardWidth + gapWidth;

        if (direction === 'prev') {
          container.scrollBy({ left: -scrollAmount, behavior: 'smooth' });
        } else {
          container.scrollBy({ left: scrollAmount, behavior: 'smooth' });
        }
      }

      updateSliderButtonStates(container, prevButtons, nextButtons) {
        const isAtStart = container.scrollLeft <= 0;
        const isAtEnd = container.scrollLeft >= (container.scrollWidth - container.clientWidth - 5); // 5px tolerance for browser differences

        // Update all prev buttons
        prevButtons.forEach(button => {
          button.disabled = isAtStart;
        });

        // Update all next buttons
        nextButtons.forEach(button => {
          button.disabled = isAtEnd;
        });
      }
    }
  }

  document.addEventListener('DOMContentLoaded', function() {
    const featuredCollectionSections = document.querySelectorAll('.custom-featured-collection');
    featuredCollectionSections.forEach(section => {
      new window.FeatureCollection(section);
    });
  });
</script>

{% schema %}
{
  "name": "Featured Collection Two",
  "tag": "section",
  "class": "section",
  "settings": [
    {
      "type": "text",
      "id": "title",
      "default": "Best Selling Paints",
      "label": "Title"
    },
    {
      "type": "richtext",
      "id": "description",
      "label": "Description",
      "default": "<p>Designed For DIY, our luxury paints require a small amount of practice, enabling even the most inexperienced of DIY'ers to create something truly special at home! To make life even easier, we include the right amount of primer for you.</p>"
    },
    {
      "type": "collection",
      "id": "collection",
      "label": "Collection"
    },
    {
      "type": "header",
      "content": "Layout Options"
    },
    {
      "type": "checkbox",
      "id": "enable_slider",
      "default": false,
      "label": "Enable slider"
    },
    {
      "type": "range",
      "id": "products_to_show",
      "min": 2,
      "max": 12,
      "step": 1,
      "default": 4,
      "label": "Products to show"
    },
    {
      "type": "range",
      "id": "columns_desktop",
      "min": 1,
      "max": 5,
      "step": 1,
      "default": 4,
      "label": "Number of columns on desktop"
    },
    {
      "type": "range",
      "id": "columns_mobile",
      "min": 1,
      "max": 3,
      "step": 1,
      "default": 2,
      "label": "Number of columns on mobile"
    },
    {
      "type": "range",
      "id": "product_spacing",
      "min": 10,
      "max": 50,
      "step": 5,
      "default": 25,
      "label": "Space between products (desktop)"
    },
    {
      "type": "range",
      "id": "product_spacing_mobile",
      "min": 5,
      "max": 30,
      "step": 5,
      "default": 15,
      "label": "Space between products (mobile)"
    },
    {
      "type": "header",
      "content": "Colors"
    },
    {
      "type": "color",
      "id": "title_color",
      "label": "Title color",
      "default": "#000000"
    },
    {
      "type": "color",
      "id": "text_color",
      "label": "Text color",
      "default": "#222222"
    },
    {
      "type": "color",
      "id": "background_color",
      "label": "Background color",
      "default": "#ffffff"
    },
    {
      "type": "header",
      "content": "View All Button"
    },
    {
      "type": "checkbox",
      "id": "show_view_all",
      "default": true,
      "label": "Show 'Shop All' button"
    },
    {
      "type": "header",
      "content": "Section Padding"
    },
    {
      "type": "range",
      "id": "padding_top",
      "min": 0,
      "max": 100,
      "step": 4,
      "unit": "px",
      "label": "Padding top",
      "default": 36
    },
    {
      "type": "range",
      "id": "padding_bottom",
      "min": 0,
      "max": 100,
      "step": 4,
      "unit": "px",
      "label": "Padding bottom",
      "default": 36
    }
  ],
  "presets": [
    {
      "name": "Featured Collection Two",
      "settings": {
        "title": "Best Selling Paints",
        "products_to_show": 4,
        "columns_desktop": 4,
        "show_view_all": true
      }
    }
  ]
}
{% endschema %}