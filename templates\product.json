/*
 * ------------------------------------------------------------
 * IMPORTANT: The contents of this file are auto-generated.
 *
 * This file may be updated by the Shopify admin theme editor
 * or related systems. Please exercise caution as any changes
 * made to this file may be overwritten.
 * ------------------------------------------------------------
 */
{
  "sections": {
    "rcl_main_product_pwDctb": {
      "type": "rcl-main-product",
      "blocks": {
        "title_8mkzDc": {
          "type": "title",
          "settings": {}
        },
        "price_4HQ9d9": {
          "type": "price",
          "settings": {}
        },
        "variant_picker_ydwCqD": {
          "type": "variant_picker",
          "settings": {
            "picker_type": "button"
          }
        },
        "quantity_selector_6irt4h": {
          "type": "quantity_selector",
          "settings": {}
        },
        "quantity_calculator_J8VrjU": {
          "type": "quantity_calculator",
          "settings": {
            "button_text": "Calculate quantity",
            "modal_heading": "Quantity Calculator"
          }
        },
        "buy_buttons_xQeBb7": {
          "type": "buy_buttons",
          "settings": {
            "show_dynamic_checkout": true
          }
        },
        "description_CL8eE9": {
          "type": "description",
          "settings": {}
        },
        "product_metafields_Dca3TT": {
          "type": "product_metafields",
          "settings": {}
        },
        "texture_gallery_TXyj93": {
          "type": "texture_gallery",
          "settings": {
            "title": "Gallery of textures",
            "show_count": true,
            "open_by_default": false
          }
        },
        "share_r3fNMN": {
          "type": "share",
          "disabled": true,
          "settings": {
            "share_label": "Share"
          }
        }
      },
      "block_order": [
        "title_8mkzDc",
        "price_4HQ9d9",
        "variant_picker_ydwCqD",
        "quantity_selector_6irt4h",
        "quantity_calculator_J8VrjU",
        "buy_buttons_xQeBb7",
        "description_CL8eE9",
        "product_metafields_Dca3TT",
        "texture_gallery_TXyj93",
        "share_r3fNMN"
      ],
      "settings": {
        "enable_sticky_info": true,
        "color_scheme": "",
        "media_size": "small",
        "gallery_layout": "thumbnail_slider",
        "media_position": "left",
        "image_zoom": "lightbox",
        "mobile_thumbnails": "hide",
        "enable_video_looping": false,
        "padding_top": 36,
        "padding_bottom": 36
      }
    },
    "rcl_product_additional_content_9NMrnT": {
      "type": "rcl-product-additional-content",
      "blocks": {
        "gallery_textures_kHwwJX": {
          "type": "gallery_textures",
          "disabled": true,
          "settings": {
            "title": "Gallery of textures",
            "show_count": true,
            "open_by_default": false
          }
        },
        "application_technologies_g3yhFD": {
          "type": "application_technologies",
          "settings": {
            "title": "Application technologies",
            "open_by_default": false
          }
        }
      },
      "block_order": [
        "gallery_textures_kHwwJX",
        "application_technologies_g3yhFD"
      ],
      "settings": {
        "color_scheme": "",
        "padding_top": 36,
        "padding_bottom": 36
      }
    },
    "rcl_video_reels_ehj6NB": {
      "type": "rcl-video-reels",
      "blocks": {
        "video_4zgLaE": {
          "type": "video",
          "settings": {
            "title": "{{ product.title }} Application Process/Technology",
            "description": "",
            "use_metafield": true,
            "metafield_path": "custom.application_video",
            "video_url": "",
            "use_appvideo_thumbnail": true
          }
        }
      },
      "block_order": [
        "video_4zgLaE"
      ],
      "settings": {
        "background_color": "rgba(0,0,0,0)",
        "title": "Application Video",
        "title_color": "#d1b073",
        "subtitle": "",
        "enable_lightbox": true,
        "play_on_hover": false,
        "enable_video_looping": false,
        "columns_desktop": 1,
        "columns_tablet": 1,
        "columns_mobile": 1,
        "padding_top": 36,
        "padding_bottom": 36
      }
    },
    "main": {
      "type": "main-product",
      "disabled": true,
      "settings": {
        "enable_sticky_info": true,
        "color_scheme": "scheme-1",
        "media_size": "large",
        "constrain_to_viewport": true,
        "media_fit": "contain",
        "gallery_layout": "stacked",
        "media_position": "left",
        "image_zoom": "lightbox",
        "mobile_thumbnails": "hide",
        "hide_variants": true,
        "enable_video_looping": false,
        "padding_top": 36,
        "padding_bottom": 12
      }
    },
    "related-products": {
      "type": "related-products",
      "settings": {
        "heading": "You may also like",
        "heading_size": "h2",
        "products_to_show": 4,
        "columns_desktop": 4,
        "color_scheme": "scheme-1",
        "image_ratio": "square",
        "image_shape": "default",
        "show_secondary_image": true,
        "show_vendor": false,
        "show_rating": false,
        "columns_mobile": "2",
        "padding_top": 36,
        "padding_bottom": 28
      }
    },
    "1736686931a6c1bb06": {
      "type": "apps",
      "blocks": {
        "judge_me_reviews_review_widget_AQDHpx": {
          "type": "shopify://apps/judge-me-reviews/blocks/review_widget/61ccd3b1-a9f2-4160-9fe9-4fec8413e5d8",
          "settings": {}
        }
      },
      "block_order": [
        "judge_me_reviews_review_widget_AQDHpx"
      ],
      "disabled": true,
      "settings": {
        "include_margins": true
      }
    }
  },
  "order": [
    "rcl_main_product_pwDctb",
    "rcl_product_additional_content_9NMrnT",
    "rcl_video_reels_ehj6NB",
    "main",
    "related-products",
    "1736686931a6c1bb06"
  ]
}
