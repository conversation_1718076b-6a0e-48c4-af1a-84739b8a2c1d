<script src="https://cdn.tailwindcss.com"></script>
<link rel="preconnect" href="https://fonts.googleapis.com">
<link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
<link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap" rel="stylesheet">

<style>
  .container-max-width { max-width: 1300px; margin-left: auto; margin-right: auto; padding-left: 1rem; padding-right: 1rem; }
  @media (min-width: 768px) { .container-max-width { padding-left: 2rem; padding-right: 2rem; } }
  #installer-map { background-color: #e9ecef; border: 1px solid #dee2e6; min-height: 400px; height: 100%; width: 100%; border-radius: 0.5rem; display: flex; align-items: center; justify-content: center; color: #6c757d; }
  .find-button { background-image: linear-gradient(to bottom, #e0c08f, #d4b783); color: #333; font-weight: 600; padding: 0.6rem 1.75rem; border-radius: 0.375rem; border: 1px solid #a18a61; transition: all 0.2s ease-in-out; cursor: pointer; box-shadow: 0 1px 2px 0 rgba(0,0,0,0.05); }
  .find-button:hover { background-image: linear-gradient(to bottom, #e8c99a, #d8bc8a); box-shadow: 0 4px 6px -1px rgba(0,0,0,0.1), 0 2px 4px -1px rgba(0,0,0,0.06); }
  .find-button:focus { outline: none; box-shadow: 0 0 0 3px rgba(212, 183, 131, 0.4); }
  .search-input { background-color: #ffffff; border: 1px solid #ced4da; border-radius: 0.375rem; padding: 0.6rem 0.85rem; width: 100%; transition: border-color 0.2s ease-in-out, box-shadow 0.2s ease-in-out; }
  .search-input:focus { outline: none; border-color: #c8a96f; box-shadow: 0 0 0 3px rgba(212, 183, 131, 0.3); }
  .installer-card { background-color: #ffffff; border-radius: 0.5rem; padding: 1.75rem; border: 1px solid #e5e7eb; margin-top: 0; box-shadow: 0 4px 6px rgba(0, 0, 0, 0.07); text-align: center; height: 100%; display: flex; flex-direction: column; justify-content: center; align-items: center; transition: all 0.2s ease-in-out; }
  .installer-card:hover { transform: translateY(-2px); box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.08), 0 4px 6px -2px rgba(0, 0, 0, 0.05); }
  .installer-image { width: 120px; height: 120px; background-color: #adb5bd; border-radius: 50%; margin: 0 auto 1.25rem; display: block; object-fit: cover; border: 2px solid white; box-shadow: 0 0 5px rgba(0,0,0,0.1); }
  .installer-placeholder-icon { width: 120px; height: 120px; background-color: #e9ecef; border-radius: 50%; margin: 0 auto 1.25rem; display: flex; align-items: center; justify-content: center; color: #6c757d; font-size: 3rem; border: 1px solid #dee2e6; }
  .loading-indicator { display: none; text-align: center; padding: 1.25rem; color: #6c757d; font-size: 1.1rem; }
  .loader { border: 4px solid #f3f3f3; border-top: 4px solid #d4b783; border-radius: 50%; width: 40px; height: 40px; animation: spin 1s linear infinite; margin: 0 auto 0.75rem; }
  @keyframes spin { 0% { transform: rotate(0deg); } 100% { transform: rotate(360deg); } }
  .section-heading { font-size: 1.25rem; font-weight: 600; color: #333; margin-bottom: 1rem; }
  .search-label { font-size: 0.95rem; font-weight: 600; color: #555; }
  .gm-style .gm-style-iw-c { padding: 8px !important; }
  .gm-style .gm-style-iw-d { overflow: hidden !important; padding: 0 !important; }
  #filtered-no-results-message { display: block; } 
  .your-location-marker { background-color: #D9534F; width: 16px; height: 16px; border-radius: 50%; border: 2px solid #FFF; box-shadow: 0 0 3px rgba(0,0,0,0.3); }
  .installer-marker { background-color: #4285F4; width: 16px; height: 16px; border-radius: 50%; border: 2px solid #FFF; box-shadow: 0 0 3px rgba(0,0,0,0.3); }
</style>

<div class="installer-finder-container container-max-width py-8 md:py-12">
  <div class="grid grid-cols-1 md:grid-cols-3 md:gap-8 lg:gap-12">

    <div class="md:col-span-1 space-y-6 order-1 md:order-1">
      <div class="bg-white p-6 rounded-lg shadow-md">
        <h2 class="text-2xl font-semibold mb-1 text-gray-800">FIND INSTALLER</h2>
        <h3 class="text-xl font-medium mb-4 text-gray-600">NEAR YOU</h3>
        <form id="installer-search-form" class="space-y-4">
          <div>
            <label for="postcode" class="block search-label mb-2">YOUR CITY OR POSTCODE</label>
            <input type="text" id="postcode" name="postcode" class="search-input" placeholder="Enter postcode or city" required>
          </div>
          <div>
            <label for="radius" class="block search-label mb-2">SEARCH IN RADIUS (MI)</label>
            <select id="radius" name="radius" class="search-input">
              <option value="5">5 miles</option> <option value="10" selected>10 miles</option> <option value="25">25 miles</option> <option value="50">50 miles</option> <option value="100">100 miles</option> <option value="250">250 miles</option>
            </select>
          </div>
          <button type="submit" class="find-button w-full mt-4">Find</button>
        </form>
      </div>
      <div id="installer-results-container" class="order-3 md:order-2 mt-8 md:mt-0">
         <h3 class="section-heading text-gray-700">INSTALLER NEAR YOU</h3>
         <div class="loading-indicator" id="loading"><div class="loader"></div> Searching...</div>
         <div id="installer-list" class="grid grid-cols-1 sm:grid-cols-2 gap-4 min-h-[50px]">
           
         </div>
         <p id="filtered-no-results-message" class="text-gray-500 italic text-center text-lg mt-4">Enter your location and click 'Find' to search for installers.</p>
      </div>
    </div>

    <div class="md:col-span-2 order-2 md:order-3 mt-8 md:mt-0">
       <div id="installer-map">Map loading...</div>
    </div>

  </div>
</div>

<div class="all-installers-container container-max-width py-8 md:py-12 mt-8 md:mt-12 border-t border-gray-200">
    <h2 class="text-2xl font-semibold text-gray-800 mb-6 border-b pb-3">All Installers</h2>
    <div id="all-installers-list" class="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6">
        <p id="all-no-installers-message" class="text-gray-500 italic col-span-full text-center text-lg">No installers available.</p>
    </div>
</div>

{% assign installers_liquid = shop.metaobjects.list_of_installers.values %}

<script>
  let map;
  let geocoder;
  let userMarker = null;
  let radiusCircle = null;
  const installerMarkers = [];
  const apiKey = 'AIzaSyDRczbml55FBsJV9deQ68fuiXeA-qMzBPU';
  const mapInfoWindows = [];

  const installersData = [
    {% for installer in installers_liquid %}
      {
        // IMPORTANT: It's highly recommended to fix Liquid to output a real ID here
        id: {{ installer.id | json }}, // This might still be null, we'll use index as workaround
        company_name: {{ installer.company_name | default: '' | json }},
        installer_name: {{ installer.installer_name | default: '' | json }},
        contact_number: {{ installer.contact_number | default: '' | json }},
        city_location: {{ installer.city_location | default: null | json }},
        locations_geocoded: [],
        closest_location: null,
        image: {% if installer.image %}{ src: {{ installer.image | image_url: width: 240 | json }}, alt: {{ installer.image.alt | default: installer.company_name | default: 'Installer image' | json }} }{% else %}null{% endif %}
        {% unless installer.city_location %}, _error: "Missing city_location data"{% endunless %}
      },
    {% endfor %}
  ];

  const validInstallers = installersData.filter(inst => inst.city_location && typeof inst.city_location === 'string' && inst.city_location.trim() !== '' && !inst._error);

  async function initMap() {
    let mapElement = document.getElementById('installer-map');
    if (!mapElement) {
      console.error("Map element not found!");
      return;
    }

    try {
      const { Map, InfoWindow } = await google.maps.importLibrary("maps");
      const { Geocoder } = await google.maps.importLibrary("geocoding");

      map = new Map(mapElement, {
        center: { lat: 54.0, lng: -2.0 },
        zoom: 6,
        mapId: 'INSTALLER_FINDER_MAP_V3',
        mapTypeControl: false,
        streetViewControl: false,
        fullscreenControl: true,
      });

      geocoder = new Geocoder();

      const searchForm = document.getElementById('installer-search-form');
      if (searchForm) {
        searchForm.addEventListener('submit', (event) => {
          handleSearch(event);
        });
      } else {
        console.error("Search form not found.");
      }

      const allListEl = document.getElementById('all-installers-list');
      const noInstallersMsgEl = document.getElementById('all-no-installers-message');
      if (allListEl && noInstallersMsgEl) {
         if (validInstallers.length === 0) {
            console.warn("No valid installers found after initial data processing. Check Liquid template output for 'city_location' string.");
        }
        displayAllInstallers(validInstallers, allListEl, noInstallersMsgEl);
      } else {
        console.error("Could not find elements for 'All Installers' list.");
      }

    } catch (error) {
      console.error("Error loading Google Maps libraries or initializing map:", error);
      mapElement.innerHTML = '<p style="padding: 20px; text-align: center; color: red;">Error initializing map: ' + error.message + '</p>';

      const allList = document.getElementById('all-installers-list');
      const noMsg = document.getElementById('all-no-installers-message');
      if(allList && noMsg) {
         if (validInstallers.length === 0) {
            console.warn("No valid installers found after initial data processing (map load error). Check Liquid template output for 'city_location' string.");
        }
        displayAllInstallers(validInstallers, allList, noMsg);
      }
    }
  }

  function getDistanceInMiles(lat1, lon1, lat2, lon2) {
    if (typeof lat1 !== 'number' || typeof lon1 !== 'number' || typeof lat2 !== 'number' || typeof lon2 !== 'number' ||
        !isFinite(lat1) || !isFinite(lon1) || !isFinite(lat2) || !isFinite(lon2)) {
        console.error("Invalid input to getDistanceInMiles:", {lat1, lon1, lat2, lon2});
        return Infinity;
    }
    const R = 3958.8;
    const dLat = (lat2 - lat1) * Math.PI / 180;
    const dLon = (lon2 - lon1) * Math.PI / 180;
    const a = Math.sin(dLat / 2) * Math.sin(dLat / 2) +
              Math.cos(lat1 * Math.PI / 180) * Math.cos(lat2 * Math.PI / 180) *
              Math.sin(dLon / 2) * Math.sin(dLon / 2);
    const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));
    const distance = R * c;
    return isFinite(distance) ? distance : Infinity;
  }

  function clearMapOverlays() {
    if (userMarker) {
      userMarker.setMap(null);
      userMarker = null;
    }
    installerMarkers.forEach(marker => {
      if (marker) marker.setMap(null);
    });
    installerMarkers.length = 0;
    if (radiusCircle) {
      radiusCircle.setMap(null);
      radiusCircle = null;
    }
    mapInfoWindows.forEach(iw => {
      if (iw) iw.close();
    });
    mapInfoWindows.length = 0;
  }

  function displayInstallersList(installers, listEl, noResultsEl) {
    if (!listEl || !noResultsEl) {
      console.error("displayInstallersList: Missing element references.");
      return;
    }
    listEl.innerHTML = '';
    if (installers.length === 0) {
      noResultsEl.textContent = 'No installers found within the selected radius.';
      noResultsEl.style.display = 'block';
      return;
    }
    noResultsEl.style.display = 'none';
    installers.sort((a, b) => (a.closest_location?.distance ?? Infinity) - (b.closest_location?.distance ?? Infinity));

    installers.forEach(installer => {
      const card = document.createElement('div');
      card.className = 'installer-card';

      let imageHtml = installer.image && installer.image.src
        ? `<img src="${installer.image.src}" alt="${installer.image.alt}" class="installer-image">`
        : `<div class="installer-placeholder-icon">👤</div>`;

      let distanceHtml = '<p class="text-base text-gray-500 mt-2">Distance: Unavailable</p>';
      if (installer.closest_location && typeof installer.closest_location.distance === 'number' && isFinite(installer.closest_location.distance)) {
          distanceHtml = `<p class="text-base text-gray-500 mt-2">Distance: ${installer.closest_location.distance.toFixed(1)} miles (nearest location)</p>`;
      }

      card.innerHTML = `${imageHtml}
        ${installer.company_name ? `<h4 class="font-semibold text-xl text-gray-800 mb-2">${installer.company_name}</h4>` : ''}
        ${installer.installer_name ? `<p class="text-lg text-gray-600 mb-2">${installer.installer_name}</p>` : ''}
        ${installer.city_location ? `<p class="text-base text-gray-500 mt-2">Located in: ${installer.city_location}</p>` : ''}
        ${installer.contact_number ? `<p class="text-base text-gray-500 mt-1">Phone: ${installer.contact_number}</p>` : ''}
        ${distanceHtml}`;

      listEl.appendChild(card);
    });
  }

  function displayAllInstallers(allValidInstallers, allListEl, noInstallersMsgEl) {
    if (!allListEl || !noInstallersMsgEl) {
      console.error("displayAllInstallers: Missing element references.");
      return;
    }
    allListEl.innerHTML = '';
    if (!allValidInstallers || allValidInstallers.length === 0) {
      noInstallersMsgEl.textContent = 'No installers available.';
      noInstallersMsgEl.style.display = 'block';
      return;
    }
    noInstallersMsgEl.style.display = 'none';
    allValidInstallers.sort((a, b) => (a.company_name || '').localeCompare(b.company_name || ''));

    allValidInstallers.forEach(installer => {
      const card = document.createElement('div');
      card.className = 'installer-card';

      let imageHtml = installer.image && installer.image.src
        ? `<img src="${installer.image.src}" alt="${installer.image.alt}" class="installer-image">`
        : `<div class="installer-placeholder-icon">👤</div>`;

      card.innerHTML = `${imageHtml}
        ${installer.company_name ? `<h4 class="font-semibold text-xl text-gray-800 mb-2">${installer.company_name}</h4>` : ''}
        ${installer.installer_name ? `<p class="text-lg text-gray-600 mb-2">${installer.installer_name}</p>` : ''}
        ${installer.city_location ? `<p class="text-base text-gray-500 mt-2">Located in: ${installer.city_location}</p>` : ''}
        ${installer.contact_number ? `<p class="text-base text-gray-500 mt-1">Phone: ${installer.contact_number}</p>` : ''}`;

      allListEl.appendChild(card);
    });
  }

  async function addMarkersAndCircle(userLoc, installers, radiusMiles) {
    clearMapOverlays();
    const bounds = new google.maps.LatLngBounds();
    const infoWindow = new google.maps.InfoWindow();
    mapInfoWindows.push(infoWindow);

    const userPosition = { lat: userLoc.lat(), lng: userLoc.lng() };
    try {
      userMarker = new google.maps.Marker({
        position: userPosition,
        map: map,
        title: 'Your Search Location',
        icon: {
          path: google.maps.SymbolPath.CIRCLE,
          fillColor: '#D9534F',
          fillOpacity: 1,
          strokeColor: '#FFFFFF',
          strokeWeight: 2,
          scale: 8
        },
        zIndex: 1000
      });
      google.maps.event.addListener(userMarker, 'click', function() {
        infoWindow.close();
        infoWindow.setContent(`
          <div style="font-family: inherit; padding: 10px; max-width: 200px;">
            <h4 style="margin: 0 0 5px 0; font-weight: 600; color: #D9534F;">Your Search Location</h4>
            <p style="margin: 0; font-size: 0.9em;">This pin shows your search center point.</p>
          </div>
        `);
        infoWindow.open(map, userMarker);
      });
      bounds.extend(userPosition);
    } catch (e) {
      console.error("Error creating user marker:", e);
    }

    const radiusMeters = radiusMiles * 1609.34;
    try {
      radiusCircle = new google.maps.Circle({
        strokeColor: '#D9534F',
        strokeOpacity: 0.7,
        strokeWeight: 1.5,
        fillColor: '#D9534F',
        fillOpacity: 0.15,
        map: map,
        center: userPosition,
        radius: radiusMeters
      });
    } catch (e) {
      console.error("Error creating radius circle:", e);
    }

    const locationGroups = new Map();
    installers.forEach(installer => {
        // Use closest_location for grouping, ensure it's valid
        if (installer.closest_location && typeof installer.closest_location.lat === 'number' && typeof installer.closest_location.lng === 'number') {
            const key = `${installer.closest_location.lat.toFixed(5)},${installer.closest_location.lng.toFixed(5)}`;
            if (!locationGroups.has(key)) locationGroups.set(key, []);
            locationGroups.get(key).push(installer);
        } else {
             console.warn(`Installer ${installer.id || installer.company_name} missing valid closest_location for marker grouping.`);
        }
    });

    const offsetMagnitude = 0.0005;

    locationGroups.forEach((group, key) => {
        const baseLat = parseFloat(key.split(',')[0]);
        const baseLng = parseFloat(key.split(',')[1]);
        const isOverlap = group.length > 1;

        group.forEach((installer, index) => {
            // Double check closest_location before using
             if (!installer.closest_location || typeof installer.closest_location.lat !== 'number' || typeof installer.closest_location.lng !== 'number') {
                console.warn(`Skipping marker for installer ${installer.id || installer.company_name} due to invalid closest_location data.`);
                return;
            }

            let markerLat = installer.closest_location.lat;
            let markerLng = installer.closest_location.lng;

            if (isOverlap) {
                markerLat += (Math.random() - 0.5) * 2 * offsetMagnitude;
                markerLng += (Math.random() - 0.5) * 2 * offsetMagnitude;
            }

            const position = { lat: markerLat, lng: markerLng };

            try {
              const marker = new google.maps.Marker({
                  position: position,
                  map: map,
                  title: `${installer.company_name || ''}\nLocated in: ${installer.city_location || ''}`,
                  icon: {
                       path: google.maps.SymbolPath.MARKER,
                       fillColor: '#4285F4',
                       fillOpacity: 1,
                       strokeColor: '#FFFFFF',
                       strokeWeight: 1,
                       scale: 6
                   },
                  zIndex: 900
              });

              let distanceText = 'Distance: Unavailable';
              if (installer.closest_location && typeof installer.closest_location.distance === 'number' && isFinite(installer.closest_location.distance)) {
                  distanceText = `Distance: ${installer.closest_location.distance.toFixed(1)} miles (nearest)`;
              }

              const contentString = `
                <div style="font-family: inherit; padding: 10px; max-width: 200px;">
                  ${installer.company_name ? `<h4 style="margin: 0 0 5px 0; font-weight: 600;">${installer.company_name}</h4>` : ''}
                  ${installer.installer_name ? `<p style="margin: 0 0 3px 0;">${installer.installer_name}</p>` : ''}
                  ${installer.city_location ? `<p style="margin: 0 0 3px 0; font-size: 0.9em;">Located in: ${installer.city_location}</p>` : ''}
                  ${installer.contact_number ? `<p style="margin: 0 0 3px 0; font-size: 0.9em;">Phone: ${installer.contact_number}</p>` : ''}
                  <p style="margin: 5px 0 0 0; font-size: 0.85em;">${distanceText}</p>
                </div>`;

              google.maps.event.addListener(marker, 'click', function() {
                  infoWindow.close();
                  infoWindow.setContent(contentString);
                  infoWindow.open(map, marker);
              });

              installerMarkers.push(marker);
              bounds.extend(position);
            } catch (e) {
              console.error(`Error creating installer marker for ${installer.company_name || installer.id}:`, e);
            }
        });
    });

    if (!bounds.isEmpty()) {
      map.fitBounds(bounds);
      const listener = google.maps.event.addListenerOnce(map, "idle", function() {
        let zoom = map.getZoom();
        if (installers.length === 0 && zoom < 9) zoom = 9;
        else if (zoom > 15) zoom = 15;
        if (map.getZoom() !== zoom) {
            map.setZoom(zoom);
        }
      });
    } else if (radiusCircle) {
      try {
        const circleBounds = radiusCircle.getBounds();
        if (circleBounds && !circleBounds.isEmpty()) map.fitBounds(circleBounds);
        else { map.setCenter(userPosition); map.setZoom(11); }
      } catch (e) {
        console.error("Error fitting to circle bounds:", e);
        map.setCenter(userPosition); map.setZoom(11);
      }
    } else {
      map.setCenter({ lat: 54.0, lng: -2.0 }); map.setZoom(6);
    }
  }

  async function handleSearch(event) {
    event.preventDefault();

    const postcodeEl = document.querySelector('#postcode, input[name="postcode"]');
    const radiusEl = document.querySelector('#radius, select[name="radius"]');
    const loadingElement = document.querySelector('#loading, .loading-indicator');
    const listElement = document.querySelector('#installer-list');
    const noResultsElement = document.querySelector('#filtered-no-results-message');

    if (!postcodeEl || !radiusEl || !loadingElement || !listElement || !noResultsElement) {
      console.error("Search cannot proceed: Missing required HTML elements");
      if (noResultsElement) {
        noResultsElement.textContent = 'Error: Page structure problem prevents search (elements missing).';
        noResultsElement.style.display = 'block';
      } else if (listElement) {
        listElement.innerHTML = '<p class="text-center text-red-500">Error: Page structure problem prevents search (elements missing).</p>';
      } else {
        alert('Error: Page structure problem prevents search (elements missing).');
      }
      return;
    }

    if (!geocoder) {
      console.error("Geocoder not initialized yet.");
      noResultsElement.textContent = 'Error: Map services not ready. Please try again in a few moments.';
      noResultsElement.style.display = 'block';
      return;
    }

    const postcode = postcodeEl.value;
    const radiusMiles = parseFloat(radiusEl.value);

    if (!postcode) { alert('Please enter a city or postcode.'); return; }

    loadingElement.style.display = 'block';
    listElement.innerHTML = '';
    noResultsElement.style.display = 'none';
    clearMapOverlays();

     if (validInstallers.length === 0) {
        console.warn("Search initiated, but no valid installers were found initially. Check Liquid template output for 'city_location' string.");
        noResultsElement.textContent = 'No installers available to search.';
        noResultsElement.style.display = 'block';
        loadingElement.style.display = 'none';
        return;
    }

    try {
      // Add component restriction for UK bias
      const geocodeRequestUser = {
          address: postcode,
          componentRestrictions: { country: 'GB' }
      };
      const geocodeResults = await geocoder.geocode(geocodeRequestUser);
      if (!geocodeResults || !geocodeResults.results || geocodeResults.results.length === 0) {
        throw new Error('Could not find location for the entered address/postcode.');
      }

      const userLocation = geocodeResults.results[0].geometry.location;
      const userLat = userLocation.lat();
      const userLng = userLocation.lng();
      // Basic check for obviously wrong user coordinates (e.g., null island or errors)
      if (typeof userLat !== 'number' || typeof userLng !== 'number' || !isFinite(userLat) || !isFinite(userLng)) {
          throw new Error('Failed to get valid coordinates for your search location.');
      }


      validInstallers.forEach(inst => {
          inst.locations_geocoded = [];
          inst.closest_location = null;
          inst._geocodeFailedCompletely = true;
      });

      const geocodeTasks = [];
      validInstallers.forEach((installer, index) => { // Get index here
          if (installer.city_location && typeof installer.city_location === 'string') {
              const locations = installer.city_location.split(',')
                                     .map(loc => loc.trim())
                                     .filter(loc => loc !== '');

              if (locations.length > 0) {
                  locations.forEach(locationName => {
                      // Use index as the identifier in the task
                      geocodeTasks.push({ installerIndex: index, locationName: locationName });
                  });
              } else {
                  console.warn(`Installer at index ${index} has empty city_location string after processing: "${installer.city_location}"`);
              }
          } else {
               console.warn(`Installer at index ${index} has invalid or missing city_location string:`, installer.city_location);
          }
      });

      if (geocodeTasks.length === 0) {
          console.warn("No valid locations found in installersData to geocode after splitting strings.");
          noResultsElement.textContent = 'No installer locations available to search.';
          noResultsElement.style.display = 'block';
          loadingElement.style.display = 'none';
          return;
      }

      const geocodePromises = geocodeTasks.map(task => {
          // Add component restriction for UK bias here too
          const geocodeRequestInstaller = {
              address: task.locationName,
              componentRestrictions: { country: 'GB' }
          };
          return geocoder.geocode(geocodeRequestInstaller)
              .then(result => ({ status: 'fulfilled', value: result, task: task }))
              .catch(error => ({ status: 'rejected', reason: { error: error, task: task } }));
      });
      const geocodeSettledResults = await Promise.allSettled(geocodePromises);

      geocodeSettledResults.forEach(settledResult => {
          if (settledResult.status === 'fulfilled') {
              const result = settledResult.value;
              // Find the installer using the index from the task
              const installer = validInstallers[result.task.installerIndex];
              if (!installer) {
                  console.error(`Could not find installer for index ${result.task.installerIndex}`);
                  return;
              }

              if (result.value && result.value.results && result.value.results.length > 0) {
                  const loc = result.value.results[0].geometry.location;
                  const lat = loc.lat();
                  const lng = loc.lng();
                  if (typeof lat === 'number' && typeof lng === 'number' && isFinite(lat) && isFinite(lng)) {
                      installer.locations_geocoded.push({
                          name: result.task.locationName,
                          lat: lat,
                          lng: lng
                      });
                      installer._geocodeFailedCompletely = false;
                  } else {
                      console.warn(`Geocode succeeded but returned invalid coordinates for installer index ${result.task.installerIndex} at location: ${result.task.locationName}`, loc);
                  }
              } else {
                  console.warn(`Geocode FAILED (ZERO_RESULTS) for installer index ${result.task.installerIndex} at location: ${result.task.locationName}`);
              }
          } else {
              const taskInfo = settledResult.reason?.task;
              const errorInfo = settledResult.reason?.error;
              const installerIndex = taskInfo?.installerIndex ?? 'unknown';
              const locationName = taskInfo?.locationName || 'unknown';
              console.warn(`Geocode REJECTED for installer index ${installerIndex} at location: ${locationName}`, errorInfo || settledResult.reason);
          }
      });

      const nearbyInstallers = [];
      validInstallers.forEach((installer, index) => { // Keep track of index for logging if needed
          if (!installer._geocodeFailedCompletely && installer.locations_geocoded.length > 0) {
              let minDistance = Infinity;
              let closestData = null;
              let isNearby = false;

              installer.locations_geocoded.forEach(geoLoc => {
                   try {
                      if (typeof geoLoc.lat !== 'number' || typeof geoLoc.lng !== 'number' || !isFinite(geoLoc.lat) || !isFinite(geoLoc.lng)) {
                          console.warn(`Skipping distance calculation for invalid coordinates for installer index ${index}:`, geoLoc);
                          return;
                      }

                      const distance = getDistanceInMiles(userLat, userLng, geoLoc.lat, geoLoc.lng);

                      if (isFinite(distance)) {
                          if (distance < minDistance) {
                              minDistance = distance;
                              closestData = { lat: geoLoc.lat, lng: geoLoc.lng, distance: minDistance, name: geoLoc.name };
                          }
                          if (distance <= radiusMiles) {
                              isNearby = true;
                          }
                      } else {
                          console.warn(`Distance calculation returned invalid value for installer index ${index}, location ${geoLoc.name}`);
                      }
                   } catch (distError) {
                       console.error(`Error calculating distance for installer index ${index}, location ${geoLoc.name}:`, distError);
                   }
              });

              installer.closest_location = closestData;

              if (isNearby) {
                  nearbyInstallers.push(installer);
              }
          }
      });

      displayInstallersList(nearbyInstallers, listElement, noResultsElement);
      addMarkersAndCircle(userLocation, nearbyInstallers, radiusMiles);

    } catch (error) {
      console.error('Search Error:', error);
      if (noResultsElement) {
        if (error.code === 'ZERO_RESULTS' || (error.message && error.message.includes('Could not find location'))) {
          noResultsElement.textContent = 'Error: Could not find location for the entered address/postcode.';
        } else if (error.message) {
          noResultsElement.textContent = `Error: ${error.message}`;
        } else {
          noResultsElement.textContent = 'An unexpected error occurred during the search.';
        }
        noResultsElement.style.display = 'block';
      }
      if(listElement) listElement.innerHTML = '';
      clearMapOverlays();
    } finally {
      if(loadingElement) loadingElement.style.display = 'none';
    }
  }

  async function loadGoogleMapsScript() {
    if (typeof google === 'object' && typeof google.maps === 'object') {
      if (typeof initMap === 'function') initMap();
    } else {
      window.initMap = initMap;
      const script = document.createElement('script');
      script.src = `https://maps.googleapis.com/maps/api/js?key=${apiKey}&callback=initMap&libraries=geocoding,maps&v=weekly`;
      script.async = true;
      script.defer = true;
      script.onerror = () => {
        console.error("Google Maps script failed to load.");
        const mapEl = document.getElementById('installer-map');
        if (mapEl) {
            mapEl.innerHTML = '<p style="padding: 20px; text-align: center; color: red;">Error: Could not load Google Maps.</p>';
        }
        const allList = document.getElementById('all-installers-list');
        const noMsg = document.getElementById('all-no-installers-message');
        if(allList && noMsg) {
             if (validInstallers.length === 0) {
                console.warn("No valid installers found (map script load error). Check Liquid template output for 'city_location' string.");
             }
            displayAllInstallers(validInstallers, allList, noMsg);
        }
        delete window.initMap;
      };
      document.head.appendChild(script);
    }
  }

  if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', loadGoogleMapsScript);
  } else {
    loadGoogleMapsScript();
  }
</script>

<script src="{{ 'announcement-bar-fix.js' | asset_url }}" defer></script>

{% schema %}
{
  "name": "Installer Finder Map",
  "tag": "section",
  "settings": [
    {
      "type": "paragraph",
      "content": "Displays an interactive map to find installers based on location. Requires Google Maps API Key and 'list_of_installers' metaobject with a 'city_location' text field (e.g., 'Manchester')."
    },
    {
      "type": "text",
      "id": "google_maps_api_key",
      "label": "Google Maps API Key",
      "info": "Required for map functionality. Ensure Maps JavaScript API & Geocoding API are enabled."
    }
  ],
  "presets": [
    {
      "name": "Installer Finder Map"
    }
  ]
}
{% endschema %}