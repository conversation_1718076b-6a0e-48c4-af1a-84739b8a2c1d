/**
 * Enhanced Related Products
 * 
 * This script adds additional functionality to the enhanced related products section:
 * - Lazy loading of products when scrolling into view
 * - Smooth animations for product cards
 * - Product card hover effects
 * - Optional product quick view
 */

class EnhancedRelatedProducts {
  constructor(container) {
    this.container = container;
    this.productCards = container.querySelectorAll('.card');
    this.init();
  }

  init() {
    this.initLazyLoading();
    this.initProductCardHover();
  }

  initLazyLoading() {
    if ('IntersectionObserver' in window) {
      const options = {
        root: null,
        rootMargin: '0px',
        threshold: 0.1
      };

      const observer = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
          if (entry.isIntersecting) {
            entry.target.classList.add('is-visible');
            observer.unobserve(entry.target);
          }
        });
      }, options);

      this.productCards.forEach(card => {
        // Add initial state class
        card.classList.add('animate-card');
        // Observe the card
        observer.observe(card);
      });
    } else {
      // Fallback for browsers that don't support IntersectionObserver
      this.productCards.forEach(card => {
        card.classList.add('is-visible');
      });
    }
  }

  initProductCardHover() {
    this.productCards.forEach(card => {
      card.addEventListener('mouseenter', function() {
        this.classList.add('card-hover');
      });
      
      card.addEventListener('mouseleave', function() {
        this.classList.remove('card-hover');
      });
    });
  }
}

// Initialize on DOMContentLoaded
document.addEventListener('DOMContentLoaded', function() {
  const enhancedRelatedProductsContainers = document.querySelectorAll('.enhanced-related-products');
  enhancedRelatedProductsContainers.forEach(container => {
    new EnhancedRelatedProducts(container);
  });
});
