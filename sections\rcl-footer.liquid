{{ 'rcl-section-footer.css' | asset_url | stylesheet_tag: preload: true }}

<footer class="footer" style="
  --footer-background-color: {{ section.settings.background_color }};
  --footer-text-color: {{ section.settings.text_color }};
  --footer-heading-color: {{ section.settings.heading_color }};
  --footer-link-hover-color: {{ section.settings.link_hover_color }};
  --footer-border-color: {{ section.settings.border_color }};
  --footer-form-background: {{ section.settings.form_background }};
  --footer-form-text: {{ section.settings.form_text_color }};
  --footer-button-background: {{ section.settings.button_background }};
  --footer-button-text: {{ section.settings.button_text_color }};
  --footer-button-hover-background: {{ section.settings.button_hover_background }};
  --footer-social-icon-color: {{ section.settings.social_icon_color }};
  --footer-social-icon-hover-color: {{ section.settings.social_icon_hover_color }};
">
  <div class="page-width">
    <div class="footer-grid">
      {% for block in section.blocks %}
        {% case block.type %}
        
          {% when 'menu' %}
            <div class="footer-block" {{ block.shopify_attributes }}>
              {% if block.settings.heading != blank %}
                <h2 class="footer-block__heading">{{ block.settings.heading }}</h2>
              {% endif %}
              
              {% if block.settings.menu != blank %}
                <ul class="footer-block__menu">
                  {%- for link in linklists[block.settings.menu].links -%}
                    <li class="footer-block__menu-item">
                      <a href="{{ link.url }}" class="footer-block__menu-link">
                        {{ link.title }}
                      </a>
                    </li>
                  {%- endfor -%}
                </ul>
              {% endif %}
            </div>
            
          {% when 'text' %}
            <div class="footer-block" {{ block.shopify_attributes }}>
              {% if block.settings.heading != blank %}
                <h2 class="footer-block__heading">{{ block.settings.heading }}</h2>
              {% endif %}
              
              {% if block.settings.text != blank %}
                <div class="footer-block__text rte">
                  {{ block.settings.text }}
                </div>
              {% endif %}
            </div>
            
          {% when 'location' %}
            <div class="footer-block" {{ block.shopify_attributes }}>
              {% if block.settings.heading != blank %}
                <h2 class="footer-block__heading">{{ block.settings.heading }}</h2>
              {% endif %}
              
              {% if block.settings.address != blank %}
                <div class="footer-block__location">
                  {{ block.settings.address }}
                </div>
              {% endif %}
              
              {% if block.settings.show_hours %}
                <div class="footer-block__hours">
                  {% if block.settings.hours_heading != blank %}
                    <h3 class="footer-block__subheading">{{ block.settings.hours_heading }}</h3>
                  {% endif %}
                  
                  {% if block.settings.monday_hours != blank %}
                    <div class="footer-block__hours-item">
                      <span>Monday</span>
                      <span>{{ block.settings.monday_hours }}</span>
                    </div>
                  {% endif %}
                  
                  {% if block.settings.tuesday_hours != blank %}
                    <div class="footer-block__hours-item">
                      <span>Tuesday</span>
                      <span>{{ block.settings.tuesday_hours }}</span>
                    </div>
                  {% endif %}
                  
                  {% if block.settings.wednesday_hours != blank %}
                    <div class="footer-block__hours-item">
                      <span>Wednesday</span>
                      <span>{{ block.settings.wednesday_hours }}</span>
                    </div>
                  {% endif %}
                  
                  {% if block.settings.thursday_hours != blank %}
                    <div class="footer-block__hours-item">
                      <span>Thursday</span>
                      <span>{{ block.settings.thursday_hours }}</span>
                    </div>
                  {% endif %}
                  
                  {% if block.settings.friday_hours != blank %}
                    <div class="footer-block__hours-item">
                      <span>Friday</span>
                      <span>{{ block.settings.friday_hours }}</span>
                    </div>
                  {% endif %}
                  
                  {% if block.settings.saturday_hours != blank %}
                    <div class="footer-block__hours-item">
                      <span>Saturday</span>
                      <span>{{ block.settings.saturday_hours }}</span>
                    </div>
                  {% endif %}
                  
                  {% if block.settings.sunday_hours != blank %}
                    <div class="footer-block__hours-item">
                      <span>Sunday</span>
                      <span>{{ block.settings.sunday_hours }}</span>
                    </div>
                  {% endif %}
                </div>
              {% endif %}
            </div>
            
          {% when 'map' %}
            <div class="footer-block" {{ block.shopify_attributes }}>
              {% if block.settings.heading != blank %}
                <h2 class="footer-block__heading">{{ block.settings.heading }}</h2>
              {% endif %}
              
              {% if block.settings.map_url != blank %}
                <div class="footer-block__map">
                  <iframe 
                    src="{{ block.settings.map_url }}" 
                    width="100%" 
                    height="100%" 
                    frameborder="0" 
                    style="border:0;" 
                    allowfullscreen="" 
                    aria-hidden="false" 
                    tabindex="0">
                  </iframe>
                </div>
              {% endif %}
            </div>
            
          {% when 'newsletter' %}
            <div class="footer-block" {{ block.shopify_attributes }}>
              {% if block.settings.heading != blank %}
                <h2 class="footer-block__heading">{{ block.settings.heading }}</h2>
              {% endif %}
              
              {% if block.settings.subtext != blank %}
                <div class="footer-block__text">
                  {{ block.settings.subtext }}
                </div>
              {% endif %}
              
              {%- form 'customer', id: 'ContactFooter', class: 'footer-block__newsletter-form' -%}
                <input type="hidden" name="contact[tags]" value="newsletter">
                <input
                  id="NewsletterForm--{{ section.id }}"
                  type="email"
                  name="contact[email]"
                  class="footer-block__newsletter-input"
                  value="{{ form.email }}"
                  aria-required="true"
                  autocomplete="email"
                  placeholder="{{ 'newsletter.label' | t }}"
                  required
                >
                <button
                  type="submit"
                  class="footer-block__newsletter-button"
                  name="commit"
                  aria-label="{{ 'newsletter.button_label' | t }}"
                >
                  {{ 'newsletter.button_label' | t }}
                </button>
              {%- endform -%}
            </div>
            
          {% when 'social' %}
            <div class="footer-block" {{ block.shopify_attributes }}>
              {% if block.settings.heading != blank %}
                <h2 class="footer-block__heading">{{ block.settings.heading }}</h2>
              {% endif %}
              
              {%- if settings.social_facebook_link != blank
                or settings.social_instagram_link != blank
                or settings.social_youtube_link != blank
                or settings.social_tiktok_link != blank
                or settings.social_twitter_link != blank
                or settings.social_pinterest_link != blank
                or settings.social_snapchat_link != blank
                or settings.social_tumblr_link != blank
                or settings.social_vimeo_link != blank
              -%}
                <div class="footer-block__social">
                  {% style %}
                    .footer-block__social ul.list-social {
                      gap: 1rem;
                    }

                    .footer-block__social a.list-social__link {
                      padding: 0;
                    }

                    .footer-block__social svg.icon {
                      height: 25px;
                      width: 25px;
                      color: var(--footer-text-color);
                    }
                  {% endstyle %}
                  {% render 'social-icons' %}
                </div>
              {%- endif -%}
            </div>
            
          {% when 'payment' %}
            <div class="footer-block" {{ block.shopify_attributes }}>
              {% if block.settings.heading != blank %}
                <h2 class="footer-block__heading">{{ block.settings.heading }}</h2>
              {% endif %}
              
              {%- unless shop.enabled_payment_types == empty -%}
                <div class="footer-block__payment">
                  {%- for type in shop.enabled_payment_types -%}
                    {{ type | payment_type_svg_tag: class: 'footer-block__payment-icon' }}
                  {%- endfor -%}
                </div>
              {%- endunless -%}
            </div>
            
        {% endcase %}
      {% endfor %}
    </div>
    
    <div class="footer-bottom">
      <p class="footer-bottom__copyright">
        {% style %}
          .footer-bottom__copyright a {
            color: var(--footer-text-color);
            text-transform: uppercase;
          }
        {% endstyle %}
        &copy; {{ 'now' | date: "%Y" }} {{ shop.name | link_to: routes.root_url }}
        {% if section.settings.show_policy_links %}
          <span class="footer-bottom__policy-links">
            {{ 'general.footer.policy_links_html' | t }}
          </span>
        {% endif %}
      </p>
      
      {% if section.settings.show_powered_by %}
        <a href="https://www.shopify.com" class="footer-bottom__shopify">
          Shopify
        </a>
      {% endif %}
    </div>
  </div>
</footer>

{% if section.blocks contains 'map' %}
<script>
  // This is just a placeholder for Google Maps integration
  // In a real implementation, you would use the Google Maps API
  document.addEventListener('DOMContentLoaded', function() {
    // Initialize Google Maps
    // This code would be expanded with actual Google Maps integration
  });
</script>
{% endif %}

{% schema %}
{
  "name": "Modern Footer",
  "settings": [
    {
      "type": "color",
      "id": "background_color",
      "label": "Background color",
      "default": "#1a1a1a"
    },
    {
      "type": "color",
      "id": "text_color",
      "label": "Text color",
      "default": "#ffffff"
    },
    {
      "type": "color",
      "id": "heading_color",
      "label": "Heading color",
      "default": "#ffffff"
    },
    {
      "type": "color",
      "id": "link_hover_color",
      "label": "Link hover color",
      "default": "#e6e6e6"
    },
    {
      "type": "color",
      "id": "border_color",
      "label": "Border color",
      "default": "#333333"
    },
    {
      "type": "color",
      "id": "form_background",
      "label": "Form background color",
      "default": "#ffffff"
    },
    {
      "type": "color",
      "id": "form_text_color",
      "label": "Form text color",
      "default": "#1a1a1a"
    },
    {
      "type": "color",
      "id": "button_background",
      "label": "Button background color",
      "default": "#007bff"
    },
    {
      "type": "color",
      "id": "button_text_color",
      "label": "Button text color",
      "default": "#ffffff"
    },
    {
      "type": "color",
      "id": "button_hover_background",
      "label": "Button hover background color",
      "default": "#0069d9"
    },
    {
      "type": "color",
      "id": "social_icon_color",
      "label": "Social icon color",
      "default": "#ffffff"
    },
    {
      "type": "color",
      "id": "social_icon_hover_color",
      "label": "Social icon hover color",
      "default": "#e6e6e6"
    },
    {
      "type": "checkbox",
      "id": "show_policy_links",
      "label": "Show policy links",
      "default": true
    },
    {
      "type": "checkbox",
      "id": "show_powered_by",
      "label": "Show 'Powered by Shopify'",
      "default": true
    }
  ],
  "blocks": [
    {
      "type": "menu",
      "name": "Menu",
      "settings": [
        {
          "type": "text",
          "id": "heading",
          "label": "Heading",
          "default": "Quick links"
        },
        {
          "type": "link_list",
          "id": "menu",
          "label": "Menu",
          "default": "footer"
        }
      ]
    },
    {
      "type": "text",
      "name": "Text",
      "settings": [
        {
          "type": "text",
          "id": "heading",
          "label": "Heading",
          "default": "About Us"
        },
        {
          "type": "richtext",
          "id": "text",
          "label": "Text",
          "default": "<p>Share information about your brand with your customers.</p>"
        }
      ]
    },
    {
      "type": "location",
      "name": "Location & Hours",
      "settings": [
        {
          "type": "text",
          "id": "heading",
          "label": "Heading",
          "default": "Our Location"
        },
        {
          "type": "richtext",
          "id": "address",
          "label": "Address",
          "default": "<p>123 Street Name<br>City, State 12345<br>Country</p>"
        },
        {
          "type": "checkbox",
          "id": "show_hours",
          "label": "Show working hours",
          "default": true
        },
        {
          "type": "text",
          "id": "hours_heading",
          "label": "Hours heading",
          "default": "Working Hours"
        },
        {
          "type": "text",
          "id": "monday_hours",
          "label": "Monday hours",
          "default": "9:00 AM - 5:00 PM"
        },
        {
          "type": "text",
          "id": "tuesday_hours",
          "label": "Tuesday hours",
          "default": "9:00 AM - 5:00 PM"
        },
        {
          "type": "text",
          "id": "wednesday_hours",
          "label": "Wednesday hours",
          "default": "9:00 AM - 5:00 PM"
        },
        {
          "type": "text",
          "id": "thursday_hours",
          "label": "Thursday hours",
          "default": "9:00 AM - 5:00 PM"
        },
        {
          "type": "text",
          "id": "friday_hours",
          "label": "Friday hours",
          "default": "9:00 AM - 5:00 PM"
        },
        {
          "type": "text",
          "id": "saturday_hours",
          "label": "Saturday hours",
          "default": "10:00 AM - 2:00 PM"
        },
        {
          "type": "text",
          "id": "sunday_hours",
          "label": "Sunday hours",
          "default": "Closed"
        }
      ]
    },
    {
      "type": "map",
      "name": "Map",
      "settings": [
        {
          "type": "text",
          "id": "heading",
          "label": "Heading",
          "default": "Showroom"
        },
        {
          "type": "text",
          "id": "map_url",
          "label": "Google Maps embed URL",
          "info": "Get this URL from Google Maps by clicking 'Share' and then 'Embed a map', then copy the src URL",
          "default": "https://www.google.com/maps/embed?pb=!1m18!1m12!1m3!1d2887.2810214308247!2d-79.38927548450627!3d43.64236067912161!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0x882b34d68bf33a9b%3A0x15edd8c4de1c7581!2sCN%20Tower!5e0!3m2!1sen!2sca!4v1633018568973!5m2!1sen!2sca"
        }
      ]
    },
    {
      "type": "newsletter",
      "name": "Newsletter",
      "limit": 1,
      "settings": [
        {
          "type": "text",
          "id": "heading",
          "label": "Heading",
          "default": "Subscribe to our newsletter"
        },
        {
          "type": "richtext",
          "id": "subtext",
          "label": "Subtext",
          "default": "<p>Sign up for exclusive updates, new arrivals & insider only discounts.</p>"
        }
      ]
    },
    {
      "type": "social",
      "name": "Social Media",
      "limit": 1,
      "settings": [
        {
          "type": "text",
          "id": "heading",
          "label": "Heading",
          "default": "Follow Us"
        }
      ]
    },
    {
      "type": "payment",
      "name": "Payment Methods",
      "limit": 1,
      "settings": [
        {
          "type": "text",
          "id": "heading",
          "label": "Heading",
          "default": "Payment Methods"
        }
      ]
    }
  ],
  "presets": [
    {
      "name": "Modern Footer",
      "blocks": [
        {
          "type": "menu"
        },
        {
          "type": "text"
        },
        {
          "type": "location"
        },
        {
          "type": "newsletter"
        },
        {
          "type": "social"
        },
        {
          "type": "payment"
        }
      ]
    }
  ]
}
{% endschema %}