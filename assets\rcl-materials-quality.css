.materials-quality-section {
  padding: 80px 0;
  position: relative;
  overflow: hidden;
}

/* Background gradient accent */
.materials-quality-section::before {
  content: '';
  position: absolute;
  width: 1200px;
  height: 1200px;
  border-radius: 50%;
  background: radial-gradient(circle, rgba(var(--color-foreground), 0.03) 0%, rgba(var(--color-foreground), 0) 70%);
  top: -400px;
  right: -400px;
  z-index: -1;
  opacity: 0.7;
}

.materials-quality-container {
  display: flex;
  flex-direction: column;
  gap: 100px;
  perspective: 1000px;
}

/* Staggered entry animations for each item */
.materials-quality-item {
  display: flex;
  align-items: center;
  gap: 60px;
  opacity: 0;
  transform: translateY(40px);
  transition: opacity 0.7s ease, transform 0.7s cubic-bezier(0.22, 1, 0.36, 1);
}

/* Use animate class when added via JS */
.materials-quality-item.animate {
  opacity: 1;
  transform: translateY(0);
}

/* Add proper animation when JS not available */
.no-js .materials-quality-item {
  opacity: 1;
  transform: translateY(0);
}

.materials-quality-item:nth-child(2) {
  transition-delay: 0.1s;
}

.materials-quality-item:nth-child(3) {
  transition-delay: 0.2s;
}

.materials-quality-item.reversed {
  flex-direction: row-reverse;
}

.materials-quality-image-container,
.materials-quality-content {
  flex: 1;
  min-width: 0;
}

/* Enhanced image styling with depth */
.materials-quality-image {
  position: relative;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 
    0 10px 30px rgba(0, 0, 0, 0.08),
    0 30px 60px rgba(0, 0, 0, 0.05);
  transition: transform 0.5s cubic-bezier(0.22, 1, 0.36, 1), 
              box-shadow 0.5s cubic-bezier(0.22, 1, 0.36, 1);
  will-change: transform, box-shadow;
}

.materials-quality-image:hover {
  transform: translateY(-10px);
  box-shadow: 
    0 15px 35px rgba(0, 0, 0, 0.1),
    0 40px 70px rgba(0, 0, 0, 0.07);
}

.materials-quality-image img,
.materials-quality-image .placeholder-svg {
  display: block;
  width: 100%;
  height: auto;
  transition: transform 0.7s cubic-bezier(0.22, 1, 0.36, 1);
  will-change: transform;
}

.materials-quality-image:hover img {
  transform: scale(1.05);
}

/* Height variations */
.materials-quality-image--small {
  height: 300px;
}

.materials-quality-image--small img {
  height: 100%;
  object-fit: cover;
}

.materials-quality-image--medium {
  height: 400px;
}

.materials-quality-image--medium img {
  height: 100%;
  object-fit: cover;
}

.materials-quality-image--large {
  height: 550px;
}

.materials-quality-image--large img {
  height: 100%;
  object-fit: cover;
}

/* Animated origin tag */
.materials-origin {
  position: absolute;
  bottom: 20px;
  right: 20px;
  padding: 8px 15px;
  border-radius: 6px;
  font-size: 0.9rem;
  font-weight: 600;
  color: #ffffff;
  text-transform: uppercase;
  letter-spacing: 0.08em;
  transform: translateY(0);
  transition: transform 0.4s cubic-bezier(0.22, 1, 0.36, 1), 
              box-shadow 0.4s cubic-bezier(0.22, 1, 0.36, 1);
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.15);
}

.materials-quality-image:hover .materials-origin {
  transform: translateY(-5px);
  box-shadow: 0 10px 20px rgba(0, 0, 0, 0.2);
}

.materials-quality-content-inner {
  max-width: 500px;
  padding: 20px;
  position: relative;
  opacity: 0;
  transform: translateX(20px);
  transition: opacity 0.7s ease, transform 0.7s cubic-bezier(0.22, 1, 0.36, 1);
  transition-delay: 0.2s;
}

.materials-quality-item.animate .materials-quality-content-inner {
  opacity: 1;
  transform: translateX(0);
}

.no-js .materials-quality-content-inner {
  opacity: 1;
  transform: translateX(0);
}

.materials-quality-item.reversed .materials-quality-content-inner {
  transform: translateX(-20px);
}

.materials-quality-item.reversed.animate .materials-quality-content-inner {
  transform: translateX(0);
}

[data-layout="standard"] .materials-quality-item:nth-child(odd) .materials-quality-content-inner {
  margin-left: auto;
}

[data-layout="standard"] .materials-quality-item:nth-child(even) .materials-quality-content-inner,
[data-layout="alternating"] .materials-quality-item.reversed .materials-quality-content-inner {
  margin-right: auto;
}

.materials-quality-title {
  margin: 0 0 15px;
  font-size: 2rem;
  font-weight: 600;
  position: relative;
  display: inline-block;
}

/* Animated accent line */
.materials-quality-detail {
  margin-bottom: 30px;
  position: relative;
}

.detail-accent {
  display: block;
  width: 60px;
  height: 3px;
  border-radius: 3px;
  position: relative;
  overflow: hidden;
}

.detail-accent::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: rgba(255, 255, 255, 0.3);
  transform: skew(-30deg);
  animation: shine 3s infinite;
}

@keyframes shine {
  0% { left: -100%; }
  20% { left: 100%; }
  100% { left: 100%; }
}

.materials-quality-text {
  font-size: 1.05rem;
  line-height: 1.7;
  margin-bottom: 35px;
}

.product-badge {
  display: inline-block;
  margin-bottom: 15px;
  font-weight: 600;
  font-size: 0.9rem;
  text-transform: uppercase;
  letter-spacing: 0.08em;
  padding: 6px 12px;
  border-radius: 4px;
  position: relative;
  overflow: hidden;
  background-color: rgba(var(--color-foreground), 0.05);
}

.product-badge::before {
  content: '';
  position: absolute;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  background: linear-gradient(to right, transparent, rgba(255, 255, 255, 0.3), transparent);
  transform: rotate(45deg);
  animation: badgeShine 3s infinite;
}

@keyframes badgeShine {
  0% { transform: translateX(-100%) rotate(45deg); }
  20%, 100% { transform: translateX(100%) rotate(45deg); }
}

.product-image {
  position: relative;
}

.product-image:after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(to bottom, rgba(0,0,0,0) 65%, rgba(0,0,0,0.25) 100%);
  z-index: 1;
  transition: opacity 0.4s ease;
}

.product-image:hover:after {
  opacity: 0.7;
}

/* Enhanced button styling */
.materials-quality-button {
  display: inline-block;
  padding: 0.9rem 2rem;
  letter-spacing: 0.08em;
  text-decoration: none;
  border-radius: 6px;
  position: relative;
  overflow: hidden;
  z-index: 1;
  transition: transform 0.3s cubic-bezier(0.22, 1, 0.36, 1), 
              box-shadow 0.3s cubic-bezier(0.22, 1, 0.36, 1);
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
}

.materials-quality-button::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(255, 255, 255, 0.1);
  transform: translateY(-100%);
  transition: transform 0.5s cubic-bezier(0.22, 1, 0.36, 1);
  z-index: -1;
}

.materials-quality-button:hover {
  transform: translateY(-5px);
  box-shadow: 0 10px 20px rgba(0, 0, 0, 0.15);
}

.materials-quality-button:hover::before {
  transform: translateY(0);
}

.materials-quality-button:focus {
  outline: none;
  box-shadow: 0 0 0 3px rgba(var(--color-foreground), 0.2);
}

/* Responsive */
@media screen and (max-width: 989px) {
  .materials-quality-item,
  .materials-quality-item.reversed {
    flex-direction: column;
    gap: 50px;
  }
  
  .materials-quality-content-inner {
    max-width: 100%;
    padding: 0;
    margin: 0 !important;
    transform: translateY(20px) !important;
  }
  
  .materials-quality-item.animate .materials-quality-content-inner {
    transform: translateY(0) !important;
  }
}

@media screen and (max-width: 749px) {
  .materials-quality-section {
    padding: 60px 0;
  }
  
  .materials-quality-container {
    gap: 60px;
  }
  
  .materials-quality-title {
    font-size: 1.5rem;
  }
  
  .materials-quality-text {
    font-size: 1rem;
  }
  
  .section-title::after {
    bottom: -3px;
  }
  
  .materials-quality-image:hover {
    transform: translateY(-5px);
  }
}