{% style %}
  .dual-cards-{{ section.id }} {
    position: relative;
    width: 100%;
    display: flex;
    flex-direction: column;
    overflow: hidden;
    z-index: 0;
  }
  
  @media screen and (min-width: 990px) {
    .dual-cards-{{ section.id }} {
      flex-direction: row;
    }
  }
  
  .dual-cards-{{ section.id }} .card__content {
    display: flex;
    flex-grow: 1;
    flex-direction: column;
    justify-content: center;
  }
  
  .dual-cards-{{ section.id }} .card {
    position: relative;
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: center;
    overflow: hidden;
    transition: all 0.6s cubic-bezier(0.25, 0.46, 0.45, 0.94);
    min-height: 50vh;
  }
  
  @media screen and (min-width: 990px) {
    .dual-cards-{{ section.id }} .card {
      min-height: 100vh;
    }
  }
  
  .dual-cards-{{ section.id }} .card:hover {
    flex: 1.1;
  }
  
  .dual-cards-{{ section.id }} .card__background {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-size: cover;
    background-position: center;
    background-repeat: no-repeat;
    transition: transform 8s cubic-bezier(0.215, 0.61, 0.355, 1);
    z-index: 1;
  }
  
  .dual-cards-{{ section.id }} .card:hover .card__background {
    transform: scale(1.05);
  }
  
  .dual-cards-{{ section.id }} .card__background--parallax {
    background-attachment: fixed;
  }
  
  .dual-cards-{{ section.id }} .card__overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 2;
    transition: opacity 0.3s ease;
  }
  
  .dual-cards-{{ section.id }} .card:hover .card__overlay {
    opacity: var(--hover-opacity) !important;
  }
  
  .dual-cards-{{ section.id }} .card__content {
    position: relative;
    z-index: 3;
    padding: 2.5rem 2.5rem;
    max-width: 650px;
    width: 90%;
    text-align: center;
    transform: translateY(0);
    transition: transform 0.5s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  }
  
  .dual-cards-{{ section.id }} .card:hover .card__content {
    transform: translateY(-10px);
  }
  
  .dual-cards-{{ section.id }} .card__heading {
    margin: 0 0 1.2rem;
    font-weight: 700;
    position: relative;
    display: inline-block;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
  }
  
  .dual-cards-{{ section.id }} .card__heading::after {
    content: "";
    position: absolute;
    bottom: -0.5rem;
    left: 50%;
    width: 80px;
    height: 3px;
    transform: translateX(-50%) scaleX(1);
    background-color: currentColor;
    transition: width 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
    opacity: 0.8;
  }
  
  .dual-cards-{{ section.id }} .card:hover .card__heading::after {
    width: 150px;
  }
  
  .dual-cards-{{ section.id }} .text-left .card__heading::after {
    left: 25%;
  }
  
  .dual-cards-{{ section.id }} .text-right .card__heading::after {
    left: 75%;
  }
  
  .dual-cards-{{ section.id }} .card__subheading {
    margin: 0 0 1.4rem;
    font-weight: 500;
    opacity: 0.95;
    text-shadow: 0 1px 3px rgba(0, 0, 0, 0.2);
    letter-spacing: 0.04em;
  }
  
  .dual-cards-{{ section.id }} .card__text {
    margin: 0;
    opacity: 0.9;
    line-height: 1.7;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.15);
    max-width: 85%;
    margin-left: auto;
    margin-right: auto;
  }
  
  .dual-cards-{{ section.id }} .card__link {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 4;
    cursor: pointer;
  }
  
  .dual-cards-{{ section.id }} .card__link-indicator {
    position: absolute;
    top: 60%;
    left: 50%;
    transform: translate(-50%, -50%) scale(0.8);
    width: 85px;
    height: 85px;
    border-radius: 50%;
    background-color: rgba(255, 255, 255, 0.15);
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
    z-index: 5;
    pointer-events: none;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.15);
    border: 1px solid rgba(255, 255, 255, 0.2);
  }
  
  .dual-cards-{{ section.id }} .card:hover .card__link-indicator {
    opacity: 1;
    transform: translate(-50%, -50%) scale(1);
  }
  
  .dual-cards-{{ section.id }} .card__link-indicator svg {
    width: 32px;
    height: 32px;
    fill: none;
    stroke: #fff;
    stroke-width: 2;
    stroke-linecap: round;
    stroke-linejoin: round;
    filter: drop-shadow(0 1px 2px rgba(0, 0, 0, 0.3));
  }
  
  /* Media Queries */
  @media screen and (max-width: 989px) {
    .dual-cards-{{ section.id }} .card__heading::after {
      transform: translateX(-50%) scaleX(1);
      width: 60px;
    }
    
    .dual-cards-{{ section.id }} .card:hover .card__heading::after {
      width: 100px;
    }
    
    .dual-cards-{{ section.id }} .card:hover .card__content {
      transform: translateY(-5px);
    }
    
    .dual-cards-{{ section.id }} .card:hover {
      flex: 1;
    }
    
    .dual-cards-{{ section.id }} .card__background--parallax {
      background-attachment: scroll;
    }
    
    .dual-cards-{{ section.id }} .card__content {
      padding: 2rem 1.5rem;
      width: 95%;
    }
    
    .dual-cards-{{ section.id }} .card__text {
      max-width: 95%;
    }
  }
  
  @media screen and (max-width: 749px) {
    .dual-cards {
      flex-direction: column-reverse;
    }
    
    .dual-cards-{{ section.id }} .card {
      min-height: 45vh;
    }
    
    .dual-cards-{{ section.id }} .card__heading {
      margin-bottom: 1rem;
    }
    
    .dual-cards-{{ section.id }} .card__subheading {
      margin-bottom: 1.1rem;
    }
    
    .dual-cards-{{ section.id }} .card__link-indicator {
      width: 70px;
      height: 70px;
    }
    
    .dual-cards-{{ section.id }} .card__link-indicator svg {
      width: 28px;
      height: 28px;
    }
  }
  
  @media screen and (max-width: 480px) {
    .dual-cards-{{ section.id }} .card {
      min-height: 35vh;
      max-height: 40vh;
    }
    
    .dual-cards-{{ section.id }} .card__content {
      padding: 1.5rem 1rem;
    }

    .dual-cards-{{ section.id }} .card__link {
      top: 28%; 
    }
    
    .dual-cards-{{ section.id }} .card__link-indicator {
      width: 40px;
      height: 40px;
    }
    
    .dual-cards-{{ section.id }} .card__link-indicator svg {
      width: 24px;
      height: 24px;
    }
  }
{% endstyle %}
  
<div class="dual-cards dual-cards-{{ section.id }}" id="dual-cards-section-{{ section.id }}">
  <div class="card" style="background-color: {{ section.settings.card1_bg_color }}">
    {% if section.settings.card1_bg_image != blank %}
      <div class="card__background {% if section.settings.card1_parallax %}card__background--parallax{% endif %}" 
            style="background-image: url('{{ section.settings.card1_bg_image | image_url: width: 1800 }}');">
      </div>
    {% endif %}
    
    <div class="card__overlay" 
          style="background-color: {{ section.settings.card1_overlay_color }}; 
                opacity: {{ section.settings.card1_overlay_opacity | divided_by: 100.0 }}; 
                --hover-opacity: {{ section.settings.card1_overlay_hover_opacity | divided_by: 100.0 }};">
    </div>
    
    <div class="card__content">
      <h2 class="card__heading" style="color: {{ section.settings.card1_heading_color }}; font-size: {{ section.settings.card1_heading_size }}px;">{{ section.settings.card1_heading | escape }}</h2>
      <h3 class="card__subheading" style="color: {{ section.settings.card1_subheading_color }}; font-size: {{ section.settings.card1_subheading_size }}px;">{{ section.settings.card1_subheading | escape }}</h3>
      <p class="card__text" style="color: {{ section.settings.card1_text_color }}; font-size: {{ section.settings.card1_text_size }}px;">{{ section.settings.card1_text }}</p>
    </div>
    
    {% if section.settings.card1_link != blank %}
      <a href="{{ section.settings.card1_link }}" class="card__link" aria-label="{{ section.settings.card1_heading | escape }}">
        <div class="card__link-indicator">
          <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24">
            <path d="M5 12h14M12 5l7 7-7 7"></path>
          </svg>
        </div>
      </a>
    {% endif %}
  </div>
  
  <div class="card" style="background-color: {{ section.settings.card2_bg_color }}">
    {% if section.settings.card2_bg_image != blank %}
      <div class="card__background {% if section.settings.card2_parallax %}card__background--parallax{% endif %}" 
            style="background-image: url('{{ section.settings.card2_bg_image | image_url: width: 1800 }}');">
      </div>
    {% endif %}
    
    <div class="card__overlay" 
          style="background-color: {{ section.settings.card2_overlay_color }}; 
                opacity: {{ section.settings.card2_overlay_opacity | divided_by: 100.0 }}; 
                --hover-opacity: {{ section.settings.card2_overlay_hover_opacity | divided_by: 100.0 }};">
    </div>
    
    <div class="card__content">
      <h2 class="card__heading" style="color: {{ section.settings.card2_heading_color }}; font-size: {{ section.settings.card2_heading_size }}px;">{{ section.settings.card2_heading | escape }}</h2>
      <h3 class="card__subheading" style="color: {{ section.settings.card2_subheading_color }}; font-size: {{ section.settings.card2_subheading_size }}px;">{{ section.settings.card2_subheading | escape }}</h3>
      <p class="card__text" style="color: {{ section.settings.card2_text_color }}; font-size: {{ section.settings.card2_text_size }}px;">{{ section.settings.card2_text }}</p>
    </div>
    
    {% if section.settings.card2_link != blank %}
      <a href="{{ section.settings.card2_link }}" class="card__link" aria-label="{{ section.settings.card2_heading | escape }}">
        <div class="card__link-indicator">
          <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24">
            <path d="M5 12h14M12 5l7 7-7 7"></path>
          </svg>
        </div>
      </a>
    {% endif %}
  </div>
</div>
  
{% if section.settings.enable_custom_parallax %}
  <script>
    document.addEventListener('DOMContentLoaded', function() {
      const sectionId = 'dual-cards-section-{{ section.id }}';
      const parallaxElements = document.querySelectorAll('#' + sectionId + ' .card__background--parallax');
      
      if (parallaxElements.length > 0) {
        function isInViewport(element) {
          const rect = element.getBoundingClientRect();
          return (
            rect.top <= (window.innerHeight || document.documentElement.clientHeight) &&
            rect.bottom >= 0
          );
        }
        
        function updateParallax() {
          parallaxElements.forEach(function(element) {
            if (isInViewport(element)) {
              const scrollPosition = window.pageYOffset;
              const elementTop = element.getBoundingClientRect().top + scrollPosition;
              const offset = (scrollPosition - elementTop) * 0.4;
              element.style.backgroundPositionY = `calc(50% + ${offset}px)`;
            }
          });
        }
        
        window.addEventListener('scroll', updateParallax);
        window.addEventListener('resize', updateParallax);
        updateParallax();
      }
    });
  </script>
{% endif %}
  
{% schema %}
  {
    "name": "Dual Content Cards",
    "tag": "section",
    "class": "section",
    "settings": [
      {
        "type": "header",
        "content": "General Settings"
      },
      {
        "type": "checkbox",
        "id": "enable_custom_parallax",
        "label": "Enable Custom Parallax Effect",
        "default": false,
        "info": "Uses JavaScript for enhanced parallax scrolling (recommended for complex layouts)"
      },
      {
        "type": "header",
        "content": "Card 1 Settings"
      },
      {
        "type": "image_picker",
        "id": "card1_bg_image",
        "label": "Background Image"
      },
      {
        "type": "checkbox",
        "id": "card1_parallax",
        "label": "Enable Parallax on this Card",
        "default": false,
        "info": "Apply parallax effect to this card's background"
      },
      {
        "type": "color",
        "id": "card1_bg_color",
        "label": "Background Color",
        "default": "#1a1a1a"
      },
      {
        "type": "color",
        "id": "card1_overlay_color",
        "label": "Overlay Color",
        "default": "#000000"
      },
      {
        "type": "range",
        "id": "card1_overlay_opacity",
        "min": 0,
        "max": 95,
        "step": 5,
        "unit": "%",
        "label": "Overlay Opacity",
        "default": 50
      },
      {
        "type": "range",
        "id": "card1_overlay_hover_opacity",
        "min": 0,
        "max": 95,
        "step": 5,
        "unit": "%",
        "label": "Overlay Hover Opacity",
        "default": 65
      },
      {
        "type": "text",
        "id": "card1_heading",
        "label": "Heading",
        "default": "DIY Users"
      },
      {
        "type": "range",
        "id": "card1_heading_size",
        "min": 24,
        "max": 72,
        "step": 2,
        "unit": "px",
        "label": "Heading Size",
        "default": 48
      },
      {
        "type": "color",
        "id": "card1_heading_color",
        "label": "Heading Color",
        "default": "#ffffff"
      },
      {
        "type": "text",
        "id": "card1_subheading",
        "label": "Subheading",
        "default": "Card 1 Subheading Text"
      },
      {
        "type": "range",
        "id": "card1_subheading_size",
        "min": 16,
        "max": 48,
        "step": 1,
        "unit": "px",
        "label": "Subheading Size",
        "default": 24
      },
      {
        "type": "color",
        "id": "card1_subheading_color",
        "label": "Subheading Color",
        "default": "#ffffff"
      },
      {
        "type": "textarea",
        "id": "card1_text",
        "label": "Text Content",
        "default": "Add your descriptive text here for the first card. This can be used to explain what the user can expect when clicking on this card."
      },
      {
        "type": "range",
        "id": "card1_text_size",
        "min": 12,
        "max": 28,
        "step": 1,
        "unit": "px",
        "label": "Text Size",
        "default": 16
      },
      {
        "type": "color",
        "id": "card1_text_color",
        "label": "Text Color",
        "default": "#ffffff"
      },
      {
        "type": "url",
        "id": "card1_link",
        "label": "Card Link",
        "info": "Where the card should link to when clicked"
      },
      {
        "type": "header",
        "content": "Card 2 Settings"
      },
      {
        "type": "image_picker",
        "id": "card2_bg_image",
        "label": "Background Image"
      },
      {
        "type": "checkbox",
        "id": "card2_parallax",
        "label": "Enable Parallax on this Card",
        "default": false,
        "info": "Apply parallax effect to this card's background"
      },
      {
        "type": "color",
        "id": "card2_bg_color",
        "label": "Background Color",
        "default": "#2a2a2a"
      },
      {
        "type": "color",
        "id": "card2_overlay_color",
        "label": "Overlay Color",
        "default": "#000000"
      },
      {
        "type": "range",
        "id": "card2_overlay_opacity",
        "min": 0,
        "max": 95,
        "step": 5,
        "unit": "%",
        "label": "Overlay Opacity",
        "default": 50
      },
      {
        "type": "range",
        "id": "card2_overlay_hover_opacity",
        "min": 0,
        "max": 95,
        "step": 5,
        "unit": "%",
        "label": "Overlay Hover Opacity",
        "default": 65
      },
      {
        "type": "text",
        "id": "card2_heading",
        "label": "Heading",
        "default": "Professional Users"
      },
      {
        "type": "range",
        "id": "card2_heading_size",
        "min": 24,
        "max": 72,
        "step": 2,
        "unit": "px",
        "label": "Heading Size",
        "default": 48
      },
      {
        "type": "color",
        "id": "card2_heading_color",
        "label": "Heading Color",
        "default": "#ffffff"
      },
      {
        "type": "text",
        "id": "card2_subheading",
        "label": "Subheading",
        "default": "Card 2 Subheading Text"
      },
      {
        "type": "range",
        "id": "card2_subheading_size",
        "min": 16,
        "max": 48,
        "step": 1,
        "unit": "px",
        "label": "Subheading Size",
        "default": 24
      },
      {
        "type": "color",
        "id": "card2_subheading_color",
        "label": "Subheading Color",
        "default": "#ffffff"
      },
      {
        "type": "textarea",
        "id": "card2_text",
        "label": "Text Content",
        "default": "Add your descriptive text here for the second card. This can be used to explain what the user can expect when clicking on this card."
      },
      {
        "type": "range",
        "id": "card2_text_size",
        "min": 12,
        "max": 28,
        "step": 1,
        "unit": "px",
        "label": "Text Size",
        "default": 16
      },
      {
        "type": "color",
        "id": "card2_text_color",
        "label": "Text Color",
        "default": "#ffffff"
      },
      {
        "type": "url",
        "id": "card2_link",
        "label": "Card Link",
        "info": "Where the card should link to when clicked"
      }
    ],
    "presets": [
      {
        "name": "Dual Content Cards",
        "settings": {
          "card1_heading": "DIY Users",
          "card2_heading": "Professional Users",
          "card1_bg_color": "#1a1a1a",
          "card2_bg_color": "#2a2a2a"
        }
      }
    ]
  }
{% endschema %}