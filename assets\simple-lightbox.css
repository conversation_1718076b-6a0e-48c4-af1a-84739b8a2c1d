.simple-lightbox {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.9);
  z-index: 9999;
  display: none;
  align-items: center;
  justify-content: center;
}

.lightbox-container {
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
}

.lightbox-img {
  max-width: 95%;
  max-height: 95%;
  object-fit: cover;
  border: 2px solid white;
  box-shadow: 0 0 20px rgba(0, 0, 0, 0.5);
}

@media screen and (max-width: 749px) {
  .lightbox-img {
    min-width: 85vw;
    min-height: 50vh;
  }
}

.lightbox-close {
  position: absolute;
  top: 20px;
  right: 20px;
  font-size: 30px;
  color: white;
  background: transparent;
  border: none;
  cursor: pointer;
  z-index: 10000;
}

.lightbox-nav {
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  background: black;
  border: none;
  cursor: pointer;
  padding: 10px;
  z-index: 10000;
}

.lightbox-prev {
  left: 20px;
}

.lightbox-next {
  right: 20px;
}

.lightbox-counter {
  position: absolute;
  bottom: 20px;
  color: white;
  font-size: 16px;
  font-weight: bold;
}
