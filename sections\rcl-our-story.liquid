{{ 'rcl-our-story.css' | asset_url | stylesheet_tag: preload: true }}

<section class="our-story" 
          id="our-story-{{ section.id }}"
          style="padding-top: {{ section.settings.padding_top }}px; 
                padding-bottom: {{ section.settings.padding_bottom }}px;
                background-color: {{ section.settings.background_color }};
                background-image: url({{ section.settings.background_image | img_url: 'master' }});
                background-size: auto;
                background-repeat: repeat;
                background-blend-mode: {{ section.settings.background_blend_mode }};
                transform: rotate({{ section.settings.background_image_rotation }}deg);">
  <div class="page-width">
    <div class="our-story__container {% if section.settings.layout == 'image_right' %}image-right{% else %}image-left{% endif %}">
      
      <div class="our-story__image-container">
        {% if section.settings.image != blank %}
          <img class="our-story__image" 
               src="{{ section.settings.image | img_url: 'master' }}" 
               alt="Our Story"
               loading="lazy"
               width="{{ section.settings.image.width }}"
               height="{{ section.settings.image.height }}">
          
          {% for block in section.blocks %}
            {% if block.type == 'year_badge' and block.settings.year != blank %}
              <div class="our-story__year-badge" style="background-color: {{ section.settings.accent_color }};">
                {{ block.settings.year }}
              </div>
            {% endif %}
          {% endfor %}
        {% else %}
          <div class="our-story__placeholder" style="background: linear-gradient(135deg, {{ section.settings.background_color }} 0%, #ffffff 100%);">
            {{ 'image' | placeholder_svg_tag: 'placeholder-svg' }}
            
            {% for block in section.blocks %}
              {% if block.type == 'year_badge' and block.settings.year != blank %}
                <div class="our-story__year-badge" style="background-color: {{ section.settings.accent_color }};">
                  {{ block.settings.year }}
                </div>
              {% endif %}
            {% endfor %}
          </div>
        {% endif %}
      </div>
      
      <div class="our-story__text-container">
        <div class="our-story__text-inner">
          {% for block in section.blocks %}
            {% case block.type %}
              {% when 'heading' %}
                {% style %}
                  #our-story-{{ section.id }} .our-story__heading {
                    background: linear-gradient(135deg, {{ block.settings.heading_color }} 0%, #444444 100%);
                    -webkit-background-clip: text;
                    background-clip: text;
                    color: transparent !important;
                    animation: textShimmer 8s infinite alternate;
                  }
                {% endstyle %}
                <h2 class="our-story__heading" style="color: {{ block.settings.heading_color }};">
                  <span class="our-story__heading-line" style="background-color: {{ section.settings.accent_color }};"></span>
                  {{ block.settings.heading }}
                </h2>
              {% when 'text' %}
                <div class="our-story__text" style="color: {{ block.settings.text_color }};">
                  {{ block.settings.text }}
                </div>
            {% endcase %}
          {% endfor %}
        </div>
      </div>
      
    </div>
  </div>
</section>

<script>
  document.addEventListener('DOMContentLoaded', function() {
    const observer = new IntersectionObserver((entries) => {
      entries.forEach(entry => {
        if (entry.isIntersecting) {
          entry.target.classList.add('animate-in');
          observer.unobserve(entry.target);
        }
      });
    }, {
      threshold: 0.2
    });
    
    const storySection = document.getElementById('our-story-{{ section.id }}');
    if (storySection) {
      const container = storySection.querySelector('.our-story__container');
      if (container) {
        observer.observe(container);
      }
    }
  });
</script>

{% schema %}
  {
    "name": "Our Story",
    "tag": "section",
    "class": "section our-story-section",
    "settings": [
      {
        "type": "select",
        "id": "layout",
        "options": [
          {
            "value": "image_left",
            "label": "Image on left"
          },
          {
            "value": "image_right",
            "label": "Image on right"
          }
        ],
        "default": "image_left",
        "label": "Layout"
      },
      {
        "type": "image_picker",
        "id": "image",
        "label": "Image"
      },
      {
        "type": "range",
        "id": "padding_top",
        "min": 0,
        "max": 100,
        "step": 10,
        "unit": "px",
        "label": "Top padding",
        "default": 60
      },
      {
        "type": "range",
        "id": "padding_bottom",
        "min": 0,
        "max": 100,
        "step": 10,
        "unit": "px",
        "label": "Bottom padding",
        "default": 60
      },
      {
        "type": "color",
        "id": "background_color",
        "label": "Background Color",
        "default": "#ffffff"
      },
      {
        "type": "color",
        "id": "accent_color",
        "label": "Accent Color",
        "default": "#d1b073"
      },
      {
        "type": "checkbox",
        "id": "use_background_image",
        "label": "Use Background Image",
        "default": false
      },
      {
        "type": "image_picker",
        "id": "background_image",
        "label": "Background Image"
      },
      {
        "type": "select",
        "id": "background_blend_mode",
        "label": "Background Blend Mode",
        "options": [
          {
            "value": "normal",
            "label": "Normal"
          },
          {
            "value": "color-burn",
            "label": "Color Burn"
          },
          {
            "value": "multiply",
            "label": "Multiply"
          },
          {
            "value": "screen",
            "label": "Screen"
          },
          {
            "value": "overlay",
            "label": "Overlay"
          },
          {
            "value": "darken",
            "label": "Darken"
          },
          {
            "value": "lighten",
            "label": "Lighten"
          }
        ],
        "default": "normal"
      },
      {
        "type": "range",
        "id": "background_image_rotation",
        "label": "Background Image Rotation",
        "min": 0,
        "max": 100,
        "step": 1,
        "unit": "deg",
        "default": 0
      }
    ],
    "blocks": [
      {
        "type": "heading",
        "name": "Heading",
        "limit": 1,
        "settings": [
          {
            "type": "text",
            "id": "heading",
            "label": "Heading",
            "default": "Our Story"
          },
          {
            "type": "color",
            "id": "heading_color",
            "label": "Heading Color",
            "default": "#222228"
          }
        ]
      },
      {
        "type": "text",
        "name": "Text Content",
        "settings": [
          {
            "type": "richtext",
            "id": "text",
            "label": "Text",
            "default": "<p>Elf Decor™ was created in 2006. The idea of its creation came after five years of experience in the production of building materials of Elf™, as well as the successful work of our stores, representing decorative coatings of well-known European manufacturers.</p>"
          },
          {
            "type": "color",
            "id": "text_color",
            "label": "Text Color",
            "default": "#222228"
          }
        ]
      },
      {
        "type": "year_badge",
        "name": "Year Badge",
        "limit": 1,
        "settings": [
          {
            "type": "text",
            "id": "year",
            "label": "Year",
            "default": "Since 2006"
          }
        ]
      }
    ],
    "presets": [
      {
        "name": "Our Story",
        "blocks": [
          {
            "type": "heading"
          },
          {
            "type": "text"
          },
          {
            "type": "year_badge"
          }
        ]
      }
    ]
  }
{% endschema %}
