/*
 * ------------------------------------------------------------
 * IMPORTANT: The contents of this file are auto-generated.
 *
 * This file may be updated by the Shopify admin theme editor
 * or related systems. Please exercise caution as any changes
 * made to this file may be overwritten.
 * ------------------------------------------------------------
 */
{
  "sections": {
    "rcl_hero_section_kcUw8j": {
      "type": "rcl-hero-section",
      "blocks": {
        "heading_Vgbbhq": {
          "type": "heading",
          "settings": {
            "heading": "{{ page.title }} Elf Decor",
            "heading_size": "h1",
            "heading_color": "#d1b073"
          }
        },
        "subheading_Fdq94B": {
          "type": "subheading",
          "disabled": true,
          "settings": {
            "subheading": "",
            "subheading_color": "#d1b073"
          }
        },
        "text_68RGRn": {
          "type": "text",
          "disabled": true,
          "settings": {
            "description": "",
            "description_color": "#ffffff"
          }
        }
      },
      "block_order": [
        "heading_Vgbbhq",
        "subheading_Fdq94B",
        "text_68RGRn"
      ],
      "settings": {
        "hero_image": "shopify://shop_images/PNG_image_5.png",
        "text_alignment": "text-center",
        "desktop_height": 300,
        "mobile_height": 250,
        "overlay_color": "#222228",
        "overlay_opacity": 70,
        "parallax_effect": true,
        "enable_image_filter": false,
        "filter_opacity": 50
      }
    },
    "main": {
      "type": "main-page",
      "disabled": true,
      "settings": {
        "padding_top": 36,
        "padding_bottom": 36
      }
    },
    "rcl_contact_us_ffntAF": {
      "type": "rcl-contact-us",
      "blocks": {
        "contact_email_P4zCP6": {
          "type": "contact_email",
          "settings": {
            "title": "Email Us",
            "email": "<EMAIL>"
          }
        },
        "follow_us_YPkmnY": {
          "type": "follow_us",
          "settings": {
            "title": "Follow Us"
          }
        }
      },
      "block_order": [
        "contact_email_P4zCP6",
        "follow_us_YPkmnY"
      ],
      "settings": {
        "title": "Reach out to us",
        "subtitle": "<p>We're here to help with any questions about our premium products and services. Get in touch with our team today.</p>",
        "heading_color": "#222228",
        "text_color": "#222228",
        "accent_color": "#d1b073",
        "background_color": "#f3f3f3",
        "padding_top": "small",
        "padding_bottom": "medium"
      }
    }
  },
  "order": [
    "rcl_hero_section_kcUw8j",
    "main",
    "rcl_contact_us_ffntAF"
  ]
}
