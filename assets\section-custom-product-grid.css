/* Custom Product Grid Styles */
.custom-product-grid {
  padding-top: var(--section-padding-top);
  padding-bottom: var(--section-padding-bottom);
  background-color: var(--section-background);
  position: relative;
}

.custom-product-grid__container {
  margin: 0;
  padding: 0;
}

.custom-product-grid__list {
  display: grid;
  grid-template-columns: repeat(var(--columns-mobile, 2), 1fr);
  gap: var(--product-spacing-mobile, 15px);
  list-style: none;
  padding: 0;
  margin: 0;
}

@media screen and (min-width: 750px) {
  .custom-product-grid__list {
    grid-template-columns: repeat(var(--columns-desktop, 4), 1fr);
    gap: var(--product-spacing-desktop, 25px);
  }
}

/* Product Card Styles */
.custom-product-card {
  position: relative;
  background: #ffffff;
  overflow: hidden;
  margin-bottom: 1.5rem;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(var(--color-foreground), 0.08);
  display: flex;
  flex-direction: column;
  height: 100%;
}

.custom-product-card__media {
  position: relative;
  overflow: hidden;
  width: 100%;
  border-radius: 8px 8px 0 0;
  height: 300px; /* Fixed height for all product images */
}

.custom-product-card__media-wrap {
  position: relative;
  overflow: hidden;
  width: 100%;
  height: 100%;
}

.custom-product-card__media img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  display: block;
  transition: transform 0.5s ease;
}

.custom-product-card:hover .custom-product-card__media img {
  transform: scale(1.05);
}

.custom-product-card__media .secondary-media {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  opacity: 0;
  transition: opacity 0.3s ease;
  object-fit: cover;
}

.custom-product-card:hover .secondary-media {
  opacity: 1;
}

.custom-product-card__info {
  padding: 1.5rem;
  display: flex;
  flex-direction: column;
  flex-grow: 1;
  position: relative;
}

.custom-product-card__product-name {
  color: var(--product-primary);
  margin: 0;
  line-height: 1.3;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.03em;
  margin-bottom: 0.75rem;
}

.custom-product-card__decorative-line {
  width: 3rem;
  height: 3px;
  background-color: #d1b073;
  margin: 0.75rem 0;
  display: block;
}

.custom-product-card__description-container {
  display: flex;
  align-items: flex-start;
  margin-top: 0.5rem;
}

.custom-product-card__long-line {
  flex: 0 0 3rem; /* Make the line longer */
  height: 3px;
  background-color: #d1b073;
  margin-top: 0.7rem;
  margin-right: 0.75rem;
}

.custom-product-card__product-description {
  color: var(--product-primary);
  flex: 1;
  line-height: 1.4;
  overflow: hidden;
  display: -webkit-box;
  -webkit-line-clamp: 3;
  line-clamp: 3;
  -webkit-box-orient: vertical;
}

.custom-product-card__arrow-button {
  position: absolute;
  right: 1.5rem;
  top: 50%;
  transform: translateY(-50%);
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 3.5rem;
  height: 3.5rem;
  background-color: #d1b073;
  transition: opacity 0.3s ease;
  opacity: 0;
  z-index: 2;
  text-decoration: none;
}

.custom-product-card:hover .custom-product-card__arrow-button {
  opacity: 1;
}

.custom-product-card__arrow-button svg {
  width: 2.25rem;
  height: 2.25rem;
  color: #fff;
}

.custom-product-card__price {
  font-weight: 600;
  margin-top: 1rem;
  color: var(--product-primary);
}

.custom-product-card__link {
  display: flex;
  flex-direction: column;
  height: 100%;
  width: 100%;
  text-decoration: none;
  color: inherit;
  position: relative;
  z-index: 1;
}

/* Equal height for product cards */
.custom-product-grid__list {
  display: grid;
  grid-auto-rows: 1fr; /* This ensures equal height rows */
}

.custom-product-card {
  height: 100%; /* Make each card fill the grid cell */
  display: flex;
  flex-direction: column;
}

.custom-product-card__media {
  flex: 0 0 300px; /* Fixed height for media to ensure consistency */
}

.custom-product-card__info {
  flex: 1 1 auto; /* Allow the info to grow and fill available space */
  display: flex;
  flex-direction: column;
}

/* Mobile Styles */
@media screen and (max-width: 749px) {
  .custom-product-card__media {
    height: 200px; /* Smaller fixed height on mobile */
  }

  .custom-product-card__media {
    flex: 0 0 200px; /* Smaller fixed height on mobile */
  }

  .custom-product-card__arrow-button {
    width: 3rem;
    height: 3rem;
    right: 1rem;
  }

  .custom-product-card__arrow-button svg {
    width: 1rem;
    height: 1rem;
  }

  .custom-product-card__info {
    padding: 1rem;
  }

  .custom-product-card__long-line {
    flex: 0 0 2rem;
  }
}

/* Pagination Styles */
.custom-product-grid__pagination {
  margin-top: 2rem;
  text-align: center;
}

/* Filter and Sort Styles */
.custom-product-grid__filters {
  margin-bottom: 2rem;
}

.custom-product-grid__filter-button {
  display: flex;
  align-items: center;
  background: transparent;
  border: 1px solid #ddd;
  padding: 0.5rem 1rem;
  cursor: pointer;
  margin-right: 1rem;
}

.custom-product-grid__filter-button svg {
  margin-left: 0.5rem;
}

.custom-product-grid__sorting {
  display: flex;
  align-items: center;
}

.custom-product-grid__sorting select {
  border: 1px solid #ddd;
  padding: 0.5rem;
}

/* Horizontal Filters */
.custom-product-grid__filter-horizontal {
  display: flex;
  flex-wrap: wrap;
  gap: 1rem;
  margin-bottom: 2rem;
}

/* Vertical Filters */
.custom-product-grid__filter-vertical {
  width: 250px;
  padding-right: 2rem;
}

.custom-product-grid__with-sidebar {
  display: flex;
}

.custom-product-grid__main {
  flex: 1;
}

@media screen and (max-width: 990px) {
  .custom-product-grid__with-sidebar {
    flex-direction: column;
  }

  .custom-product-grid__filter-vertical {
    width: 100%;
    padding-right: 0;
    margin-bottom: 2rem;
  }
}

/* Empty Collection */
.custom-product-grid__empty {
  text-align: center;
  padding: 3rem 0;
}

.custom-product-grid__empty-title {
  margin-bottom: 1rem;
}

.custom-product-grid__empty-text {
  color: var(--product-primary);
}